"""
Fix MT5 authorization issue by using portable mode
"""
import os
import MetaTrader5 as mt5
import time

print("Fixing MT5 authorization issue...")
print("\nThis script will:")
print("1. Close any running MT5 instances")
print("2. Start MT5 in portable mode")
print("3. Test the connection")

# First, let's find where MT5 is installed
mt5_paths = [
    r"C:\Program Files\MetaTrader 5\terminal64.exe",
    r"C:\Program Files (x86)\MetaTrader 5\terminal64.exe",
    r"C:\Program Files\MetaQuotes\Terminal\terminal64.exe",
]

mt5_exe = None
for path in mt5_paths:
    if os.path.exists(path):
        mt5_exe = path
        print(f"\nFound MT5 at: {path}")
        break

if not mt5_exe:
    print("\nERROR: Could not find MT5 installation!")
    print("Please install MetaTrader 5 or update the path in this script.")
    exit(1)

# Close any running MT5
print("\nClosing any running MT5 instances...")
os.system("taskkill /F /IM terminal64.exe 2>nul")
time.sleep(2)

# Try to initialize with portable mode
print("\nTrying to initialize MT5 in portable mode...")

# Method 1: Direct initialization with path and portable flag
result = mt5.initialize(
    path=mt5_exe,
    portable=True  # This tells MT5 to run in portable mode
)

if result:
    print("SUCCESS! Connected to MT5 in portable mode")
    
    # Get account info
    account_info = mt5.account_info()
    if account_info:
        print(f"\nAccount Info:")
        print(f"Login: {account_info.login}")
        print(f"Server: {account_info.server}")
        print(f"Balance: {account_info.balance}")
    else:
        print("\nConnected but no account logged in.")
        print("You'll need to log in manually when MT5 opens.")
else:
    error = mt5.last_error()
    print(f"Failed: {error}")
    
    # Method 2: Try without portable flag
    print("\nTrying standard initialization...")
    result = mt5.initialize(path=mt5_exe)
    
    if result:
        print("SUCCESS! Connected with standard mode")
    else:
        print(f"Failed: {mt5.last_error()}")
        
        # Method 3: Let Windows handle it
        print("\nTrying to let MT5 start on its own...")
        mt5.shutdown()
        time.sleep(1)
        
        result = mt5.initialize()
        if result:
            print("SUCCESS! Connected after MT5 self-start")
        else:
            print(f"Failed: {mt5.last_error()}")

if result:
    print("\n✓ MT5 connection is working!")
    print("\nTo fix the main trading system:")
    print("1. Make sure MT5 stays open")
    print("2. Run trading_loop.py again")
else:
    print("\n✗ Could not establish MT5 connection")
    print("\nTroubleshooting steps:")
    print("1. Run this script as Administrator")
    print("2. Temporarily disable antivirus/firewall")
    print("3. Reinstall MT5")
    print("4. Check Windows Event Viewer for errors")

# Keep connection alive for testing
if result:
    print("\nKeeping MT5 connection alive. Press Ctrl+C to exit...")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nShutting down...")

mt5.shutdown()
print("Done.")