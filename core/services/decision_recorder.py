"""
Decision Recording Service for ML Training Data Collection

This service records all council decisions with full context for building
comprehensive training datasets for ML/LLM simulators.
"""

import json
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import asdict
import uuid
import hashlib

from core.domain.models import MarketData, TradingSignal, Trade, SignalType
from core.domain.enums.trading_enums import TrendDirection
from core.services.decision_types import (
    AgentAnalysis, MarketSnapshot, DecisionContext, DecisionOutcome
)
from core.infrastructure.database.decision_repository import DecisionRepository
from core.utils.structured_logger import get_logger

logger = get_logger(__name__)


class DecisionRecorder:
    """Records and manages trading decisions for ML training"""
    
    def __init__(self, repository: DecisionRepository):
        self.repository = repository
        self.current_session_id = str(uuid.uuid4())
        self._decision_buffer: Dict[str, DecisionContext] = {}
        self._start_time = datetime.now()
        
    async def start_new_session(self, mode: str = "backtest") -> str:
        """Start a new recording session"""
        self.current_session_id = str(uuid.uuid4())
        self._start_time = datetime.now()
        
        await self.repository.create_session(
            session_id=self.current_session_id,
            mode=mode,
            start_time=self._start_time
        )
        
        logger.info(f"Started new decision recording session: {self.current_session_id}")
        return self.current_session_id
    
    def create_market_snapshot(
        self,
        symbol: str,
        market_data: MarketData,
        h1_data: Dict[str, Any],
        h4_data: Dict[str, Any],
        news_data: Optional[Dict[str, Any]] = None
    ) -> MarketSnapshot:
        """Create a complete market snapshot"""
        return MarketSnapshot(
            symbol=symbol,
            current_price=market_data.current_price,
            h1_data=h1_data,
            h4_data=h4_data,
            recent_volatility=market_data.volatility_24h,
            spread=market_data.spread,
            volume=market_data.volume_24h,
            technical_indicators={
                'rsi': market_data.rsi,
                'macd': market_data.macd,
                'ma_20': market_data.ma_20,
                'ma_50': market_data.ma_50,
                'atr': market_data.atr,
                'support': market_data.support_level,
                'resistance': market_data.resistance_level
            },
            news_sentiment=news_data,
            timestamp=datetime.now()
        )
    
    async def record_decision(
        self,
        market_snapshot: MarketSnapshot,
        agent_analyses: List[AgentAnalysis],
        debate_rounds: List[Dict[str, Any]],
        final_consensus: Dict[str, Any],
        signal: Optional[TradingSignal],
        ml_prediction: Optional[Dict[str, Any]],
        api_metrics: Dict[str, Any],
        decision_time_ms: int
    ) -> str:
        """Record a complete trading decision"""
        decision_id = self._generate_decision_id(market_snapshot, final_consensus)
        
        decision_context = DecisionContext(
            decision_id=decision_id,
            session_id=self.current_session_id,
            market_snapshot=market_snapshot,
            agent_analyses=agent_analyses,
            debate_rounds=debate_rounds,
            final_consensus=final_consensus,
            signal_generated=signal,
            confidence_score=final_consensus.get('confidence', 0.0),
            ml_prediction=ml_prediction,
            api_tokens_used=api_metrics.get('tokens_used', 0),
            api_cost_usd=api_metrics.get('cost_usd', 0.0),
            decision_time_ms=decision_time_ms,
            timestamp=datetime.now()
        )
        
        # Store in buffer for potential updates
        self._decision_buffer[decision_id] = decision_context
        
        # Save to database
        await self.repository.save_decision(decision_context)
        
        logger.info(f"Recorded decision {decision_id} for {market_snapshot.symbol}")
        return decision_id
    
    async def record_agent_analysis(
        self,
        agent_name: str,
        analysis: str,
        signal_type: Optional[SignalType],
        confidence: float,
        key_factors: List[str]
    ) -> AgentAnalysis:
        """Record an individual agent's analysis"""
        return AgentAnalysis(
            agent_name=agent_name,
            analysis=analysis,
            signal_type=signal_type,
            confidence=confidence,
            key_factors=key_factors,
            timestamp=datetime.now()
        )
    
    async def link_trade_outcome(
        self,
        decision_id: str,
        trade: Trade,
        exit_reason: str
    ) -> None:
        """Link a trade outcome to a decision"""
        if not trade.exit_price:
            logger.warning(f"Trade {trade.id} has no exit price, skipping outcome recording")
            return
            
        outcome = DecisionOutcome(
            decision_id=decision_id,
            trade_id=trade.id,
            signal_executed=True,
            entry_price=trade.entry_price,
            exit_price=trade.exit_price,
            pnl=trade.pnl,
            pnl_percentage=trade.pnl_percentage,
            holding_time_minutes=int((trade.exit_time - trade.entry_time).total_seconds() / 60) if trade.exit_time else None,
            max_drawdown=trade.max_drawdown,
            max_profit=trade.max_profit,
            exit_reason=exit_reason,
            timestamp=datetime.now()
        )
        
        await self.repository.save_outcome(outcome)
        
        # Remove from buffer
        self._decision_buffer.pop(decision_id, None)
        
        logger.info(f"Linked trade outcome to decision {decision_id}: PnL={trade.pnl:.2f}")
    
    async def record_no_trade_outcome(
        self,
        decision_id: str,
        reason: str
    ) -> None:
        """Record when a signal was not executed"""
        outcome = DecisionOutcome(
            decision_id=decision_id,
            trade_id=None,
            signal_executed=False,
            entry_price=None,
            exit_price=None,
            pnl=None,
            pnl_percentage=None,
            holding_time_minutes=None,
            max_drawdown=None,
            max_profit=None,
            exit_reason=reason,
            timestamp=datetime.now()
        )
        
        await self.repository.save_outcome(outcome)
        logger.info(f"Recorded no-trade outcome for decision {decision_id}: {reason}")
    
    async def get_decision_statistics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get statistics on recorded decisions"""
        stats = await self.repository.get_statistics(start_date, end_date)
        
        # Calculate additional metrics
        if stats['total_decisions'] > 0:
            stats['avg_confidence'] = stats['total_confidence'] / stats['total_decisions']
            stats['execution_rate'] = stats['executed_signals'] / stats['total_decisions']
            
            if stats['executed_signals'] > 0:
                stats['win_rate'] = stats['profitable_trades'] / stats['executed_signals']
                stats['avg_pnl'] = stats['total_pnl'] / stats['executed_signals']
        
        return stats
    
    async def export_for_training(
        self,
        output_path: str,
        min_confidence: float = 0.0,
        only_with_outcomes: bool = False
    ) -> Tuple[int, str]:
        """Export decisions for ML training"""
        decisions = await self.repository.get_decisions_for_export(
            min_confidence=min_confidence,
            only_with_outcomes=only_with_outcomes
        )
        
        # Convert to training format
        training_data = []
        for decision in decisions:
            training_record = {
                'decision_id': decision.decision_id,
                'timestamp': decision.timestamp.isoformat(),
                'symbol': decision.market_snapshot.symbol,
                'market_state': {
                    'price': decision.market_snapshot.current_price,
                    'indicators': decision.market_snapshot.technical_indicators,
                    'volatility': decision.market_snapshot.recent_volatility,
                    'spread': decision.market_snapshot.spread
                },
                'agent_analyses': [
                    {
                        'agent': analysis.agent_name,
                        'signal': analysis.signal_type.value if analysis.signal_type else None,
                        'confidence': analysis.confidence,
                        'factors': analysis.key_factors
                    }
                    for analysis in decision.agent_analyses
                ],
                'final_decision': {
                    'signal': decision.signal_generated.signal_type.value if decision.signal_generated else 'HOLD',
                    'confidence': decision.confidence_score
                },
                'outcome': None  # Will be filled if outcome exists
            }
            
            # Add outcome if available
            outcome = await self.repository.get_outcome_for_decision(decision.decision_id)
            if outcome:
                training_record['outcome'] = {
                    'executed': outcome.signal_executed,
                    'pnl': outcome.pnl,
                    'pnl_percentage': outcome.pnl_percentage,
                    'holding_time': outcome.holding_time_minutes,
                    'exit_reason': outcome.exit_reason
                }
            
            training_data.append(training_record)
        
        # Save to file
        with open(output_path, 'w') as f:
            json.dump(training_data, f, indent=2)
        
        logger.info(f"Exported {len(training_data)} decisions to {output_path}")
        return len(training_data), output_path
    
    async def replay_decision(self, decision_id: str) -> Optional[DecisionContext]:
        """Replay a historical decision for analysis"""
        decision = await self.repository.get_decision_by_id(decision_id)
        if decision:
            logger.info(f"Replaying decision {decision_id} from {decision.timestamp}")
        return decision
    
    async def get_similar_decisions(
        self,
        market_snapshot: MarketSnapshot,
        limit: int = 10
    ) -> List[Tuple[DecisionContext, DecisionOutcome]]:
        """Find similar historical decisions"""
        return await self.repository.find_similar_decisions(
            symbol=market_snapshot.symbol,
            price_range=(market_snapshot.current_price * 0.98, market_snapshot.current_price * 1.02),
            volatility_range=(market_snapshot.recent_volatility * 0.8, market_snapshot.recent_volatility * 1.2),
            limit=limit
        )
    
    def _generate_decision_id(self, snapshot: MarketSnapshot, consensus: Dict[str, Any]) -> str:
        """Generate unique decision ID"""
        content = f"{snapshot.symbol}_{snapshot.timestamp}_{consensus}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
    
    async def cleanup_old_decisions(self, days_to_keep: int = 90) -> int:
        """Clean up old decisions to manage storage"""
        cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        cutoff_date = cutoff_date.replace(day=cutoff_date.day - days_to_keep)
        
        deleted = await self.repository.delete_decisions_before(cutoff_date)
        logger.info(f"Cleaned up {deleted} decisions older than {days_to_keep} days")
        return deleted