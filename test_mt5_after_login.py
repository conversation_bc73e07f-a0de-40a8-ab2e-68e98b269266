"""
Quick test to verify MT5 connection after manual login
"""
import MetaTrader5 as mt5

print("Testing MT5 connection after manual login...")

if mt5.initialize():
    print("✓ SUCCESS! Connected to MT5")
    
    # Get account info
    account = mt5.account_info()
    if account:
        print(f"\nAccount Details:")
        print(f"  Login: {account.login}")
        print(f"  Server: {account.server}")
        print(f"  Balance: {account.balance} {account.currency}")
        print(f"  Leverage: 1:{account.leverage}")
        print(f"  Trade allowed: {account.trade_allowed}")
        
    # Get some symbols
    symbols = mt5.symbols_get()
    if symbols:
        print(f"\nAvailable symbols: {len(symbols)}")
        print("First 5 symbols:")
        for i, s in enumerate(symbols[:5]):
            print(f"  {s.name}")
            
    print("\n✓ MT5 is ready for trading!")
    
else:
    error = mt5.last_error()
    print(f"✗ Failed to connect: {error}")
    print("\nMake sure:")
    print("1. MT5 is running")
    print("2. You are logged into an account")
    print("3. The login was successful")

mt5.shutdown()
print("\nTest complete.")