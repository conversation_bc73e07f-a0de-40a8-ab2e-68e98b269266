"""
Optimize settings for demo/testing to avoid rate limits
"""
import os

print("Optimizing settings for demo/testing...")
print("\nThis will adjust settings to:")
print("- Increase delays between API calls")
print("- Use fewer symbols initially")
print("- Enable more aggressive caching")

# Create a demo .env settings file
demo_settings = """# Demo/Testing Settings - Optimized to avoid rate limits

# Copy your existing settings here
OPENAI_API_KEY=your_key_here
MT5_FILES_DIR=your_mt5_path_here

# Optimized settings for demo
TRADING_SYMBOLS=EURUSD,GBPUSD  # Start with just 2 symbols
SYMBOL_PROCESSING_DELAY=5.0  # 5 seconds between symbols
TRADING_COUNCIL_AGENT_DELAY=2.0  # 2 seconds between agents
RATE_LIMIT_SAFETY_MARGIN=0.5  # Use only 50% of rate limit

# Enhanced caching
CACHE_ENABLED=true
CACHE_TTL_MINUTES=120  # 2 hour cache
CACHE_SIMILARITY_THRESHOLD=0.75  # More aggressive caching

# Quick mode for faster processing
TRADING_COUNCIL_QUICK_MODE=true
TRADING_COUNCIL_DEBATE_ROUNDS=1

# Lower confidence threshold for testing
TRADING_COUNCIL_MIN_CONFIDENCE=40.0

# Disable some features initially
NEWS_ENABLED=false  # Disable news to reduce API calls
ML_ENABLED=false  # Disable ML initially
"""

with open('.env.demo', 'w') as f:
    f.write(demo_settings)

print("\n✓ Created .env.demo file")
print("\nTo use these settings:")
print("1. Copy your OPENAI_API_KEY and MT5_FILES_DIR from .env to .env.demo")
print("2. Rename .env to .env.backup")
print("3. Rename .env.demo to .env")
print("4. Run the trading system")
print("\nThese settings will:")
print("- Process only 2 symbols (EURUSD, GBPUSD)")
print("- Add 5 second delays between symbols")
print("- Use aggressive caching to reduce API calls")
print("- Disable news updates temporarily")

print("\nOnce it's working well, you can gradually:")
print("- Add more symbols")
print("- Reduce delays")
print("- Enable news and ML features")