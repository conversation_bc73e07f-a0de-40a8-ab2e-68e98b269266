"""
Data provider interface for market data access.
"""

from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any
from datetime import datetime
from core.domain.models import MarketData, Candle
from core.domain.enums.mt5_enums import TimeFrame


class IDataProvider(ABC):
    """Interface for market data providers"""
    
    @abstractmethod
    async def get_market_data(
        self,
        symbol: str,
        timeframe: TimeFrame,
        bars: int = 100
    ) -> MarketData:
        """
        Get market data for a symbol.
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe for the data
            bars: Number of bars to retrieve
            
        Returns:
            MarketData object with candles and indicators
        """
        pass
    
    @abstractmethod
    async def get_latest_price(self, symbol: str) -> Dict[str, float]:
        """
        Get latest price information for a symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Dictionary with bid, ask, and last prices
        """
        pass
    
    @abstractmethod
    async def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get symbol specifications.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Symbol information or None if not found
        """
        pass
    
    @abstractmethod
    async def get_historical_data(
        self,
        symbol: str,
        timeframe: TimeFrame,
        start_date: datetime,
        end_date: datetime
    ) -> List[Candle]:
        """
        Get historical data for a date range.
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe for the data
            start_date: Start date for historical data
            end_date: End date for historical data
            
        Returns:
            List of candles
        """
        pass
    
    @abstractmethod
    async def subscribe_to_symbol(
        self,
        symbol: str,
        callback: callable
    ) -> bool:
        """
        Subscribe to real-time updates for a symbol.
        
        Args:
            symbol: Trading symbol
            callback: Function to call on price updates
            
        Returns:
            True if subscription successful
        """
        pass
    
    @abstractmethod
    async def unsubscribe_from_symbol(self, symbol: str) -> bool:
        """
        Unsubscribe from real-time updates.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            True if unsubscription successful
        """
        pass