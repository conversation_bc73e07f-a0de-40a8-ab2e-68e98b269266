"""
Domain models and data structures for the GPT Trading System.
These models represent the core business entities and value objects.
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timezone
import json


class SignalType(Enum):
    """Trading signal types"""
    BUY = "BUY"
    SELL = "SELL"
    WAIT = "WAIT"


class RiskClass(Enum):
    """Risk classification for trades"""
    A = "A"  # High confidence
    B = "B"  # Medium confidence  
    C = "C"  # Low confidence


class TradeStatus(Enum):
    """Trade status types"""
    IDLE = "idle"
    OPEN = "open"
    CLOSED = "closed"
    PENDING = "pending"
    CANCELLED = "cancelled"


class TradeResult(Enum):
    """Trade outcome types"""
    WIN = "win"
    LOSS = "loss"
    BREAKEVEN = "breakeven"
    TIMEOUT_CLOSE = "timeout_close"
    GPT_CLOSE = "GPT_close"
    MANUAL_CLOSE = "manual_close"


class ManagementDecision(Enum):
    """Trade management decisions"""
    HOLD = "HOLD"
    MOVE_SL = "MOVE_SL"
    MOVE_TP = "MOVE_TP"
    CLOSE_NOW = "CLOSE_NOW"
    SCALE_IN = "SCALE_IN"


class MarketSession(Enum):
    """Trading session types"""
    ASIA = "Asia"
    EUROPE = "Europe"
    NEW_YORK = "New York"
    OVERLAP = "Overlap"


class VolatilityLevel(Enum):
    """Market volatility levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


@dataclass
class Candle:
    """Individual candle/bar data"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    spread: Optional[float] = None
    
    # Technical indicators
    ema50: Optional[float] = None
    ema200: Optional[float] = None
    rsi14: Optional[float] = None
    atr14: Optional[float] = None
    rsi_slope: Optional[float] = None
    
    # Additional indicators for position trading
    ema20: Optional[float] = None
    sma50: Optional[float] = None
    sma200: Optional[float] = None
    true_range: Optional[float] = None
    atr_percentage: Optional[float] = None
    volume_ratio: Optional[float] = None
    body_size: Optional[float] = None
    upper_shadow: Optional[float] = None
    lower_shadow: Optional[float] = None
    weekly_range: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume,
            'spread': self.spread,
            'ema50': self.ema50,
            'ema200': self.ema200,
            'rsi14': self.rsi14,
            'atr14': self.atr14,
            'rsi_slope': self.rsi_slope,
            'ema20': self.ema20,
            'sma50': self.sma50,
            'sma200': self.sma200,
            'true_range': self.true_range,
            'atr_percentage': self.atr_percentage,
            'volume_ratio': self.volume_ratio,
            'body_size': self.body_size,
            'upper_shadow': self.upper_shadow,
            'lower_shadow': self.lower_shadow,
            'weekly_range': self.weekly_range
        }


@dataclass
class MarketData:
    """Market data for a symbol and timeframe"""
    symbol: str
    timeframe: str
    candles: List[Candle]
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    @property
    def latest_candle(self) -> Optional[Candle]:
        """Get the most recent candle"""
        return self.candles[-1] if self.candles else None
    
    @property
    def previous_candle(self) -> Optional[Candle]:
        """Get the second most recent candle"""
        return self.candles[-2] if len(self.candles) >= 2 else None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'candles': [candle.to_dict() for candle in self.candles],
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class MarketConditions:
    """Current market conditions for a symbol"""
    # Required fields first
    symbol: str
    current_price: float
    bid: float
    ask: float
    spread: float
    is_open: bool
    session: str  # "asian", "european", "american", "overlap", "closed"
    liquidity: str  # "high", "medium", "low"
    trend_h1: str  # "bullish", "bearish", "sideways"
    trend_h4: str  # "bullish", "bearish", "sideways"
    trend_d1: str  # "bullish", "bearish", "sideways"
    volatility: float  # ATR-based volatility
    volatility_rank: str  # "low", "medium", "high", "extreme"
    daily_high: float
    daily_low: float
    
    # Fields with defaults
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    support_levels: List[float] = field(default_factory=list)
    resistance_levels: List[float] = field(default_factory=list)
    pivot_point: Optional[float] = None
    rsi: Optional[float] = None
    momentum: Optional[str] = None  # "overbought", "oversold", "neutral"
    volume_24h: Optional[float] = None
    average_spread: Optional[float] = None
    tick_value: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'current_price': self.current_price,
            'bid': self.bid,
            'ask': self.ask,
            'spread': self.spread,
            'is_open': self.is_open,
            'session': self.session,
            'liquidity': self.liquidity,
            'trend_h1': self.trend_h1,
            'trend_h4': self.trend_h4,
            'trend_d1': self.trend_d1,
            'volatility': self.volatility,
            'volatility_rank': self.volatility_rank,
            'daily_high': self.daily_high,
            'daily_low': self.daily_low,
            'support_levels': self.support_levels,
            'resistance_levels': self.resistance_levels,
            'pivot_point': self.pivot_point,
            'rsi': self.rsi,
            'momentum': self.momentum,
            'volume_24h': self.volume_24h,
            'average_spread': self.average_spread,
            'tick_value': self.tick_value
        }


@dataclass
class NewsEvent:
    """Economic news event"""
    timestamp: datetime
    country: str
    title: str
    impact: str  # "low", "medium", "high"
    actual: Optional[str] = None
    forecast: Optional[str] = None
    previous: Optional[str] = None
    
    @property
    def is_high_impact(self) -> bool:
        """Check if this is a high impact event"""
        return self.impact.lower() == "high"


@dataclass
class TradingSignal:
    """Trading signal generated by GPT"""
    symbol: str
    signal: SignalType
    reason: str
    risk_class: RiskClass
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Trade parameters (None for WAIT signals)
    entry: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    risk_reward: Optional[float] = None
    
    # Context data
    market_context: Optional[Dict[str, Any]] = None
    news_events: List[NewsEvent] = field(default_factory=list)
    
    @property
    def is_actionable(self) -> bool:
        """Check if signal is actionable (not WAIT)"""
        return self.signal != SignalType.WAIT
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'symbol': self.symbol,
            'signal': self.signal.value,
            'entry': self.entry,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'risk_reward': self.risk_reward,
            'risk_class': self.risk_class.value,
            'reason': self.reason,
            'timestamp': self.timestamp.isoformat(),
            'market_context': self.market_context,
            'news_events': [
                {
                    'timestamp': event.timestamp.isoformat(),
                    'country': event.country,
                    'title': event.title,
                    'impact': event.impact
                } for event in self.news_events
            ]
        }


@dataclass
class TradeUpdate:
    """Trade update information for modifications and closures"""
    trade_id: str
    symbol: str
    action: str  # 'close', 'modify_sl', 'modify_tp', etc.
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Closing information
    exit_price: Optional[float] = None
    exit_reason: Optional[str] = None
    final_pnl: Optional[float] = None
    final_pnl_pips: Optional[float] = None
    
    # Modification information
    new_stop_loss: Optional[float] = None
    new_take_profit: Optional[float] = None
    previous_stop_loss: Optional[float] = None
    previous_take_profit: Optional[float] = None
    
    # Additional context
    market_price: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'trade_id': self.trade_id,
            'symbol': self.symbol,
            'action': self.action,
            'timestamp': self.timestamp.isoformat(),
            'exit_price': self.exit_price,
            'exit_reason': self.exit_reason,
            'final_pnl': self.final_pnl,
            'final_pnl_pips': self.final_pnl_pips,
            'new_stop_loss': self.new_stop_loss,
            'new_take_profit': self.new_take_profit,
            'previous_stop_loss': self.previous_stop_loss,
            'previous_take_profit': self.previous_take_profit,
            'market_price': self.market_price,
            'success': self.success,
            'error_message': self.error_message,
            'details': self.details
        }


@dataclass
class Trade:
    """Active or completed trade"""
    id: str
    symbol: str
    side: SignalType  # BUY or SELL
    entry_price: float
    stop_loss: float
    take_profit: float
    status: TradeStatus
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # MT5 specific
    ticket: Optional[int] = None
    lot_size: Optional[float] = None
    
    # Trade tracking
    max_drawdown_pips: float = 0.0
    current_pnl: float = 0.0
    exit_price: Optional[float] = None
    exit_timestamp: Optional[datetime] = None
    result: Optional[TradeResult] = None
    
    # Risk management
    risk_reward_ratio: Optional[float] = None
    risk_amount_usd: Optional[float] = None
    
    # Context
    original_signal: Optional[TradingSignal] = None
    management_history: List[Dict[str, Any]] = field(default_factory=list)
    reflection: Optional[str] = None
    
    @property
    def is_open(self) -> bool:
        """Check if trade is currently open"""
        return self.status == TradeStatus.OPEN
    
    @property
    def duration_minutes(self) -> Optional[float]:
        """Get trade duration in minutes"""
        if self.exit_timestamp:
            return (self.exit_timestamp - self.timestamp).total_seconds() / 60
        elif self.is_open:
            return (datetime.now(timezone.utc) - self.timestamp).total_seconds() / 60
        return None
    
    def add_management_action(self, action: ManagementDecision, details: Dict[str, Any]):
        """Add a management action to history"""
        self.management_history.append({
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'action': action.value,
            'details': details
        })
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'symbol': self.symbol,
            'side': self.side.value,
            'entry_price': self.entry_price,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'status': self.status.value,
            'timestamp': self.timestamp.isoformat(),
            'ticket': self.ticket,
            'lot_size': self.lot_size,
            'max_drawdown_pips': self.max_drawdown_pips,
            'current_pnl': self.current_pnl,
            'exit_price': self.exit_price,
            'exit_timestamp': self.exit_timestamp.isoformat() if self.exit_timestamp else None,
            'result': self.result.value if self.result else None,
            'risk_reward_ratio': self.risk_reward_ratio,
            'risk_amount_usd': self.risk_amount_usd,
            'original_signal': self.original_signal.to_dict() if self.original_signal else None,
            'management_history': self.management_history,
            'reflection': self.reflection
        }


@dataclass
class TradeManagementDecision:
    """Decision made by trade management system"""
    decision: ManagementDecision
    reason: str
    risk_class: RiskClass
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Optional parameters for specific decisions
    new_stop_loss: Optional[float] = None
    new_take_profit: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'decision': self.decision.value,
            'reason': self.reason,
            'risk_class': self.risk_class.value,
            'timestamp': self.timestamp.isoformat(),
            'new_stop_loss': self.new_stop_loss,
            'new_take_profit': self.new_take_profit
        }


@dataclass
class MarketContext:
    """Current market context information"""
    session: MarketSession
    volatility: VolatilityLevel
    win_streak_type: str  # "win" or "loss"
    win_streak_length: int
    win_rate: float
    sample_size: int
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'session': self.session.value,
            'volatility': self.volatility.value,
            'win_streak_type': self.win_streak_type,
            'win_streak_length': self.win_streak_length,
            'win_rate': self.win_rate,
            'sample_size': self.sample_size,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class TradeCase:
    """Historical trade case for RAG memory system"""
    id: str
    symbol: str
    context: str  # Text description of market conditions
    signal: SignalType
    entry_price: float
    risk_reward: float
    result: TradeResult
    reason: str
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'symbol': self.symbol,
            'context': self.context,
            'signal': self.signal.value,
            'entry_price': self.entry_price,
            'risk_reward': self.risk_reward,
            'result': self.result.value,
            'reason': self.reason,
            'timestamp': self.timestamp.isoformat()
        }


# Type aliases for common use cases
TradingData = Dict[str, Union[MarketData, List[NewsEvent], MarketContext]]
SignalResponse = Dict[str, Any]  # JSON response from GPT
ManagementResponse = Dict[str, Any]  # JSON response from GPT trade manager


# Utility functions for model creation
def create_trade_id(symbol: str, timestamp: datetime) -> str:
    """Create a unique trade ID"""
    return f"{symbol}_{timestamp.strftime('%Y%m%d_%H%M%S')}"


def create_case_id(symbol: str, timestamp: datetime, entry: float) -> str:
    """Create a unique case ID for RAG memory"""
    return f"{symbol}_{timestamp.strftime('%Y%m%d_%H%M%S')}_{entry}"


# Export all models
__all__ = [
    # Enums
    'SignalType', 'RiskClass', 'TradeStatus', 'TradeResult', 
    'ManagementDecision', 'MarketSession', 'VolatilityLevel',
    
    # Data models
    'Candle', 'MarketData', 'NewsEvent', 'TradingSignal', 
    'Trade', 'TradeManagementDecision', 'MarketContext', 'TradeCase',
    
    # Type aliases
    'TradingData', 'SignalResponse', 'ManagementResponse',
    
    # Utility functions
    'create_trade_id', 'create_case_id'
]