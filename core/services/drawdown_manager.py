"""
Drawdown Manager Service
Enforces strict risk limits in live trading
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Optional, Tuple, List
from dataclasses import dataclass
from pathlib import Path

from config.settings import get_settings
from config.risk_management_config import get_risk_limits, DrawdownTracker
from core.domain.models import Trade, TradingSignal
from core.infrastructure.database.repositories import TradeRepository

logger = logging.getLogger(__name__)


@dataclass
class DrawdownStatus:
    """Current drawdown status"""
    current_balance: float
    starting_balance: float
    daily_starting_balance: float
    max_balance: float
    current_drawdown_percent: float
    daily_drawdown_percent: float
    consecutive_losses: int
    daily_trades: int
    can_trade: bool
    reason: str


class DrawdownManager:
    """Manages drawdown limits and risk controls"""
    
    def __init__(self, trade_repository: TradeRepository, initial_balance: float = 10000):
        self.settings = get_settings()
        self.trade_repository = trade_repository
        self.risk_limits = get_risk_limits(self.settings.trading.risk_profile)
        
        # Initialize tracker
        self.tracker = DrawdownTracker(
            starting_balance=initial_balance,
            current_balance=initial_balance,
            daily_starting_balance=initial_balance,
            max_balance=initial_balance
        )
        
        # Load state from database
        self._load_state_from_trades()
        
        logger.info(f"DrawdownManager initialized with {self.settings.trading.risk_profile} profile")
        logger.info(f"Risk limits: Max daily DD: {self.risk_limits.max_daily_drawdown_percent}%, "
                   f"Max total DD: {self.risk_limits.max_total_drawdown_percent}%")
    
    def _load_state_from_trades(self):
        """Load current state from trade history"""
        try:
            # Get all trades
            trades = self.trade_repository.get_all()
            
            if trades:
                # Calculate current balance from trades
                total_pnl = sum(trade.profit for trade in trades if trade.profit is not None)
                self.tracker.current_balance = self.tracker.starting_balance + total_pnl
                
                # Track max balance
                running_balance = self.tracker.starting_balance
                for trade in sorted(trades, key=lambda t: t.entry_time):
                    if trade.profit is not None:
                        running_balance += trade.profit
                        if running_balance > self.tracker.max_balance:
                            self.tracker.max_balance = running_balance
                
                # Get today's trades
                today = datetime.now(timezone.utc).date()
                today_trades = [t for t in trades if t.entry_time.date() == today]
                
                if today_trades:
                    # Calculate daily starting balance
                    yesterday_trades = [t for t in trades if t.entry_time.date() < today]
                    yesterday_pnl = sum(t.profit for t in yesterday_trades if t.profit is not None)
                    self.tracker.daily_starting_balance = self.tracker.starting_balance + yesterday_pnl
                    
                    # Count daily trades
                    self.tracker.daily_trades = len(today_trades)
                    
                    # Track symbol trades
                    for trade in today_trades:
                        self.tracker.symbol_trades[trade.symbol] = self.tracker.symbol_trades.get(trade.symbol, 0) + 1
                
                # Count consecutive losses
                recent_trades = sorted([t for t in trades if t.exit_time is not None], 
                                     key=lambda t: t.exit_time, reverse=True)[:10]
                
                consecutive_losses = 0
                for trade in recent_trades:
                    if trade.profit is not None and trade.profit < 0:
                        consecutive_losses += 1
                    else:
                        break
                self.tracker.consecutive_losses = consecutive_losses
                
                logger.info(f"Loaded state: Balance ${self.tracker.current_balance:.2f}, "
                           f"Daily trades: {self.tracker.daily_trades}, "
                           f"Consecutive losses: {self.tracker.consecutive_losses}")
                
        except Exception as e:
            logger.error(f"Error loading state from trades: {e}")
    
    def can_take_trade(self, symbol: str, signal: TradingSignal) -> Tuple[bool, str]:
        """
        Check if a new trade is allowed based on risk limits
        
        Returns:
            (allowed, reason)
        """
        # Check basic trading permission
        can_trade, reason = self.tracker.can_trade(symbol, self.risk_limits)
        if not can_trade:
            logger.warning(f"Trade blocked for {symbol}: {reason}")
            return False, reason
        
        # Check signal confidence
        if signal.confidence < self.risk_limits.min_confidence_to_trade:
            reason = f"Confidence {signal.confidence}% below minimum {self.risk_limits.min_confidence_to_trade}%"
            logger.warning(f"Trade blocked for {symbol}: {reason}")
            return False, reason
        
        # Check risk/reward ratio
        if signal.stop_loss and signal.take_profit:
            rr_ratio = abs(signal.take_profit - signal.entry_price) / abs(signal.entry_price - signal.stop_loss)
            if rr_ratio < self.risk_limits.min_risk_reward_ratio:
                reason = f"R:R ratio {rr_ratio:.2f} below minimum {self.risk_limits.min_risk_reward_ratio}"
                logger.warning(f"Trade blocked for {symbol}: {reason}")
                return False, reason
        
        # Calculate position risk
        risk_amount = self.tracker.current_balance * (self.settings.trading.risk_per_trade_percent / 100)
        risk_percent = (risk_amount / self.tracker.current_balance) * 100
        
        # Validate trade risk
        allowed, reason = self.risk_limits.validate_trade_risk(
            risk_percent, 
            self.tracker.current_drawdown_percent
        )
        
        if not allowed:
            logger.warning(f"Trade blocked for {symbol}: {reason}")
            
        return allowed, reason
    
    def record_trade_result(self, trade: Trade):
        """Record trade result and update drawdown tracking"""
        if trade.profit is not None:
            self.tracker.record_trade_result(trade.symbol, trade.profit)
            
            logger.info(f"Trade recorded: {trade.symbol} P/L: ${trade.profit:.2f}")
            logger.info(f"Updated balance: ${self.tracker.current_balance:.2f}, "
                       f"Current DD: {self.tracker.current_drawdown_percent:.1f}%, "
                       f"Daily DD: {self.tracker.daily_drawdown_percent:.1f}%")
            
            # Check if emergency stop needed
            if self.tracker.current_drawdown_percent >= self.risk_limits.max_total_drawdown_percent:
                logger.critical(f"EMERGENCY STOP: Total drawdown {self.tracker.current_drawdown_percent:.1f}% "
                               f"exceeds limit {self.risk_limits.max_total_drawdown_percent}%")
            elif self.tracker.daily_drawdown_percent >= self.risk_limits.max_daily_drawdown_percent:
                logger.critical(f"DAILY LIMIT: Daily drawdown {self.tracker.daily_drawdown_percent:.1f}% "
                               f"exceeds limit {self.risk_limits.max_daily_drawdown_percent}%")
    
    def get_status(self) -> DrawdownStatus:
        """Get current drawdown status"""
        can_trade, reason = self.tracker.can_trade("", self.risk_limits)
        
        return DrawdownStatus(
            current_balance=self.tracker.current_balance,
            starting_balance=self.tracker.starting_balance,
            daily_starting_balance=self.tracker.daily_starting_balance,
            max_balance=self.tracker.max_balance,
            current_drawdown_percent=self.tracker.current_drawdown_percent,
            daily_drawdown_percent=self.tracker.daily_drawdown_percent,
            consecutive_losses=self.tracker.consecutive_losses,
            daily_trades=self.tracker.daily_trades,
            can_trade=can_trade,
            reason=reason
        )
    
    def reset_daily_limits(self):
        """Reset daily statistics (call at start of trading day)"""
        self.tracker.reset_daily_stats()
        logger.info("Daily trading limits reset")
    
    def calculate_safe_position_size(
        self, 
        symbol: str,
        stop_loss_pips: float,
        pip_value: float
    ) -> float:
        """Calculate position size based on current drawdown"""
        return self.risk_limits.calculate_position_size(
            account_balance=self.tracker.current_balance,
            stop_loss_pips=stop_loss_pips,
            pip_value=pip_value,
            current_drawdown=self.tracker.current_drawdown_percent
        )
    
    def should_use_emergency_stop(self, current_loss_percent: float) -> bool:
        """Check if emergency stop should be triggered"""
        return (
            self.settings.trading.emergency_stop_enabled and
            current_loss_percent >= self.risk_limits.emergency_stop_loss_percent
        )