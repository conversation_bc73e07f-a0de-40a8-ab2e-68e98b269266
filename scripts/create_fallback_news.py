#!/usr/bin/env python3
"""
Create fallback ForexFactory news file when the API is unavailable.
This creates a minimal news file to prevent the system from crashing.
"""

import json
import logging
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_fallback_news_file() -> bool:
    """
    Create a fallback news file with minimal data
    
    Returns:
        True if creation successful
    """
    try:
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        
        output_file = data_dir / "forexfactory_week.json"
        
        logger.info("🔧 Creating fallback ForexFactory news file...")
        
        # Create minimal events for the next few days
        now = datetime.now(timezone.utc)
        fallback_events = []
        
        # Add some generic events to prevent empty file issues
        for day in range(7):
            event_date = now + timedelta(days=day)
            
            # Add a few sample events per day
            sample_events = [
                {
                    "datetime": event_date.replace(hour=13, minute=30).isoformat(),
                    "date": event_date.strftime('%Y-%m-%d'),
                    "time": "13:30",
                    "currency": "USD",
                    "country": "US",
                    "event": "Economic Data Release",
                    "title": "Economic Data Release",
                    "impact": "medium",
                    "forecast": None,
                    "previous": None,
                    "actual": None,
                    "url": None
                },
                {
                    "datetime": event_date.replace(hour=15, minute=0).isoformat(),
                    "date": event_date.strftime('%Y-%m-%d'),
                    "time": "15:00",
                    "currency": "EUR",
                    "country": "EU",
                    "event": "ECB Economic Bulletin",
                    "title": "ECB Economic Bulletin",
                    "impact": "low",
                    "forecast": None,
                    "previous": None,
                    "actual": None,
                    "url": None
                }
            ]
            
            fallback_events.extend(sample_events)
        
        # Save as direct list (not wrapped in object)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(fallback_events, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Created fallback news file: {output_file}")
        logger.info(f"📊 Created {len(fallback_events)} fallback events")
        logger.info("⚠️  This is fallback data. Please update with real ForexFactory data when possible.")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create fallback news file: {e}")
        return False

def main():
    """Main function"""
    logger.info("🚀 Creating fallback ForexFactory news file...")
    
    success = create_fallback_news_file()
    
    if success:
        logger.info("🎉 Fallback file created successfully!")
        logger.info("💡 To get real data, run: python scripts/update_forexfactory_news.py")
    else:
        logger.error("💥 Failed to create fallback file!")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
