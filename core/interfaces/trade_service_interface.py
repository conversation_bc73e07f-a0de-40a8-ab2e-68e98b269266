"""
Trade service interface for trade management operations.
"""

from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any
from core.domain.models import Trade, TradingSignal, TradeUpdate, TradeStatus


class ITradeService(ABC):
    """Interface for trade management services"""
    
    @abstractmethod
    async def execute_signal(self, signal: TradingSignal) -> Optional[Trade]:
        """
        Execute a trading signal.
        
        Args:
            signal: Trading signal to execute
            
        Returns:
            Trade object if successful, None otherwise
        """
        pass
    
    @abstractmethod
    async def get_trade_by_symbol(self, symbol: str) -> Optional[Trade]:
        """
        Get active trade for a symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Active trade or None
        """
        pass
    
    @abstractmethod
    async def get_trade_by_id(self, trade_id: str) -> Optional[Trade]:
        """
        Get trade by its ID.
        
        Args:
            trade_id: Unique trade identifier
            
        Returns:
            Trade object or None
        """
        pass
    
    @abstractmethod
    async def manage_trade(self, trade: Trade) -> bool:
        """
        Manage an existing trade (update stops, check status).
        
        Args:
            trade: Trade to manage
            
        Returns:
            True if trade is still open
        """
        pass
    
    @abstractmethod
    async def close_trade(
        self,
        trade: Trade,
        reason: str = "Manual close"
    ) -> Optional[TradeUpdate]:
        """
        Close an open trade.
        
        Args:
            trade: Trade to close
            reason: Reason for closing
            
        Returns:
            TradeUpdate with closing details
        """
        pass
    
    @abstractmethod
    async def get_open_trades(self) -> List[Trade]:
        """
        Get all open trades.
        
        Returns:
            List of open trades
        """
        pass
    
    @abstractmethod
    async def get_trade_history(
        self,
        symbol: Optional[str] = None,
        limit: int = 100
    ) -> List[Trade]:
        """
        Get trade history.
        
        Args:
            symbol: Optional symbol filter
            limit: Maximum number of trades
            
        Returns:
            List of historical trades
        """
        pass
    
    @abstractmethod
    async def update_trade_status(
        self,
        trade_id: str,
        status: TradeStatus,
        details: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update trade status.
        
        Args:
            trade_id: Trade identifier
            status: New status
            details: Optional additional details
            
        Returns:
            True if update successful
        """
        pass
    
    @abstractmethod
    async def calculate_position_size(
        self,
        symbol: str,
        stop_loss_distance: float,
        risk_amount: float
    ) -> float:
        """
        Calculate appropriate position size.
        
        Args:
            symbol: Trading symbol
            stop_loss_distance: Distance to stop loss in points
            risk_amount: Amount willing to risk
            
        Returns:
            Position size in lots
        """
        pass