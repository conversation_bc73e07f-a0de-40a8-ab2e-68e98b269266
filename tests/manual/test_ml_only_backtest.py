#!/usr/bin/env python
"""Test ML-only backtest mode to ensure it works."""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

def test_ml_only_imports():
    """Test that ML-only mode can be imported and initialized."""
    print("Testing ML-only backtest mode...\n")
    
    try:
        # Test basic imports
        print("1. Testing basic imports...")
        from config.settings import get_settings
        from core.services.llm_simulator import LLMSimulator
        from core.services.enhanced_backtesting_service import EnhancedBacktestingService
        print("   ✓ Basic imports successful")
        
        # Test settings
        print("\n2. Testing settings...")
        settings = get_settings()
        print(f"   ✓ Settings loaded: {settings.app_name}")
        
        # Test LLM simulator initialization
        print("\n3. Testing LLM simulator...")
        simulator = LLMSimulator()
        print("   ✓ LLM simulator initialized")
        
        # Check if models directory exists
        print("\n4. Checking models directory...")
        models_dir = Path("models")
        if models_dir.exists():
            print(f"   ✓ Models directory exists: {models_dir}")
            # List available models
            model_files = list(models_dir.glob("**/*.pkl"))
            if model_files:
                print(f"   ✓ Found {len(model_files)} model files")
            else:
                print("   ⚠ No model files found (will use rule-based simulation)")
        else:
            print("   ⚠ Models directory not found (will use rule-based simulation)")
        
        print("\n✅ ML-only backtest mode is ready to use!")
        print("\nYou can now run:")
        print("  python run_backtest.py --mode ml_only --days 7")
        
        return True
        
    except ImportError as e:
        print(f"\n❌ Import error: {e}")
        print("\nPlease ensure all dependencies are installed:")
        print("  pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False


if __name__ == "__main__":
    success = test_ml_only_imports()
    sys.exit(0 if success else 1)