# Core dependencies
pydantic>=2.0.0,<3.0.0
pydantic-settings>=2.0.0
python-dotenv>=1.0.0
MetaTrader5>=5.0.45
openai>=1.0.0,<2.0.0
tiktoken>=0.5.0

# Data processing
pandas>=2.0.0,<3.0.0
numpy>=1.24.0,<2.0.0

# Machine learning
torch>=2.1.0
sentence-transformers>=2.2.0
faiss-cpu>=1.7.0
seaborn>=0.13.0
h5py>=3.10.0
scikit-learn>=1.3.0
imbalanced-learn>=0.11.0

# Charting
mplfinance>=0.12.9b7
matplotlib>=3.7.0,<4.0.0
streamlit>=1.29.0
plotly>=5.18.0
tabulate>=0.9.0

# Technical analysis
ta>=0.10.0

# Utilities
requests>=2.31.0  # Security: CVE-2023-32681
aiohttp>=3.9.0    # Security updates
schedule>=1.2.0
nest-asyncio>=1.5.8
backoff>=2.2.0

# Database
joblib>=1.3.0
aiosqlite>=0.19.0

# Other
scipy>=1.11.0
psutil>=5.9.0