"""
Simple test to check if the system runs without getting stuck
"""
import asyncio
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_timeout():
    """Test with a simple timeout"""
    print(f"Starting test at {datetime.now()}")
    
    try:
        # Wait for 5 seconds max
        await asyncio.wait_for(asyncio.sleep(60), timeout=5.0)
        print("Sleep completed normally")
    except asyncio.TimeoutError:
        print("Timeout occurred after 5 seconds (expected)")
    
    print(f"Test completed at {datetime.now()}")

if __name__ == "__main__":
    asyncio.run(test_timeout())