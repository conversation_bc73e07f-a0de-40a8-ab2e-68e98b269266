"""
Decision Repository for storing council decisions and outcomes

Provides persistence layer for the decision recording system.
"""

import json
import sqlite3
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import asdict

from core.services.decision_types import (
    DecisionContext, DecisionOutcome, MarketSnapshot, AgentAnalysis
)
from core.domain.models import SignalType
from core.utils.structured_logger import get_logger

logger = get_logger(__name__)


class DecisionRepository:
    """Repository for storing and retrieving trading decisions"""
    
    def __init__(self, db_path: str = "data/trades.db"):
        self.db_path = db_path
        self._ensure_tables()
    
    def _ensure_tables(self):
        """Ensure decision tables exist"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Recording sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS recording_sessions (
                    session_id TEXT PRIMARY KEY,
                    mode TEXT NOT NULL,
                    start_time TIMESTAMP NOT NULL,
                    end_time TIMESTAMP,
                    total_decisions INTEGER DEFAULT 0,
                    total_trades INTEGER DEFAULT 0,
                    total_pnl REAL DEFAULT 0
                )
            """)
            
            # Decisions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS council_decisions (
                    decision_id TEXT PRIMARY KEY,
                    session_id TEXT NOT NULL,
                    timestamp TIMESTAMP NOT NULL,
                    symbol TEXT NOT NULL,
                    current_price REAL NOT NULL,
                    market_snapshot TEXT NOT NULL,
                    agent_analyses TEXT NOT NULL,
                    debate_rounds TEXT NOT NULL,
                    final_consensus TEXT NOT NULL,
                    signal_type TEXT,
                    confidence_score REAL NOT NULL,
                    ml_prediction TEXT,
                    api_tokens_used INTEGER NOT NULL,
                    api_cost_usd REAL NOT NULL,
                    decision_time_ms INTEGER NOT NULL,
                    FOREIGN KEY (session_id) REFERENCES recording_sessions(session_id)
                )
            """)
            
            # Outcomes table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS decision_outcomes (
                    outcome_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    decision_id TEXT NOT NULL,
                    trade_id TEXT,
                    signal_executed BOOLEAN NOT NULL,
                    entry_price REAL,
                    exit_price REAL,
                    pnl REAL,
                    pnl_percentage REAL,
                    holding_time_minutes INTEGER,
                    max_drawdown REAL,
                    max_profit REAL,
                    exit_reason TEXT,
                    timestamp TIMESTAMP NOT NULL,
                    FOREIGN KEY (decision_id) REFERENCES council_decisions(decision_id)
                )
            """)
            
            # Indexes for performance
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_decisions_symbol_timestamp 
                ON council_decisions(symbol, timestamp)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_decisions_confidence 
                ON council_decisions(confidence_score)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_outcomes_decision 
                ON decision_outcomes(decision_id)
            """)
            
            conn.commit()
    
    async def create_session(self, session_id: str, mode: str, start_time: datetime) -> None:
        """Create a new recording session"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO recording_sessions (session_id, mode, start_time)
                VALUES (?, ?, ?)
            """, (session_id, mode, start_time))
            conn.commit()
    
    async def save_decision(self, decision: DecisionContext) -> None:
        """Save a trading decision"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Serialize complex objects
            market_snapshot_json = json.dumps(asdict(decision.market_snapshot), default=str)
            agent_analyses_json = json.dumps([asdict(a) for a in decision.agent_analyses], default=str)
            debate_rounds_json = json.dumps(decision.debate_rounds, default=str)
            final_consensus_json = json.dumps(decision.final_consensus, default=str)
            ml_prediction_json = json.dumps(decision.ml_prediction, default=str) if decision.ml_prediction else None
            
            signal_type = decision.signal_generated.signal_type.value if decision.signal_generated else None
            
            cursor.execute("""
                INSERT INTO council_decisions (
                    decision_id, session_id, timestamp, symbol, current_price,
                    market_snapshot, agent_analyses, debate_rounds, final_consensus,
                    signal_type, confidence_score, ml_prediction, api_tokens_used,
                    api_cost_usd, decision_time_ms
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                decision.decision_id, decision.session_id, decision.timestamp,
                decision.market_snapshot.symbol, decision.market_snapshot.current_price,
                market_snapshot_json, agent_analyses_json, debate_rounds_json,
                final_consensus_json, signal_type, decision.confidence_score,
                ml_prediction_json, decision.api_tokens_used, decision.api_cost_usd,
                decision.decision_time_ms
            ))
            
            # Update session stats
            cursor.execute("""
                UPDATE recording_sessions 
                SET total_decisions = total_decisions + 1
                WHERE session_id = ?
            """, (decision.session_id,))
            
            conn.commit()
    
    async def save_outcome(self, outcome: DecisionOutcome) -> None:
        """Save a decision outcome"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO decision_outcomes (
                    decision_id, trade_id, signal_executed, entry_price, exit_price,
                    pnl, pnl_percentage, holding_time_minutes, max_drawdown,
                    max_profit, exit_reason, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                outcome.decision_id, outcome.trade_id, outcome.signal_executed,
                outcome.entry_price, outcome.exit_price, outcome.pnl,
                outcome.pnl_percentage, outcome.holding_time_minutes,
                outcome.max_drawdown, outcome.max_profit, outcome.exit_reason,
                outcome.timestamp
            ))
            
            # Update session stats if trade was executed
            if outcome.signal_executed and outcome.pnl is not None:
                cursor.execute("""
                    UPDATE recording_sessions 
                    SET total_trades = total_trades + 1,
                        total_pnl = total_pnl + ?
                    WHERE session_id = (
                        SELECT session_id FROM council_decisions 
                        WHERE decision_id = ?
                    )
                """, (outcome.pnl, outcome.decision_id))
            
            conn.commit()
    
    async def get_statistics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get decision statistics"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Build date filter
            date_filter = ""
            params = []
            if start_date:
                date_filter += " AND cd.timestamp >= ?"
                params.append(start_date)
            if end_date:
                date_filter += " AND cd.timestamp <= ?"
                params.append(end_date)
            
            # Get overall stats
            cursor.execute(f"""
                SELECT 
                    COUNT(DISTINCT cd.decision_id) as total_decisions,
                    COUNT(DISTINCT cd.symbol) as unique_symbols,
                    AVG(cd.confidence_score) as avg_confidence,
                    SUM(cd.confidence_score) as total_confidence,
                    SUM(cd.api_tokens_used) as total_tokens,
                    SUM(cd.api_cost_usd) as total_cost,
                    AVG(cd.decision_time_ms) as avg_decision_time_ms
                FROM council_decisions cd
                WHERE 1=1 {date_filter}
            """, params)
            
            stats = dict(zip([desc[0] for desc in cursor.description], cursor.fetchone()))
            
            # Get outcome stats
            cursor.execute(f"""
                SELECT 
                    COUNT(DISTINCT do.decision_id) as executed_signals,
                    COUNT(CASE WHEN do.pnl > 0 THEN 1 END) as profitable_trades,
                    COUNT(CASE WHEN do.pnl < 0 THEN 1 END) as losing_trades,
                    SUM(do.pnl) as total_pnl,
                    AVG(do.pnl) as avg_pnl,
                    AVG(do.pnl_percentage) as avg_pnl_percentage,
                    AVG(do.holding_time_minutes) as avg_holding_time
                FROM decision_outcomes do
                JOIN council_decisions cd ON do.decision_id = cd.decision_id
                WHERE do.signal_executed = 1 {date_filter}
            """, params)
            
            outcome_stats = dict(zip([desc[0] for desc in cursor.description], cursor.fetchone()))
            stats.update(outcome_stats)
            
            # Get per-symbol stats
            cursor.execute(f"""
                SELECT 
                    cd.symbol,
                    COUNT(cd.decision_id) as decisions,
                    AVG(cd.confidence_score) as avg_confidence,
                    COUNT(do.decision_id) as trades,
                    SUM(CASE WHEN do.pnl > 0 THEN 1 ELSE 0 END) as wins,
                    SUM(do.pnl) as total_pnl
                FROM council_decisions cd
                LEFT JOIN decision_outcomes do ON cd.decision_id = do.decision_id
                WHERE 1=1 {date_filter}
                GROUP BY cd.symbol
            """, params)
            
            stats['per_symbol'] = [
                dict(zip([desc[0] for desc in cursor.description], row))
                for row in cursor.fetchall()
            ]
            
            return stats
    
    async def get_decision_by_id(self, decision_id: str) -> Optional[DecisionContext]:
        """Get a specific decision by ID"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM council_decisions WHERE decision_id = ?
            """, (decision_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            # Reconstruct DecisionContext
            return self._row_to_decision(row, cursor.description)
    
    async def get_decisions_for_export(
        self,
        min_confidence: float = 0.0,
        only_with_outcomes: bool = False
    ) -> List[DecisionContext]:
        """Get decisions for ML training export"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            query = """
                SELECT cd.* FROM council_decisions cd
                WHERE cd.confidence_score >= ?
            """
            
            if only_with_outcomes:
                query += " AND EXISTS (SELECT 1 FROM decision_outcomes do WHERE do.decision_id = cd.decision_id)"
            
            query += " ORDER BY cd.timestamp DESC"
            
            cursor.execute(query, (min_confidence,))
            
            decisions = []
            for row in cursor.fetchall():
                decision = self._row_to_decision(row, cursor.description)
                if decision:
                    decisions.append(decision)
            
            return decisions
    
    async def get_outcome_for_decision(self, decision_id: str) -> Optional[DecisionOutcome]:
        """Get outcome for a specific decision"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM decision_outcomes WHERE decision_id = ? ORDER BY timestamp DESC LIMIT 1
            """, (decision_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            cols = [desc[0] for desc in cursor.description]
            data = dict(zip(cols, row))
            
            return DecisionOutcome(
                decision_id=data['decision_id'],
                trade_id=data['trade_id'],
                signal_executed=bool(data['signal_executed']),
                entry_price=data['entry_price'],
                exit_price=data['exit_price'],
                pnl=data['pnl'],
                pnl_percentage=data['pnl_percentage'],
                holding_time_minutes=data['holding_time_minutes'],
                max_drawdown=data['max_drawdown'],
                max_profit=data['max_profit'],
                exit_reason=data['exit_reason'],
                timestamp=datetime.fromisoformat(data['timestamp'])
            )
    
    async def find_similar_decisions(
        self,
        symbol: str,
        price_range: Tuple[float, float],
        volatility_range: Tuple[float, float],
        limit: int = 10
    ) -> List[Tuple[DecisionContext, Optional[DecisionOutcome]]]:
        """Find similar historical decisions"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT cd.*, do.*
                FROM council_decisions cd
                LEFT JOIN decision_outcomes do ON cd.decision_id = do.decision_id
                WHERE cd.symbol = ?
                    AND cd.current_price BETWEEN ? AND ?
                    AND json_extract(cd.market_snapshot, '$.recent_volatility') BETWEEN ? AND ?
                ORDER BY cd.timestamp DESC
                LIMIT ?
            """, (symbol, price_range[0], price_range[1], volatility_range[0], volatility_range[1], limit))
            
            results = []
            for row in cursor.fetchall():
                # Split the row data
                decision_data = row[:15]  # First 15 columns are from council_decisions
                outcome_data = row[15:]   # Remaining columns are from decision_outcomes
                
                decision = self._row_to_decision(decision_data, cursor.description[:15])
                outcome = None
                
                if outcome_data[0] is not None:  # Check if outcome exists
                    outcome_cols = [desc[0] for desc in cursor.description[15:]]
                    outcome_dict = dict(zip(outcome_cols, outcome_data))
                    
                    outcome = DecisionOutcome(
                        decision_id=outcome_dict['decision_id'],
                        trade_id=outcome_dict['trade_id'],
                        signal_executed=bool(outcome_dict['signal_executed']),
                        entry_price=outcome_dict['entry_price'],
                        exit_price=outcome_dict['exit_price'],
                        pnl=outcome_dict['pnl'],
                        pnl_percentage=outcome_dict['pnl_percentage'],
                        holding_time_minutes=outcome_dict['holding_time_minutes'],
                        max_drawdown=outcome_dict['max_drawdown'],
                        max_profit=outcome_dict['max_profit'],
                        exit_reason=outcome_dict['exit_reason'],
                        timestamp=datetime.fromisoformat(outcome_dict['timestamp'])
                    )
                
                if decision:
                    results.append((decision, outcome))
            
            return results
    
    async def delete_decisions_before(self, cutoff_date: datetime) -> int:
        """Delete old decisions for storage management"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # First delete outcomes
            cursor.execute("""
                DELETE FROM decision_outcomes
                WHERE decision_id IN (
                    SELECT decision_id FROM council_decisions
                    WHERE timestamp < ?
                )
            """, (cutoff_date,))
            
            # Then delete decisions
            cursor.execute("""
                DELETE FROM council_decisions WHERE timestamp < ?
            """, (cutoff_date,))
            
            deleted = cursor.rowcount
            conn.commit()
            
            return deleted
    
    def _row_to_decision(self, row: tuple, description: list) -> Optional[DecisionContext]:
        """Convert database row to DecisionContext"""
        try:
            cols = [desc[0] for desc in description]
            data = dict(zip(cols, row))
            
            # Deserialize JSON fields
            market_snapshot_data = json.loads(data['market_snapshot'])
            market_snapshot = MarketSnapshot(**market_snapshot_data)
            
            agent_analyses_data = json.loads(data['agent_analyses'])
            agent_analyses = [AgentAnalysis(**a) for a in agent_analyses_data]
            
            debate_rounds = json.loads(data['debate_rounds'])
            final_consensus = json.loads(data['final_consensus'])
            ml_prediction = json.loads(data['ml_prediction']) if data['ml_prediction'] else None
            
            # Reconstruct signal if it exists
            signal = None
            if data['signal_type']:
                from core.domain.models import TradingSignal
                signal = TradingSignal(
                    symbol=data['symbol'],
                    signal_type=SignalType(data['signal_type']),
                    confidence=data['confidence_score'],
                    entry_price=market_snapshot.current_price,
                    stop_loss=0,  # Would need to store these separately
                    take_profit=0,
                    timeframe="H1",
                    source="council"
                )
            
            return DecisionContext(
                decision_id=data['decision_id'],
                session_id=data['session_id'],
                market_snapshot=market_snapshot,
                agent_analyses=agent_analyses,
                debate_rounds=debate_rounds,
                final_consensus=final_consensus,
                signal_generated=signal,
                confidence_score=data['confidence_score'],
                ml_prediction=ml_prediction,
                api_tokens_used=data['api_tokens_used'],
                api_cost_usd=data['api_cost_usd'],
                decision_time_ms=data['decision_time_ms'],
                timestamp=datetime.fromisoformat(data['timestamp'])
            )
        except Exception as e:
            logger.error(f"Error converting row to decision: {e}")
            return None