@echo off
echo Starting Simple Unified Trading Dashboard (No Auth)...
echo =====================================

REM Show current directory
echo Working directory: %CD%

REM Activate virtual environment
if exist venv\Scripts\activate (
    call venv\Scripts\activate
) else (
    echo WARNING: Virtual environment not found, using system Python
)

REM Set Python path
set PYTHONPATH=%CD%

REM Start the simple unified dashboard
streamlit run scripts\unified_dashboard_simple.py

pause