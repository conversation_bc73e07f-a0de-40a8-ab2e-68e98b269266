@echo off
setlocal enabledelayedexpansion

REM GPT Trading System - Unified Launcher
REM =====================================
REM This script consolidates all launcher functionality into one menu-based interface

REM Set project paths
set PROJECT_ROOT=D:\gpt_trader_v1
set PYTHONPATH=%PROJECT_ROOT%
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

REM Change to project directory
cd /d %PROJECT_ROOT%

REM Determine Python executable
if exist "venv\Scripts\python.exe" (
    set PYTHON_EXE=venv\Scripts\python.exe
    set ACTIVATE_CMD=call venv\Scripts\activate.bat
    set ENV_TYPE=Virtual Environment
) else if exist "venv_wsl\Scripts\python.exe" (
    set PYTHON_EXE=venv_wsl\Scripts\python.exe
    set ACTIVATE_CMD=call venv_wsl\Scripts\activate.bat
    set ENV_TYPE=WSL Virtual Environment
) else (
    set PYTHON_EXE=python
    set ACTIVATE_CMD=echo Using system Python...
    set ENV_TYPE=System Python
)

:MAIN_MENU
cls
echo ============================================================
echo              GPT Trading System - Launcher
echo ============================================================
echo.
echo Environment: %ENV_TYPE%
echo Working Directory: %CD%
echo.
echo ============================================================
echo.
echo   1. Start Trading System
echo   2. Start Dashboard (Multiple Options)
echo   3. Start ML Monitor
echo   4. Start All Production Services
echo   5. Setup and Utilities
echo   6. Exit
echo.
echo ============================================================
echo.
set /p choice="Select an option (1-6): "

if "%choice%"=="1" goto START_TRADING
if "%choice%"=="2" goto DASHBOARD_MENU
if "%choice%"=="3" goto START_ML_MONITOR
if "%choice%"=="4" goto START_ALL_SERVICES
if "%choice%"=="5" goto UTILITIES_MENU
if "%choice%"=="6" goto EXIT_LAUNCHER

echo.
echo Invalid selection! Please try again.
pause
goto MAIN_MENU

:START_TRADING
cls
echo ============================================================
echo              Starting Trading System
echo ============================================================
echo.
echo Setting up environment...
%ACTIVATE_CMD%
echo.
echo Starting GPT Trading System...
echo.
%PYTHON_EXE% trading_loop.py %*
pause
goto MAIN_MENU

:DASHBOARD_MENU
cls
echo ============================================================
echo              Dashboard Options
echo ============================================================
echo.
echo   1. Unified Dashboard (Full Features)
echo   2. Simple Unified Dashboard (No Auth)
echo   3. Comprehensive Dashboard (Legacy)
echo   4. Simple Dashboard (No ML)
echo   5. Dashboard with GPT Flow (Streamlit)
echo   6. Back to Main Menu
echo.
echo ============================================================
echo.
set /p dash_choice="Select dashboard type (1-6): "

if "%dash_choice%"=="1" goto START_UNIFIED_DASHBOARD
if "%dash_choice%"=="2" goto START_UNIFIED_SIMPLE
if "%dash_choice%"=="3" goto START_COMPREHENSIVE_DASHBOARD
if "%dash_choice%"=="4" goto START_SIMPLE_DASHBOARD
if "%dash_choice%"=="5" goto START_GPT_FLOW_DASHBOARD
if "%dash_choice%"=="6" goto MAIN_MENU

echo.
echo Invalid selection! Please try again.
pause
goto DASHBOARD_MENU

:START_UNIFIED_DASHBOARD
cls
echo Starting Unified Trading Dashboard...
echo =====================================
echo.
%ACTIVATE_CMD%
%PYTHON_EXE% scripts\run_unified_dashboard.py
pause
goto DASHBOARD_MENU

:START_UNIFIED_SIMPLE
cls
echo Starting Simple Unified Trading Dashboard (No Auth)...
echo =====================================
echo.
%ACTIVATE_CMD%
streamlit run scripts\unified_dashboard_simple.py
pause
goto DASHBOARD_MENU

:START_COMPREHENSIVE_DASHBOARD
cls
echo ========================================
echo Starting Comprehensive Trading Dashboard
echo ========================================
echo.
%ACTIVATE_CMD%
%PYTHON_EXE% scripts\run_dashboard.py
pause
goto DASHBOARD_MENU

:START_SIMPLE_DASHBOARD
cls
echo Starting Simple Trading Dashboard...
echo =====================================
echo This dashboard does not include ML features
echo =====================================
echo.
%ACTIVATE_CMD%
%PYTHON_EXE% scripts\run_simple_dashboard.py
pause
goto DASHBOARD_MENU

:START_GPT_FLOW_DASHBOARD
cls
echo Starting Trading Dashboard with GPT Flow Visualization...
echo.
%ACTIVATE_CMD%
echo.
echo Starting Streamlit Dashboard...
streamlit run scripts\trading_dashboard_simple.py
pause
goto DASHBOARD_MENU

:START_ML_MONITOR
cls
echo Starting ML Performance Monitor...
echo ================================
echo.
%ACTIVATE_CMD%
echo.
echo Running ML monitor with all outputs...
%PYTHON_EXE% scripts\ml_performance_monitor.py --terminal --html --plot --days 30
echo.
echo ML Performance Monitor completed!
echo Check the reports folder for generated files.
echo.
pause
goto MAIN_MENU

:START_ALL_SERVICES
cls
echo ============================================================
echo        Starting All Production Services
echo ============================================================
echo.

REM Check environment setup
echo Checking environment setup...
if not exist ".env" (
    echo ERROR: .env file not found!
    echo Please create a .env file with your configuration.
    echo See .env.example for reference.
    pause
    goto MAIN_MENU
)

REM Quick environment test
echo Testing environment variables...
%PYTHON_EXE% -c "from dotenv import load_dotenv; import os; load_dotenv(); print(f'MT5_FILES_DIR: {os.getenv(\"MT5_FILES_DIR\", \"NOT SET\")}')"

echo.
echo Starting essential services in visible windows...
echo.

REM Ensure logs directory exists
if not exist "logs" mkdir "logs"

REM Start Trading Loop (visible window)
echo [1/2] Starting Trading Loop...
start "Trading Loop" cmd /c "cd /d %PROJECT_ROOT% && set PYTHONPATH=%PROJECT_ROOT% && %ACTIVATE_CMD% && %PYTHON_EXE% trading_loop.py || pause"

REM Wait for trading loop to initialize
timeout /t 10 /nobreak

REM Start ML Scheduler (visible window)
echo [2/2] Starting ML Scheduler...
start "ML Scheduler" cmd /c "cd /d %PROJECT_ROOT% && set PYTHONPATH=%PROJECT_ROOT% && %ACTIVATE_CMD% && %PYTHON_EXE% scripts\ml_scheduler.py daemon || pause"

echo.
echo ============================================================
echo All essential services started!
echo ============================================================
echo.
echo You should see 2 new command windows:
echo - Trading Loop (Main trading system)
echo - ML Scheduler (Model monitoring and updates)
echo.
echo If a window closes immediately, check for errors.
echo.
echo To stop services, close their windows or press Ctrl+C in each.
echo.
pause
goto MAIN_MENU

:UTILITIES_MENU
cls
echo ============================================================
echo              Setup and Utilities
echo ============================================================
echo.
echo   1. Create Windows Scheduled Tasks
echo   2. Run ML Model Training
echo   3. Run Backtesting
echo   4. Check MT5 Connection
echo   5. Find and Enable MT5 Symbols
echo   6. Database Backup
echo   7. Back to Main Menu
echo.
echo ============================================================
echo.
set /p util_choice="Select utility (1-7): "

if "%util_choice%"=="1" goto CREATE_SCHEDULED_TASKS
if "%util_choice%"=="2" goto TRAIN_ML_MODELS
if "%util_choice%"=="3" goto RUN_BACKTEST
if "%util_choice%"=="4" goto TEST_MT5_CONNECTION
if "%util_choice%"=="5" goto FIND_MT5_SYMBOLS
if "%util_choice%"=="6" goto DATABASE_BACKUP
if "%util_choice%"=="7" goto MAIN_MENU

echo.
echo Invalid selection! Please try again.
pause
goto UTILITIES_MENU

:CREATE_SCHEDULED_TASKS
cls
echo Creating Windows Scheduled Tasks for GPT Trading System...
echo.

REM Create main trading task (runs at startup)
echo Creating main trading task...
schtasks /create /tn "GPT Trading System Main" /tr "\"%PYTHON_EXE%\" \"%PROJECT_ROOT%\trading_loop.py\"" /sc onstart /ru %USERNAME% /f
if %errorlevel% equ 0 (
    echo [OK] Main trading task created
) else (
    echo [FAILED] Could not create main trading task
)
echo.

REM Create daily backup task (2:00 AM)
echo Creating daily backup task...
schtasks /create /tn "GPT Trading Backup" /tr "\"%PYTHON_EXE%\" \"%PROJECT_ROOT%\scripts\control_panel.py\" backup" /sc daily /st 02:00 /ru %USERNAME% /f
if %errorlevel% equ 0 (
    echo [OK] Backup task created
) else (
    echo [FAILED] Could not create backup task
)
echo.

REM Create daily report task (11:00 PM)
echo Creating daily report task...
schtasks /create /tn "GPT Trading Report" /tr "\"%PYTHON_EXE%\" \"%PROJECT_ROOT%\scripts\control_panel.py\" report" /sc daily /st 23:00 /ru %USERNAME% /f
if %errorlevel% equ 0 (
    echo [OK] Report task created
) else (
    echo [FAILED] Could not create report task
)
echo.

echo.
echo Setup complete!
echo.
echo To view your tasks, run: schtasks /query /tn "GPT Trading*"
echo To delete a task, run: schtasks /delete /tn "TASK_NAME" /f
echo.
pause
goto UTILITIES_MENU

:TRAIN_ML_MODELS
cls
echo Training ML Models...
echo ====================
echo.
%ACTIVATE_CMD%
%PYTHON_EXE% scripts\train_ml_production.py
pause
goto UTILITIES_MENU

:RUN_BACKTEST
cls
echo Running Backtesting...
echo =====================
echo.
%ACTIVATE_CMD%
%PYTHON_EXE% run_backtest.py
pause
goto UTILITIES_MENU

:TEST_MT5_CONNECTION
cls
echo Testing MT5 Connection...
echo ========================
echo.
%ACTIVATE_CMD%
%PYTHON_EXE% test_mt5_simple.py
pause
goto UTILITIES_MENU

:FIND_MT5_SYMBOLS
cls
echo Finding and Enabling MT5 Symbols...
echo ==================================
echo.
%ACTIVATE_CMD%
echo.
echo Run with --quick flag for common symbols? (Y/N)
set /p quick="Your choice: "
if /i "%quick%"=="Y" (
    %PYTHON_EXE% test_symbol_finder.py --quick
) else (
    %PYTHON_EXE% test_symbol_finder.py
)
pause
goto UTILITIES_MENU

:DATABASE_BACKUP
cls
echo Creating Database Backup...
echo ==========================
echo.
%ACTIVATE_CMD%
if exist "scripts\control_panel.py" (
    %PYTHON_EXE% scripts\control_panel.py backup
) else (
    echo WARNING: control_panel.py not found!
    echo Attempting manual backup...
    if exist "scripts\automation\database_backup.py" (
        %PYTHON_EXE% scripts\automation\database_backup.py
    ) else (
        echo ERROR: No backup script found!
    )
)
pause
goto UTILITIES_MENU

:EXIT_LAUNCHER
cls
echo.
echo Thank you for using GPT Trading System!
echo.
timeout /t 2 /nobreak >nul
exit /b 0