"""
MT5 connection fix - handle existing MT5 instance
"""
import MetaTrader5 as mt5
import time
import subprocess
import psutil
import os

print("Checking for running MT5 instances...")

# Check if MT5 is already running
mt5_running = False
for proc in psutil.process_iter(['pid', 'name']):
    try:
        if 'terminal64.exe' in proc.info['name'].lower():
            print(f"Found MT5 running: PID {proc.info['pid']}")
            mt5_running = True
            break
    except:
        pass

if mt5_running:
    print("\nMT5 is already running. Trying to connect to existing instance...")
    
    # Try multiple times with delays
    for attempt in range(5):
        print(f"\nAttempt {attempt + 1}/5...")
        
        # First shutdown any existing connection
        mt5.shutdown()
        time.sleep(1)
        
        # Try to initialize
        result = mt5.initialize()
        
        if result:
            print("SUCCESS! Connected to MT5")
            break
        else:
            error = mt5.last_error()
            print(f"Failed: {error}")
            
            if attempt < 4:
                print("Waiting before retry...")
                time.sleep(2)
    
    if not result:
        print("\nCouldn't connect to existing MT5 instance.")
        print("\nTry this:")
        print("1. Close MT5 completely")
        print("2. Run this script again (it will start MT5 fresh)")
        
else:
    print("\nMT5 is not running. Starting fresh instance...")
    
    # Try to find and start MT5
    possible_paths = [
        r"C:\Program Files\MetaTrader 5\terminal64.exe",
        r"C:\Program Files (x86)\MetaTrader 5\terminal64.exe",
        r"C:\Program Files\MetaQuotes\Terminal\terminal64.exe",
    ]
    
    mt5_path = None
    for path in possible_paths:
        if os.path.exists(path):
            mt5_path = path
            break
    
    if mt5_path:
        print(f"Found MT5 at: {mt5_path}")
        print("Starting MT5...")
        
        # Start MT5
        subprocess.Popen([mt5_path])
        
        # Wait for it to start
        print("Waiting for MT5 to start...")
        time.sleep(10)
        
        # Try to connect
        result = mt5.initialize()
        if result:
            print("SUCCESS! Connected to new MT5 instance")
        else:
            print(f"Failed to connect: {mt5.last_error()}")
    else:
        print("Could not find MT5 installation")

# If connected, show info
if 'result' in locals() and result:
    account_info = mt5.account_info()
    if account_info:
        print(f"\nAccount Info:")
        print(f"Login: {account_info.login}")
        print(f"Server: {account_info.server}")
        print(f"Balance: {account_info.balance}")
        
    terminal_info = mt5.terminal_info()
    if terminal_info:
        print(f"\nTerminal Info:")
        print(f"Path: {terminal_info.path}")
        print(f"Data path: {terminal_info.data_path}")
        print(f"Trade allowed: {terminal_info.trade_allowed}")

mt5.shutdown()
print("\nDone.")