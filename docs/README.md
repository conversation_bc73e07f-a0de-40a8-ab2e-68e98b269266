# Documentation Index

This directory contains all documentation for the GPT Trader v1 project.

## Root Documentation

Essential documentation files in the root directory:

- **[README.md](../README.md)** - Main project overview and getting started guide
- **[CLAUDE.md](../CLAUDE.md)** - Instructions for Claude Code AI assistant
- **[COMPREHENSIVE_USAGE_GUIDE.md](../COMPREHENSIVE_USAGE_GUIDE.md)** - Detailed usage instructions
- **[QUICK_REFERENCE.md](../QUICK_REFERENCE.md)** - Quick command reference
- **[README_LAUNCHERS.md](../README_LAUNCHERS.md)** - Guide for launcher scripts and batch files

## Technical Documentation

### Implementation Guides (`implementation/`)
- **[EXCEPTION_HANDLING_IMPROVEMENTS.md](implementation/EXCEPTION_HANDLING_IMPROVEMENTS.md)** - Exception handling patterns
- **[ML_USAGE_GUIDE.md](implementation/ML_USAGE_GUIDE.md)** - Machine Learning integration guide
- **[MT5_ORDER_MANAGEMENT_GUIDE.md](implementation/MT5_ORDER_MANAGEMENT_GUIDE.md)** - MT5 order management
- **[MT5_TEST_GUIDE.md](implementation/MT5_TEST_GUIDE.md)** - MT5 testing procedures
- **[POSITION_TRADING_IMPLEMENTATION.md](implementation/POSITION_TRADING_IMPLEMENTATION.md)** - Position trading implementation
- **[POSITION_TRADING_UPDATE.md](implementation/POSITION_TRADING_UPDATE.md)** - Position trading updates
- **[STARTUP_CHECKLIST.md](implementation/STARTUP_CHECKLIST.md)** - System startup checklist

### Progress Reports (`progress/`)
- **[CODE_REVIEW_PROGRESS.md](progress/CODE_REVIEW_PROGRESS.md)** - Code review progress tracking
- **[CODE_REVIEW_REPORT.md](progress/CODE_REVIEW_REPORT.md)** - Code review findings
- **[PHASE1_COST_OPTIMIZATION_SUMMARY.md](progress/PHASE1_COST_OPTIMIZATION_SUMMARY.md)** - Cost optimization results
- **[PHASE2_MARKET_EXPANSION_SUMMARY.md](progress/PHASE2_MARKET_EXPANSION_SUMMARY.md)** - Market expansion summary
- **[PHASE2_POSITION_TRADING_SUMMARY.md](progress/PHASE2_POSITION_TRADING_SUMMARY.md)** - Position trading implementation
- **[PHASE3_BACKTESTING_SUMMARY.md](progress/PHASE3_BACKTESTING_SUMMARY.md)** - Backtesting improvements
- **[REFACTORING_PLAN.md](progress/REFACTORING_PLAN.md)** - Refactoring roadmap
- **[REFACTORING_PROGRESS_SUMMARY.md](progress/REFACTORING_PROGRESS_SUMMARY.md)** - Refactoring progress

### Integration Guides
- **[dependency_management_guide.md](dependency_management_guide.md)** - Managing project dependencies
- **[gpt_flow_visualization_guide.md](gpt_flow_visualization_guide.md)** - Visualizing GPT request flows
- **[marketaux_free_plan_guide.md](marketaux_free_plan_guide.md)** - Using MarketAux free tier
- **[marketaux_integration_guide.md](marketaux_integration_guide.md)** - MarketAux API integration
- **[ml_enhancement_guide.md](ml_enhancement_guide.md)** - Enhancing ML models
- **[position_trading_guide.md](position_trading_guide.md)** - Position trading strategies
- **[secure_ml_models_guide.md](secure_ml_models_guide.md)** - Securing ML models
- **[symbol_resolution_guide.md](symbol_resolution_guide.md)** - Symbol mapping and resolution
- **[trading_council_guide.md](trading_council_guide.md)** - Trading Council architecture

## Dashboard Documentation

Dashboard-specific documentation in the scripts directory:

- **[scripts/DASHBOARD_README.md](../scripts/DASHBOARD_README.md)** - Main dashboard guide
- **[scripts/DASHBOARD_AUTH_GUIDE.md](../scripts/DASHBOARD_AUTH_GUIDE.md)** - Dashboard authentication setup
- **[scripts/ML_MONITOR_README.md](../scripts/ML_MONITOR_README.md)** - ML monitoring dashboard
- **[scripts/ML_PREDICTIONS_SETUP.md](../scripts/ML_PREDICTIONS_SETUP.md)** - ML predictions configuration

## Archived Documentation

### Historical Documentation (`archive/`)
- **[AGENT_IMPROVEMENTS_SUMMARY.md](archive/AGENT_IMPROVEMENTS_SUMMARY.md)** - Agent system improvements
- **[CIRCULAR_IMPORT_FIX_SUMMARY.md](archive/CIRCULAR_IMPORT_FIX_SUMMARY.md)** - Import fixes
- **[CLEANUP_PLAN.md](archive/CLEANUP_PLAN.md)** - Project cleanup plan
- **[CLEANUP_SUMMARY.md](archive/CLEANUP_SUMMARY.md)** - Cleanup results
- **[FIX_SUMMARY.md](archive/FIX_SUMMARY.md)** - Various fixes summary
- **[IMPORT_FIX_SUMMARY.md](archive/IMPORT_FIX_SUMMARY.md)** - Import resolution summary
- **[ML_INTEGRATION_PLAN.md](archive/ML_INTEGRATION_PLAN.md)** - ML integration planning
- **[ML_INTEGRATION_SUMMARY.md](archive/ML_INTEGRATION_SUMMARY.md)** - ML integration results
- **[MT5_TEST_FIXES.md](archive/MT5_TEST_FIXES.md)** - MT5 test issue resolutions
- **[dashboard_fixes_summary.md](archive/dashboard_fixes_summary.md)** - Dashboard fixes
- **[vsa_rules.txt](archive/vsa_rules.txt)** - Volume Spread Analysis rules

### Status Reports (`archive/status/`)
- **[ML_TELEGRAM_FIX_SUMMARY.md](archive/status/ML_TELEGRAM_FIX_SUMMARY.md)** - ML and Telegram fixes
- **[TRADING_LOOP_STATUS.md](archive/status/TRADING_LOOP_STATUS.md)** - Trading loop status
- **[drawdown_fixes_summary.md](archive/status/drawdown_fixes_summary.md)** - Drawdown management fixes
- **[improve_win_rate_strategy.md](archive/status/improve_win_rate_strategy.md)** - Win rate improvement strategies
- **[integrate_risk_management.md](archive/status/integrate_risk_management.md)** - Risk management integration

## Documentation Organization

The documentation is organized into the following categories:

1. **Root Documentation** - Essential guides for getting started and daily use
2. **Technical Documentation** - Implementation details and technical guides
3. **Dashboard Documentation** - Dashboard-specific setup and usage
4. **Archived Documentation** - Historical documentation for reference

When looking for specific information:
- Start with the root documentation for overview and quick reference
- Check technical documentation for implementation details
- Refer to dashboard documentation for UI-related setup
- Browse archived documentation for historical context and past decisions


● Perfect! I've updated both guides with the correct backtest flags. The main backtest script (run_backtest.py) now has these enhanced modes:

  📊 Enhanced Backtest Modes:

  1. --mode full_council - Uses all 7 AI agents
    - Most accurate results
    - Highest GPT costs
    - Best for final validation
  2. --mode simplified - Uses cheaper GPT models
    - Balanced cost/accuracy
    - Good for development testing
    - ~70% cost reduction
  3. --mode ml_only - Uses only ML models
    - No GPT costs at all
    - Requires trained ML models
    - Fastest execution
  4. --mode auto (default) - Automatically selects best mode
    - Considers available resources
    - Balances cost and accuracy
  5. --mode legacy - Uses old backtest engine
    - For comparison purposes

  🔧 Additional Options:

  - --compare-modes - Runs all modes and compares results
  - --walk-forward N - Walk-forward analysis with N periods
  - --optimize - Parameter optimization
  - --position-trading - Uses D1/W1 timeframes
  - --train-simulator - Trains LLM simulator before backtesting
  - --symbols - Space-separated list (not comma!)
  - --days - Number of days to backtest (default: 180)

  💡 Cost-Saving Tips:

  1. Use ml_only mode for quick tests (free)
  2. Use simplified mode for development (70% cheaper)
  3. Reserve full_council for final validation
  4. Run --compare-modes to see accuracy vs cost tradeoffs

  The guides are now accurate with all the current backtest capabilities!