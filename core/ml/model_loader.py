"""
Custom model loader that handles import path changes for pickled models
"""

import pickle
import io
import sys
from pathlib import Path
from typing import Any, Dict

# Ensure the new module is available
from core.ml.feature_engineering import ProductionFeatureEngineer


class ModelUnpickler(pickle.Unpickler):
    """Custom unpickler that handles module path remapping"""
    
    def find_class(self, module, name):
        """Override find_class to remap old module paths to new ones"""
        # Map old module paths to new ones
        module_mappings = {
            'scripts.train_ml_production': 'core.ml.feature_engineering',
            '__main__': 'core.ml.feature_engineering',  # In case it was pickled from __main__
        }
        
        # Check if we need to remap the module
        if name == 'ProductionFeatureEngineer' and module in module_mappings:
            module = module_mappings[module]
        
        # Use the default find_class with the potentially remapped module
        return super().find_class(module, name)


def load_model_safe(model_path: Path) -> Dict[str, Any]:
    """
    Safely load a pickled model, handling import path changes
    
    Args:
        model_path: Path to the pickled model file
        
    Returns:
        The unpickled model data
    """
    with open(model_path, 'rb') as f:
        try:
            # Try custom unpickler first
            unpickler = ModelUnpickler(f)
            return unpickler.load()
        except Exception as e:
            # If custom unpickler fails, try adding module to sys.modules as a fallback
            f.seek(0)  # Reset file position
            
            # Temporarily add the old module path
            import types
            old_module = types.ModuleType('scripts.train_ml_production')
            old_module.ProductionFeatureEngineer = ProductionFeatureEngineer
            sys.modules['scripts.train_ml_production'] = old_module
            
            try:
                # Try loading again
                model_data = pickle.load(f)
                return model_data
            finally:
                # Clean up the temporary module
                if 'scripts.train_ml_production' in sys.modules:
                    del sys.modules['scripts.train_ml_production']


def save_model_safe(model_data: Dict[str, Any], model_path: Path):
    """
    Save a model ensuring ProductionFeatureEngineer has the correct module path
    
    Args:
        model_data: The model data to save
        model_path: Path where to save the model
    """
    # Ensure ProductionFeatureEngineer has correct module path
    if 'feature_engineer' in model_data:
        feature_engineer = model_data['feature_engineer']
        if hasattr(feature_engineer, '__class__') and feature_engineer.__class__.__name__ == 'ProductionFeatureEngineer':
            # This ensures the class is from the correct module
            model_data['feature_engineer'] = ProductionFeatureEngineer(
                lookback_periods=getattr(feature_engineer, 'lookback_periods', [5, 10, 20, 50, 100])
            )
            # Copy over the fitted state if any
            if hasattr(feature_engineer, 'feature_names'):
                model_data['feature_engineer'].feature_names = feature_engineer.feature_names
    
    # Save the model
    with open(model_path, 'wb') as f:
        pickle.dump(model_data, f)