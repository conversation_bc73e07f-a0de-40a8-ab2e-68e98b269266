"""
Authentication middleware for command-line tools and APIs.
Provides secure authentication for non-Streamlit applications.
"""

import os
import jwt
import bcrypt
import json
import functools
from typing import Optional, Dict, Any, Callable, List
from datetime import datetime, timezone, timedelta
from pathlib import Path
import getpass
import hashlib
import secrets
from enum import Enum

from core.utils.structured_logger import get_logger
from core.domain.exceptions import AuthenticationError

logger = get_logger(__name__)


class AuthLevel(Enum):
    """Authentication levels"""
    READ_ONLY = "read_only"
    OPERATOR = "operator"
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"


class Permission(Enum):
    """System permissions"""
    VIEW_TRADES = "view_trades"
    EXECUTE_TRADES = "execute_trades"
    MODIFY_CONFIG = "modify_config"
    VIEW_LOGS = "view_logs"
    SYSTEM_CONTROL = "system_control"
    BACKUP_RESTORE = "backup_restore"
    ML_TRAINING = "ml_training"
    USER_MANAGEMENT = "user_management"


# Permission mappings by auth level
LEVEL_PERMISSIONS = {
    AuthLevel.READ_ONLY: [
        Permission.VIEW_TRADES,
        Permission.VIEW_LOGS
    ],
    AuthLevel.OPERATOR: [
        Permission.VIEW_TRADES,
        Permission.EXECUTE_TRADES,
        Permission.VIEW_LOGS,
        Permission.ML_TRAINING
    ],
    AuthLevel.ADMIN: [
        Permission.VIEW_TRADES,
        Permission.EXECUTE_TRADES,
        Permission.MODIFY_CONFIG,
        Permission.VIEW_LOGS,
        Permission.SYSTEM_CONTROL,
        Permission.BACKUP_RESTORE,
        Permission.ML_TRAINING
    ],
    AuthLevel.SUPER_ADMIN: [p for p in Permission]  # All permissions
}


class CLIAuthManager:
    """Authentication manager for command-line tools"""
    
    def __init__(self, app_name: str = "GPT Trading System"):
        self.app_name = app_name
        self.auth_dir = Path.home() / ".gpt_trader" / "auth"
        self.auth_dir.mkdir(parents=True, exist_ok=True)
        
        self.users_file = self.auth_dir / "users.json"
        self.tokens_file = self.auth_dir / "tokens.json"
        self.api_keys_file = self.auth_dir / "api_keys.json"
        
        # JWT configuration
        self.jwt_secret = self._get_or_create_secret()
        self.jwt_algorithm = "HS256"
        self.token_expiry_hours = 24
        
        # Initialize storage files
        self._init_storage()
    
    def _get_or_create_secret(self) -> str:
        """Get or create JWT secret"""
        secret_file = self.auth_dir / "jwt_secret"
        
        if secret_file.exists():
            return secret_file.read_text().strip()
        else:
            # Generate new secret
            secret = secrets.token_urlsafe(32)
            secret_file.write_text(secret)
            # Secure the file
            os.chmod(secret_file, 0o600)
            return secret
    
    def _init_storage(self):
        """Initialize storage files if they don't exist"""
        # Users file
        if not self.users_file.exists():
            default_users = {
                "admin": {
                    "password_hash": self._hash_password("admin123"),  # Change on first login
                    "level": AuthLevel.SUPER_ADMIN.value,
                    "created_at": datetime.now(timezone.utc).isoformat(),
                    "must_change_password": True
                }
            }
            self._save_json(self.users_file, default_users)
            logger.info("Created default admin user. Password: admin123 (must change on first login)")
        
        # Tokens file
        if not self.tokens_file.exists():
            self._save_json(self.tokens_file, {})
        
        # API keys file
        if not self.api_keys_file.exists():
            self._save_json(self.api_keys_file, {})
    
    def _save_json(self, filepath: Path, data: Dict):
        """Save JSON data securely"""
        filepath.write_text(json.dumps(data, indent=2))
        os.chmod(filepath, 0o600)
    
    def _load_json(self, filepath: Path) -> Dict:
        """Load JSON data"""
        if filepath.exists():
            return json.loads(filepath.read_text())
        return {}
    
    def _hash_password(self, password: str) -> str:
        """Hash a password using bcrypt"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def _verify_password(self, password: str, hash: str) -> bool:
        """Verify a password against its hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hash.encode('utf-8'))
    
    def authenticate_interactive(self) -> Optional[Dict[str, Any]]:
        """Interactive authentication for CLI"""
        print(f"\n{self.app_name} - Authentication Required")
        print("-" * 40)
        
        username = input("Username: ")
        password = getpass.getpass("Password: ")
        
        try:
            token_data = self.authenticate(username, password)
            
            # Check if password change required
            users = self._load_json(self.users_file)
            user = users.get(username, {})
            
            if user.get('must_change_password', False):
                print("\nPassword change required.")
                new_password = self._prompt_password_change()
                self.change_password(username, password, new_password)
                # Re-authenticate with new password
                token_data = self.authenticate(username, new_password)
            
            print(f"\n✅ Authentication successful. Welcome, {username}!")
            return token_data
            
        except AuthenticationError as e:
            print(f"\n❌ Authentication failed: {e}")
            return None
    
    def _prompt_password_change(self) -> str:
        """Prompt for password change"""
        while True:
            new_password = getpass.getpass("New password: ")
            confirm_password = getpass.getpass("Confirm password: ")
            
            if new_password != confirm_password:
                print("Passwords do not match. Try again.")
                continue
            
            if len(new_password) < 8:
                print("Password must be at least 8 characters. Try again.")
                continue
            
            return new_password
    
    def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """Authenticate user and return token data"""
        users = self._load_json(self.users_file)
        
        if username not in users:
            raise AuthenticationError("Invalid username or password")
        
        user = users[username]
        
        if not self._verify_password(password, user['password_hash']):
            raise AuthenticationError("Invalid username or password")
        
        # Generate JWT token
        token_data = {
            'username': username,
            'level': user['level'],
            'permissions': [p.value for p in LEVEL_PERMISSIONS[AuthLevel(user['level'])]],
            'exp': datetime.now(timezone.utc) + timedelta(hours=self.token_expiry_hours),
            'iat': datetime.now(timezone.utc)
        }
        
        token = jwt.encode(token_data, self.jwt_secret, algorithm=self.jwt_algorithm)
        
        # Store token
        tokens = self._load_json(self.tokens_file)
        tokens[token] = {
            'username': username,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'expires_at': token_data['exp'].isoformat()
        }
        self._save_json(self.tokens_file, tokens)
        
        return {
            'token': token,
            'username': username,
            'level': user['level'],
            'permissions': token_data['permissions']
        }
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify JWT token"""
        try:
            # Decode token
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            
            # Check if token is in active tokens
            tokens = self._load_json(self.tokens_file)
            if token not in tokens:
                raise AuthenticationError("Invalid token")
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.InvalidTokenError:
            raise AuthenticationError("Invalid token")
    
    def create_api_key(self, username: str, description: str) -> str:
        """Create an API key for programmatic access"""
        # Verify user exists and is authorized
        users = self._load_json(self.users_file)
        if username not in users:
            raise AuthenticationError("User not found")
        
        user = users[username]
        if AuthLevel(user['level']) not in [AuthLevel.ADMIN, AuthLevel.SUPER_ADMIN]:
            raise AuthenticationError("Insufficient permissions to create API keys")
        
        # Generate API key
        api_key = f"gpt_{secrets.token_urlsafe(32)}"
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Store API key
        api_keys = self._load_json(self.api_keys_file)
        api_keys[key_hash] = {
            'username': username,
            'description': description,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'last_used': None,
            'active': True
        }
        self._save_json(self.api_keys_file, api_keys)
        
        return api_key
    
    def verify_api_key(self, api_key: str) -> Dict[str, Any]:
        """Verify API key"""
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        api_keys = self._load_json(self.api_keys_file)
        if key_hash not in api_keys:
            raise AuthenticationError("Invalid API key")
        
        key_data = api_keys[key_hash]
        if not key_data.get('active', True):
            raise AuthenticationError("API key is inactive")
        
        # Update last used
        key_data['last_used'] = datetime.now(timezone.utc).isoformat()
        self._save_json(self.api_keys_file, api_keys)
        
        # Get user data
        users = self._load_json(self.users_file)
        user = users.get(key_data['username'])
        
        return {
            'username': key_data['username'],
            'level': user['level'],
            'permissions': [p.value for p in LEVEL_PERMISSIONS[AuthLevel(user['level'])]]
        }
    
    def add_user(
        self,
        username: str,
        password: str,
        level: AuthLevel,
        created_by: str
    ):
        """Add a new user"""
        users = self._load_json(self.users_file)
        
        if username in users:
            raise ValueError(f"User {username} already exists")
        
        users[username] = {
            'password_hash': self._hash_password(password),
            'level': level.value,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'created_by': created_by,
            'must_change_password': True
        }
        
        self._save_json(self.users_file, users)
        logger.info(f"User {username} created by {created_by}")
    
    def change_password(self, username: str, old_password: str, new_password: str):
        """Change user password"""
        users = self._load_json(self.users_file)
        
        if username not in users:
            raise AuthenticationError("User not found")
        
        user = users[username]
        
        if not self._verify_password(old_password, user['password_hash']):
            raise AuthenticationError("Invalid current password")
        
        user['password_hash'] = self._hash_password(new_password)
        user['must_change_password'] = False
        user['password_changed_at'] = datetime.now(timezone.utc).isoformat()
        
        self._save_json(self.users_file, users)
        logger.info(f"Password changed for user {username}")
    
    def revoke_token(self, token: str):
        """Revoke a token"""
        tokens = self._load_json(self.tokens_file)
        if token in tokens:
            del tokens[token]
            self._save_json(self.tokens_file, tokens)
    
    def cleanup_expired_tokens(self):
        """Clean up expired tokens"""
        tokens = self._load_json(self.tokens_file)
        now = datetime.now(timezone.utc)
        
        expired = []
        for token, data in tokens.items():
            expires_at = datetime.fromisoformat(data['expires_at'])
            if expires_at < now:
                expired.append(token)
        
        for token in expired:
            del tokens[token]
        
        if expired:
            self._save_json(self.tokens_file, tokens)
            logger.info(f"Cleaned up {len(expired)} expired tokens")


def require_auth(
    required_permission: Optional[Permission] = None,
    required_level: Optional[AuthLevel] = None
):
    """Decorator to require authentication for functions"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Check for auth context
            auth_context = kwargs.get('auth_context')
            if not auth_context:
                raise AuthenticationError("Authentication required")
            
            # Check permission
            if required_permission:
                if required_permission.value not in auth_context.get('permissions', []):
                    raise AuthenticationError(
                        f"Permission denied. Required: {required_permission.value}"
                    )
            
            # Check level
            if required_level:
                user_level = AuthLevel(auth_context.get('level'))
                if user_level.value < required_level.value:
                    raise AuthenticationError(
                        f"Insufficient privileges. Required: {required_level.value}"
                    )
            
            return func(*args, **kwargs)
        
        return wrapper
    
    return decorator


# Global auth manager instance
_cli_auth_manager = None


def get_cli_auth_manager() -> CLIAuthManager:
    """Get or create CLI auth manager"""
    global _cli_auth_manager
    if _cli_auth_manager is None:
        _cli_auth_manager = CLIAuthManager()
    return _cli_auth_manager