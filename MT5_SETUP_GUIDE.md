# MT5 Setup Guide

## MT5 Authorization Failed - How to Fix

The error `Terminal: Authorization failed` means MetaTrader 5 opened but couldn't log in to your trading account.

### Steps to Fix:

1. **Update your .env file** with your actual MT5 credentials:
   ```
   MT5_ACCOUNT=your_actual_account_number
   MT5_PASSWORD=your_actual_password
   MT5_SERVER=your_broker_server_name
   ```

2. **Find your MT5 credentials:**
   - Open MetaTrader 5 manually
   - Go to File → Login to Trade Account
   - Note your:
     - Login (account number)
     - Server name
     - Password (you should know this)

3. **Common broker servers:**
   - ICMarkets: ICMarketsSC-Demo or ICMarketsSC-Live
   - XM: XMGlobal-Demo-2 or XMGlobal-Real-2
   - Pepperstone: Pepperstone-Demo or Pepperstone-Live
   - FTMO: FTMO-Demo or FTMO-Server

4. **For demo accounts:**
   - You can create a new demo account in MT5
   - File → Open an Account → Select broker → Demo account
   - Use the provided credentials in your .env file

### Alternative: Let MT5 Remember Login

If you want MT5 to handle login automatically:

1. Open MT5 manually
2. Log in with "Save password" checked
3. Close MT5
4. Comment out or remove MT5 credentials from .env:
   ```
   # MT5_ACCOUNT=12345
   # MT5_PASSWORD=password
   # MT5_SERVER=YourBroker-Server
   ```

The system will then use whatever account is already logged into MT5.

### Test Your Setup

After updating .env, run:
```bash
# From WSL
cmd.exe /c "D:\\gpt_trader_v1\\venv\\Scripts\\python.exe D:\\gpt_trader_v1\\tests\\manual\\test_mt5_connection.py"

# Or from Windows
python tests\manual\test_mt5_connection.py
```

This will verify your MT5 connection is working correctly.