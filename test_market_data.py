"""
Test MT5 market data access
"""
import MetaTrader5 as mt5
from datetime import datetime
import pytz

print("Testing MT5 market data access...")
print(f"Current time: {datetime.now()}")

# Initialize MT5
if not mt5.initialize():
    print(f"MT5 initialization failed: {mt5.last_error()}")
    exit(1)

print("\nMT5 Connected successfully")

# Test various timeframes
timeframes = [
    (mt5.TIMEFRAME_M1, "M1"),
    (mt5.TIMEFRAME_M5, "M5"),
    (mt5.TIMEFRAME_M15, "M15"),
    (mt5.TIMEFRAME_H1, "H1"),
    (mt5.TIMEFRAME_H4, "H4"),
    (mt5.TIMEFRAME_D1, "D1")
]

symbols = ["EURUSD", "GBPUSD", "XAUUSD"]

for symbol in symbols:
    print(f"\n{'='*50}")
    print(f"Testing {symbol}:")
    print(f"{'='*50}")
    
    # Check if symbol exists
    symbol_info = mt5.symbol_info(symbol)
    if symbol_info is None:
        print(f"❌ Symbol {symbol} not found")
        continue
        
    print(f"✓ Symbol found: {symbol_info.name}")
    print(f"  Visible: {symbol_info.visible}")
    print(f"  Selected: {symbol_info.select}")
    print(f"  Spread: {symbol_info.spread}")
    print(f"  Digits: {symbol_info.digits}")
    
    # Try to select symbol
    if not symbol_info.visible:
        if mt5.symbol_select(symbol, True):
            print(f"✓ Symbol {symbol} selected successfully")
        else:
            print(f"❌ Failed to select {symbol}")
            continue
    
    # Get current tick
    tick = mt5.symbol_info_tick(symbol)
    if tick:
        print(f"\nCurrent tick:")
        print(f"  Bid: {tick.bid}")
        print(f"  Ask: {tick.ask}")
        print(f"  Time: {datetime.fromtimestamp(tick.time)}")
    else:
        print(f"❌ No tick data available")
    
    # Try different methods to get rates
    print(f"\nTesting different data retrieval methods:")
    
    # Method 1: copy_rates_from_pos
    for tf, tf_name in timeframes[:3]:  # Test first 3 timeframes
        rates = mt5.copy_rates_from_pos(symbol, tf, 0, 10)
        if rates is not None and len(rates) > 0:
            print(f"  ✓ {tf_name}: Got {len(rates)} bars, latest: {datetime.fromtimestamp(rates[-1]['time'])}")
        else:
            print(f"  ❌ {tf_name}: No data")
    
    # Method 2: copy_rates_from with timezone
    utc_tz = pytz.timezone('UTC')
    utc_from = datetime.now(utc_tz)
    rates = mt5.copy_rates_from(symbol, mt5.TIMEFRAME_H1, utc_from, 10)
    if rates is not None and len(rates) > 0:
        print(f"  ✓ copy_rates_from: Got {len(rates)} bars")
    else:
        print(f"  ❌ copy_rates_from: No data")

# Check account status
account = mt5.account_info()
if account:
    print(f"\n{'='*50}")
    print(f"Account Status:")
    print(f"{'='*50}")
    print(f"  Server: {account.server}")
    print(f"  Trade allowed: {account.trade_allowed}")
    print(f"  Trade expert: {account.trade_expert}")
    print(f"  Connection status: {'Connected' if mt5.terminal_info().connected else 'Disconnected'}")

# Check terminal info
terminal = mt5.terminal_info()
if terminal:
    print(f"\nTerminal Info:")
    print(f"  Connected: {terminal.connected}")
    print(f"  Trade allowed: {terminal.trade_allowed}")
    print(f"  Community account: {terminal.community_account}")
    print(f"  Path: {terminal.path}")

mt5.shutdown()
print("\nTest complete.")