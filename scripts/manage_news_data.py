#!/usr/bin/env python3
"""
Comprehensive ForexFactory News Data Manager
Handles updating, fallback creation, and validation of news data.
"""

import json
import logging
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NewsDataManager:
    """Manages ForexFactory news data with multiple strategies"""
    
    def __init__(self):
        self.data_dir = Path("data")
        self.data_dir.mkdir(exist_ok=True)
        self.news_file = self.data_dir / "forexfactory_week.json"
    
    def check_news_file_status(self) -> Dict[str, Any]:
        """Check the current status of the news file"""
        status = {
            'exists': False,
            'valid': False,
            'age_hours': None,
            'event_count': 0,
            'last_updated': None,
            'needs_update': True
        }
        
        try:
            if self.news_file.exists():
                status['exists'] = True
                
                # Check file age
                file_mtime = datetime.fromtimestamp(self.news_file.stat().st_mtime)
                age = datetime.now() - file_mtime
                status['age_hours'] = age.total_seconds() / 3600
                
                # Try to load and validate
                try:
                    with open(self.news_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if isinstance(data, list):
                        status['valid'] = True
                        status['event_count'] = len(data)
                        
                        # Check if data is recent (less than 24 hours old)
                        if status['age_hours'] < 24:
                            status['needs_update'] = False
                        
                        # Try to find last updated info in events
                        if data:
                            # Sort by datetime and get the latest
                            try:
                                sorted_events = sorted(data, key=lambda x: x.get('datetime', ''), reverse=True)
                                if sorted_events:
                                    status['last_updated'] = sorted_events[0].get('datetime')
                            except:
                                pass
                    
                except (json.JSONDecodeError, KeyError) as e:
                    logger.warning(f"News file is corrupted: {e}")
                    status['valid'] = False
                    
        except Exception as e:
            logger.error(f"Error checking news file: {e}")
        
        return status
    
    def update_from_api(self) -> bool:
        """Try to update from ForexFactory API"""
        try:
            logger.info("🔄 Attempting to update from ForexFactory API...")
            
            # Import and run the updater
            import sys
            import subprocess
            
            result = subprocess.run([
                sys.executable, 
                "scripts/update_forexfactory_news.py"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                logger.info("✅ API update successful")
                return True
            else:
                logger.error(f"❌ API update failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ API update timed out")
            return False
        except Exception as e:
            logger.error(f"❌ API update error: {e}")
            return False
    
    def create_fallback(self) -> bool:
        """Create fallback news data"""
        try:
            logger.info("🔧 Creating fallback news data...")
            
            import subprocess
            
            result = subprocess.run([
                sys.executable,
                "scripts/create_fallback_news.py"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                logger.info("✅ Fallback creation successful")
                return True
            else:
                logger.error(f"❌ Fallback creation failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Fallback creation error: {e}")
            return False
    
    def ensure_news_data_available(self) -> bool:
        """Ensure news data is available using multiple strategies"""
        logger.info("🔍 Checking news data availability...")
        
        status = self.check_news_file_status()
        
        # Print current status
        logger.info(f"📊 News File Status:")
        logger.info(f"   Exists: {status['exists']}")
        logger.info(f"   Valid: {status['valid']}")
        logger.info(f"   Events: {status['event_count']}")
        if status['age_hours'] is not None:
            logger.info(f"   Age: {status['age_hours']:.1f} hours")
        
        # If file is valid and recent, we're good
        if status['valid'] and not status['needs_update']:
            logger.info("✅ News data is current and valid")
            return True
        
        # Try to update from API first
        if status['needs_update']:
            logger.info("🔄 News data needs updating...")
            if self.update_from_api():
                # Verify the update worked
                new_status = self.check_news_file_status()
                if new_status['valid']:
                    logger.info("✅ Successfully updated from API")
                    return True
        
        # If API failed and we don't have valid data, create fallback
        if not status['valid']:
            logger.warning("⚠️  API update failed, creating fallback data...")
            if self.create_fallback():
                # Verify fallback worked
                fallback_status = self.check_news_file_status()
                if fallback_status['valid']:
                    logger.info("✅ Fallback data created successfully")
                    return True
        
        # If we have old but valid data, use it
        if status['valid']:
            logger.warning("⚠️  Using existing (possibly outdated) news data")
            return True
        
        logger.error("❌ Failed to ensure news data availability")
        return False
    
    def validate_news_data(self) -> bool:
        """Validate the current news data"""
        try:
            if not self.news_file.exists():
                logger.error("❌ News file does not exist")
                return False
            
            with open(self.news_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                logger.error("❌ News data must be a list")
                return False
            
            # Validate structure of events
            required_fields = ['datetime', 'currency', 'event', 'impact']
            valid_events = 0
            
            for i, event in enumerate(data):
                if not isinstance(event, dict):
                    logger.warning(f"⚠️  Event {i} is not a dictionary")
                    continue
                
                missing_fields = [field for field in required_fields if field not in event]
                if missing_fields:
                    logger.warning(f"⚠️  Event {i} missing fields: {missing_fields}")
                    continue
                
                valid_events += 1
            
            logger.info(f"✅ Validation complete: {valid_events}/{len(data)} events are valid")
            return valid_events > 0
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {e}")
            return False

def main():
    """Main function"""
    logger.info("🚀 ForexFactory News Data Manager")
    
    manager = NewsDataManager()
    
    # Check command line arguments
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "status":
            status = manager.check_news_file_status()
            print(json.dumps(status, indent=2))
            return True
        elif command == "update":
            return manager.update_from_api()
        elif command == "fallback":
            return manager.create_fallback()
        elif command == "validate":
            return manager.validate_news_data()
        elif command == "ensure":
            return manager.ensure_news_data_available()
        else:
            logger.error(f"Unknown command: {command}")
            logger.info("Available commands: status, update, fallback, validate, ensure")
            return False
    
    # Default behavior: ensure data is available
    success = manager.ensure_news_data_available()
    
    if success:
        logger.info("🎉 News data management completed successfully!")
    else:
        logger.error("💥 News data management failed!")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
