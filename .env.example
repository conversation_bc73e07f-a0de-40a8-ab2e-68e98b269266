# Copy this file to .env and fill in your actual values
# <PERSON>VER commit the actual .env file to version control

# OpenAI Configuration (REQUIRED)
OPENAI_API_KEY=your_openai_api_key_here

# MetaTrader 5 Configuration (REQUIRED)
MT5_FILES_DIR=C:/path/to/your/MT5/MQL5/Files

# Telegram Configuration (OPTIONAL)
TELEGRAM_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
TELEGRAM_ENABLED=false

# Machine Learning Configuration
ML_ENABLED=true

# Trading Council Configuration
TRADING_USE_COUNCIL=true
TRADING_COUNCIL_MIN_CONFIDENCE=50.0
TRADING_COUNCIL_LLM_WEIGHT=0.7
TRADING_COUNCIL_ML_WEIGHT=0.3
TRADING_COUNCIL_DEBATE_ROUNDS=3

# MarketAux News API Configuration (OPTIONAL)
MARKETAUX_API_TOKEN=your_marketaux_api_token
MARKETAUX_ENABLED=true
MARKETAUX_DAILY_LIMIT=100
MARKETAUX_REQUESTS_PER_MINUTE=5
MARKETAUX_CACHE_TTL_HOURS=24
MARKETAUX_MIN_RELEVANCE_SCORE=0.3
MARKETAUX_SENTIMENT_WEIGHT=0.3
MARKETAUX_HIGH_IMPACT_ONLY=false

# Trading Configuration
TRADING_OFFLINE_VALIDATION_THRESHOLD=0.3
TRADING_SYMBOLS=["EURUSD","GBPUSD"]

# Cache Configuration
TRADING_CACHE_ENABLED=true
TRADING_CACHE_SIMILARITY_THRESHOLD=0.85
TRADING_CACHE_TTL_MINUTES=60
TRADING_CACHE_SIZE_MB=500

# Rate Limiting Configuration
GPT_MODEL=gpt-4o-mini
OPENAI_TIER=tier_1
RATE_LIMIT_SAFETY_MARGIN=0.8
LOG_GPT_REQUESTS=true

# Production Settings
TRADING_COUNCIL_AGENT_DELAY=0.75
TRADING_COUNCIL_QUICK_MODE=false
TRADING_SYMBOL_PROCESSING_DELAY=2.0

# Error Handling
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT=300