# Development Requirements - Pinned Versions
# ==========================================
# This file contains exact version pins for all development dependencies.
# These versions have been tested and work well together for development.
#
# Generated: 2025-01-08
# Source: requirements-dev.txt
#
# Usage:
# - For reproducible dev environment: pip install -r requirements-pinned.txt -r requirements-dev-pinned.txt
# - To update: pip install --upgrade -r requirements-dev.txt && pip freeze

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
coverage==7.3.4

# Code quality
black==23.12.1
flake8==6.1.0
mypy==1.7.1
isort==5.13.2
pylint==3.0.3

# Type stubs for better mypy support
types-requests==*********
types-PyYAML==*********
pandas-stubs==2.0.3.230814

# Security scanning
bandit==1.7.5
safety==3.0.1
pip-audit==2.6.1

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==2.0.0
myst-parser==2.0.0

# Development tools
ipython==8.18.1
jupyter==1.0.0
notebook==7.0.6
ipykernel==6.27.1

# Pre-commit hooks
pre-commit==3.6.0

# Performance profiling
line-profiler==4.1.2
memory-profiler==0.61.0

# Debugging
ipdb==0.13.13
pudb==2023.1