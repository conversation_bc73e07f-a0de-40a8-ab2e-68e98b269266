@echo off
REM Fix MT5 crash and Edge WebView issues

echo ========================================
echo     MT5 Crash Fix - Edge WebView
echo ========================================
echo.

REM Run as administrator check
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo [1/4] Creating MT5 temp directories with proper permissions...

REM Create temp directories
mkdir "C:\ProgramData\MetaTrader5" 2>nul
mkdir "C:\ProgramData\MetaTrader5\temp" 2>nul
mkdir "C:\ProgramData\MetaTrader5\temp\EBWebView" 2>nul

REM Set permissions
icacls "C:\ProgramData\MetaTrader5" /grant Everyone:F /T >nul 2>&1
echo Done.

echo.
echo [2/4] Clearing MT5 cache...

REM Clear MT5 cache from user profile
set MT5_CACHE=%APPDATA%\MetaQuotes\Terminal
if exist "%MT5_CACHE%\Common\Cache" (
    rd /s /q "%MT5_CACHE%\Common\Cache" 2>nul
    echo Cache cleared.
) else (
    echo No cache found.
)

echo.
echo [3/4] Setting compatibility mode...

REM Find MT5 installation
set MT5_EXE=
if exist "C:\Program Files\MetaTrader 5\terminal64.exe" set MT5_EXE=C:\Program Files\MetaTrader 5\terminal64.exe
if exist "C:\Program Files (x86)\MetaTrader 5\terminal64.exe" set MT5_EXE=C:\Program Files (x86)\MetaTrader 5\terminal64.exe

if defined MT5_EXE (
    reg add "HKCU\Software\Microsoft\Windows NT\CurrentVersion\AppCompatFlags\Layers" /v "%MT5_EXE%" /t REG_SZ /d "~ RUNASADMIN" /f >nul 2>&1
    echo Compatibility mode set.
) else (
    echo MT5 not found in standard locations.
)

echo.
echo [4/4] Creating MT5 startup script...

REM Create a startup script that sets environment variables
echo @echo off > "%USERPROFILE%\Desktop\MT5_Fixed.bat"
echo REM MT5 with Edge WebView fix >> "%USERPROFILE%\Desktop\MT5_Fixed.bat"
echo set WEBVIEW2_ADDITIONAL_BROWSER_ARGUMENTS=--no-sandbox >> "%USERPROFILE%\Desktop\MT5_Fixed.bat"
echo set WEBVIEW2_USER_DATA_FOLDER=%%TEMP%%\MT5WebView >> "%USERPROFILE%\Desktop\MT5_Fixed.bat"
echo start "" "%MT5_EXE%" >> "%USERPROFILE%\Desktop\MT5_Fixed.bat"

echo.
echo ========================================
echo            Fix Applied!
echo ========================================
echo.
echo Next steps:
echo 1. Close MT5 completely if it's running
echo 2. Use the new "MT5_Fixed.bat" on your Desktop to start MT5
echo 3. If it still crashes, restart your computer and try again
echo.
echo Alternative solution:
echo - Run MT5 as Administrator (right-click → Run as administrator)
echo.
pause