"""
Unified Cache Implementation
Provides a centralized caching layer with multiple backends and consistency guarantees.
"""

import asyncio
import json
import pickle
import time
from typing import Any, Optional, Dict, List, Callable, Union
from datetime import datetime, timezone, timedelta
from pathlib import Path
from enum import Enum
import hashlib
import redis
from abc import ABC, abstractmethod

from core.utils.structured_logger import get_logger
from core.utils.error_decorator import handle_errors
from core.services.data_consistency_service import (
    DataConsistencyService, DataType, get_data_consistency_service
)

logger = get_logger(__name__)


class CacheBackend(Enum):
    """Available cache backends"""
    MEMORY = "memory"
    REDIS = "redis"
    FILE = "file"
    HYBRID = "hybrid"


class CacheEntry:
    """Represents a cached item"""
    
    def __init__(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        tags: Optional[List[str]] = None
    ):
        self.key = key
        self.value = value
        self.created_at = time.time()
        self.accessed_at = self.created_at
        self.access_count = 0
        self.ttl = ttl
        self.tags = tags or []
    
    def is_expired(self) -> bool:
        """Check if entry has expired"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    def touch(self):
        """Update access time and count"""
        self.accessed_at = time.time()
        self.access_count += 1


class ICacheBackend(ABC):
    """Interface for cache backends"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get value by key"""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """Set value with optional TTL"""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete key"""
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """Check if key exists"""
        pass
    
    @abstractmethod
    async def clear(self):
        """Clear all entries"""
        pass
    
    @abstractmethod
    async def get_stats(self) -> Dict[str, Any]:
        """Get backend statistics"""
        pass


class MemoryCacheBackend(ICacheBackend):
    """In-memory cache backend"""
    
    def __init__(self, max_entries: int = 10000):
        self.max_entries = max_entries
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        async with self._lock:
            entry = self._cache.get(key)
            if entry:
                if entry.is_expired():
                    del self._cache[key]
                    return None
                entry.touch()
                return entry.value
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None):
        async with self._lock:
            self._cache[key] = CacheEntry(key, value, ttl)
            
            # Evict if necessary
            if len(self._cache) > self.max_entries:
                await self._evict()
    
    async def delete(self, key: str) -> bool:
        async with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
        return False
    
    async def exists(self, key: str) -> bool:
        async with self._lock:
            entry = self._cache.get(key)
            if entry and not entry.is_expired():
                return True
        return False
    
    async def clear(self):
        async with self._lock:
            self._cache.clear()
    
    async def get_stats(self) -> Dict[str, Any]:
        async with self._lock:
            total = len(self._cache)
            expired = sum(1 for e in self._cache.values() if e.is_expired())
            
            return {
                'total_entries': total,
                'active_entries': total - expired,
                'expired_entries': expired,
                'backend': 'memory'
            }
    
    async def _evict(self):
        """Evict least recently used entries"""
        # Remove expired first
        expired_keys = [k for k, v in self._cache.items() if v.is_expired()]
        for key in expired_keys:
            del self._cache[key]
        
        # If still over limit, remove LRU
        if len(self._cache) > self.max_entries:
            entries = sorted(
                self._cache.items(),
                key=lambda x: x[1].accessed_at
            )
            
            to_remove = len(entries) - int(self.max_entries * 0.9)
            for key, _ in entries[:to_remove]:
                del self._cache[key]


class RedisCacheBackend(ICacheBackend):
    """Redis cache backend"""
    
    def __init__(
        self,
        host: str = 'localhost',
        port: int = 6379,
        db: int = 0,
        password: Optional[str] = None
    ):
        self.redis_client = None
        self.connection_params = {
            'host': host,
            'port': port,
            'db': db,
            'password': password,
            'decode_responses': False  # We'll handle encoding/decoding
        }
    
    async def _get_client(self):
        """Get or create Redis client"""
        if self.redis_client is None:
            import aioredis
            self.redis_client = await aioredis.create_redis_pool(
                f"redis://{self.connection_params['host']}:{self.connection_params['port']}",
                password=self.connection_params['password'],
                db=self.connection_params['db']
            )
        return self.redis_client
    
    async def get(self, key: str) -> Optional[Any]:
        try:
            client = await self._get_client()
            data = await client.get(key)
            if data:
                return pickle.loads(data)
        except Exception as e:
            logger.error(f"Redis get error: {e}")
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None):
        try:
            client = await self._get_client()
            data = pickle.dumps(value)
            if ttl:
                await client.setex(key, ttl, data)
            else:
                await client.set(key, data)
        except Exception as e:
            logger.error(f"Redis set error: {e}")
    
    async def delete(self, key: str) -> bool:
        try:
            client = await self._get_client()
            result = await client.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Redis delete error: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        try:
            client = await self._get_client()
            return await client.exists(key) > 0
        except Exception as e:
            logger.error(f"Redis exists error: {e}")
            return False
    
    async def clear(self):
        try:
            client = await self._get_client()
            await client.flushdb()
        except Exception as e:
            logger.error(f"Redis clear error: {e}")
    
    async def get_stats(self) -> Dict[str, Any]:
        try:
            client = await self._get_client()
            info = await client.info()
            return {
                'backend': 'redis',
                'used_memory': info.get('used_memory_human', 'unknown'),
                'connected_clients': info.get('connected_clients', 0),
                'total_keys': await client.dbsize()
            }
        except Exception as e:
            logger.error(f"Redis stats error: {e}")
            return {'backend': 'redis', 'error': str(e)}


class FileCacheBackend(ICacheBackend):
    """File-based cache backend"""
    
    def __init__(self, cache_dir: Path):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self._index_file = self.cache_dir / "_index.json"
        self._index = self._load_index()
    
    def _load_index(self) -> Dict[str, Dict[str, Any]]:
        """Load cache index"""
        if self._index_file.exists():
            try:
                return json.loads(self._index_file.read_text())
            except Exception as e:
                logger.error(f"Failed to load cache index: {e}")
        return {}
    
    def _save_index(self):
        """Save cache index"""
        try:
            self._index_file.write_text(json.dumps(self._index, indent=2))
        except Exception as e:
            logger.error(f"Failed to save cache index: {e}")
    
    def _get_file_path(self, key: str) -> Path:
        """Get file path for key"""
        # Hash key to avoid filesystem issues
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.pkl"
    
    async def get(self, key: str) -> Optional[Any]:
        # Check index
        if key in self._index:
            info = self._index[key]
            
            # Check expiry
            if info.get('expires_at'):
                if time.time() > info['expires_at']:
                    await self.delete(key)
                    return None
            
            # Load from file
            file_path = self._get_file_path(key)
            if file_path.exists():
                try:
                    with open(file_path, 'rb') as f:
                        return pickle.load(f)
                except Exception as e:
                    logger.error(f"Failed to load cache file: {e}")
        
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None):
        file_path = self._get_file_path(key)
        
        # Save to file
        try:
            with open(file_path, 'wb') as f:
                pickle.dump(value, f)
            
            # Update index
            self._index[key] = {
                'created_at': time.time(),
                'expires_at': time.time() + ttl if ttl else None,
                'file': file_path.name
            }
            self._save_index()
            
        except Exception as e:
            logger.error(f"Failed to save cache file: {e}")
    
    async def delete(self, key: str) -> bool:
        if key in self._index:
            file_path = self._get_file_path(key)
            
            # Delete file
            if file_path.exists():
                file_path.unlink()
            
            # Update index
            del self._index[key]
            self._save_index()
            
            return True
        return False
    
    async def exists(self, key: str) -> bool:
        if key in self._index:
            info = self._index[key]
            
            # Check expiry
            if info.get('expires_at'):
                if time.time() > info['expires_at']:
                    return False
            
            # Check file exists
            return self._get_file_path(key).exists()
        
        return False
    
    async def clear(self):
        # Delete all cache files
        for file_path in self.cache_dir.glob("*.pkl"):
            file_path.unlink()
        
        # Clear index
        self._index.clear()
        self._save_index()
    
    async def get_stats(self) -> Dict[str, Any]:
        total_size = sum(f.stat().st_size for f in self.cache_dir.glob("*.pkl"))
        
        return {
            'backend': 'file',
            'total_entries': len(self._index),
            'total_size_mb': total_size / 1024 / 1024,
            'cache_dir': str(self.cache_dir)
        }


class UnifiedCache:
    """
    Unified caching system with multiple backend support.
    Integrates with data consistency service for coherent data management.
    """
    
    def __init__(
        self,
        backend: CacheBackend = CacheBackend.MEMORY,
        backend_config: Optional[Dict[str, Any]] = None,
        consistency_service: Optional[DataConsistencyService] = None
    ):
        self.backend_type = backend
        self.backend = self._create_backend(backend, backend_config or {})
        self.consistency_service = consistency_service or get_data_consistency_service()
        
        # Cache statistics
        self._hits = 0
        self._misses = 0
        self._sets = 0
        
        # Invalidation callbacks
        self._invalidation_callbacks: List[Callable] = []
    
    def _create_backend(
        self,
        backend: CacheBackend,
        config: Dict[str, Any]
    ) -> ICacheBackend:
        """Create cache backend"""
        if backend == CacheBackend.MEMORY:
            return MemoryCacheBackend(
                max_entries=config.get('max_entries', 10000)
            )
        
        elif backend == CacheBackend.REDIS:
            return RedisCacheBackend(
                host=config.get('host', 'localhost'),
                port=config.get('port', 6379),
                db=config.get('db', 0),
                password=config.get('password')
            )
        
        elif backend == CacheBackend.FILE:
            return FileCacheBackend(
                cache_dir=Path(config.get('cache_dir', 'data/file_cache'))
            )
        
        else:
            raise ValueError(f"Unsupported backend: {backend}")
    
    @handle_errors(
        component="UnifiedCache",
        operation="get",
        retryable=True,
        default_return=None
    )
    async def get(
        self,
        key: str,
        data_type: Optional[DataType] = None,
        use_consistency: bool = True
    ) -> Optional[Any]:
        """
        Get value from cache with optional consistency check.
        
        Args:
            key: Cache key
            data_type: Data type for consistency service
            use_consistency: Whether to check consistency service
            
        Returns:
            Cached value or None
        """
        # Try cache first
        value = await self.backend.get(key)
        
        if value is not None:
            self._hits += 1
            logger.debug(f"Cache hit for {key}")
            return value
        
        self._misses += 1
        
        # Try consistency service if enabled
        if use_consistency and data_type:
            result = await self.consistency_service.get_data(data_type, key)
            if result:
                value, version = result
                
                # Store in cache for next time
                await self.set(key, value, use_consistency=False)
                
                return value
        
        return None
    
    @handle_errors(
        component="UnifiedCache",
        operation="set",
        retryable=True
    )
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        data_type: Optional[DataType] = None,
        source: str = "cache",
        use_consistency: bool = True
    ):
        """
        Set value in cache with optional consistency tracking.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            data_type: Data type for consistency service
            source: Source of the data
            use_consistency: Whether to update consistency service
        """
        # Store in backend
        await self.backend.set(key, value, ttl)
        self._sets += 1
        
        # Update consistency service if enabled
        if use_consistency and data_type:
            await self.consistency_service.set_data(
                data_type=data_type,
                identifier=key,
                value=value,
                source=source,
                ttl_seconds=ttl
            )
        
        # Trigger invalidation callbacks
        await self._trigger_invalidation(key, value)
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        return await self.backend.delete(key)
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        return await self.backend.exists(key)
    
    async def clear(self):
        """Clear all cache entries"""
        await self.backend.clear()
        logger.info("Cache cleared")
    
    async def invalidate_pattern(self, pattern: str):
        """Invalidate keys matching pattern"""
        # This would need to be implemented per backend
        # For now, log warning
        logger.warning(f"Pattern invalidation not implemented for {self.backend_type}")
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        backend_stats = await self.backend.get_stats()
        
        hit_rate = self._hits / (self._hits + self._misses) if (self._hits + self._misses) > 0 else 0
        
        return {
            'backend': self.backend_type.value,
            'hits': self._hits,
            'misses': self._misses,
            'sets': self._sets,
            'hit_rate': hit_rate,
            **backend_stats
        }
    
    def add_invalidation_callback(self, callback: Callable):
        """Add callback for cache invalidation events"""
        self._invalidation_callbacks.append(callback)
    
    async def _trigger_invalidation(self, key: str, value: Any):
        """Trigger invalidation callbacks"""
        for callback in self._invalidation_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(key, value)
                else:
                    callback(key, value)
            except Exception as e:
                logger.error(f"Invalidation callback error: {e}")
    
    # Convenience methods for common data types
    
    async def get_market_data(self, symbol: str, timeframe: str) -> Optional[Any]:
        """Get market data from cache"""
        key = f"market:{symbol}:{timeframe}"
        return await self.get(key, DataType.MARKET_DATA)
    
    async def set_market_data(self, symbol: str, timeframe: str, data: Any, ttl: int = 60):
        """Cache market data"""
        key = f"market:{symbol}:{timeframe}"
        await self.set(key, data, ttl, DataType.MARKET_DATA, "mt5")
    
    async def get_trade(self, trade_id: str) -> Optional[Any]:
        """Get trade from cache"""
        key = f"trade:{trade_id}"
        return await self.get(key, DataType.TRADE_DATA)
    
    async def set_trade(self, trade_id: str, trade: Any, ttl: int = 3600):
        """Cache trade data"""
        key = f"trade:{trade_id}"
        await self.set(key, trade, ttl, DataType.TRADE_DATA, "database")


# Global instance
_unified_cache: Optional[UnifiedCache] = None


def get_unified_cache(
    backend: Optional[CacheBackend] = None,
    backend_config: Optional[Dict[str, Any]] = None
) -> UnifiedCache:
    """Get or create the global unified cache"""
    global _unified_cache
    
    if _unified_cache is None:
        _unified_cache = UnifiedCache(
            backend=backend or CacheBackend.MEMORY,
            backend_config=backend_config
        )
    
    return _unified_cache