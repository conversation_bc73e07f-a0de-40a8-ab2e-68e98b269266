# Risk Management Integration Guide

## Current Status

The risk management rules are configured but NOT YET automatically enforced in live trading. Here's how to ensure they're applied:

## 1. Manual Configuration (Currently Active)

Your `.env` file has been updated with:
```bash
# Risk Management Settings
RISK_PER_TRADE=1.0                              # ✅ Max 1% risk per trade
TRADING_RISK_PROFILE=prop_firm                  # ✅ Using prop firm rules
TRADING_MAX_DAILY_DRAWDOWN_PERCENT=5.0          # ✅ Max 5% daily DD
TRADING_MAX_TOTAL_DRAWDOWN_PERCENT=10.0         # ✅ Max 10% total DD
TRADING_MAX_LOSS_PER_TRADE_PERCENT=5.0          # ✅ Emergency stop at 5%
TRADING_EMERGENCY_STOP_ENABLED=true             # ✅ Emergency stops enabled
TRADING_TRAILING_STOP_ENABLED=true              # ✅ Trailing stops enabled
COUNCIL_CONFIDENCE_THRESHOLD=85.0               # ✅ High confidence required
```

## 2. Risk Controls in Effect

### Currently Active:
- **Confidence Filter**: Only trades with 85%+ confidence will be taken
- **Risk Per Trade**: Position sizing limited to 1% risk
- **Symbol List**: Trading EURUSD, GBPUSD, USDCAD, AUDUSD, XAUUSD, USOIL.cash

### NOT Yet Enforced Automatically:
- Daily drawdown limit (5%)
- Total drawdown limit (10%)
- Emergency stops
- Consecutive loss limits

## 3. How to Add Full Enforcement

To add automatic drawdown enforcement to live trading, you would need to:

1. **Import DrawdownManager** in `trading_loop.py`:
```python
from core.services.drawdown_manager import DrawdownManager
```

2. **Add to DependencyContainer**:
```python
def drawdown_manager(self) -> DrawdownManager:
    return self.get_or_create('drawdown_manager',
        lambda: DrawdownManager(
            trade_repository=self.trade_repository(),
            initial_balance=10000  # Your starting balance
        ))
```

3. **Check Before Each Trade** in `trading_orchestrator.py`:
```python
# Before generating signal
can_trade, reason = self.drawdown_manager.can_take_trade(symbol, signal)
if not can_trade:
    logger.warning(f"Trade blocked: {reason}")
    return
```

## 4. Current Risk Protection

Even without full automation, you have protection through:

1. **High Confidence Requirement (85%)**: This alone dramatically reduces bad trades
2. **Small Position Sizing (1%)**: Limits loss per trade
3. **Manual Monitoring**: Check your drawdown regularly

## 5. Testing Risk Limits

### Safe Backtesting:
```bash
# Test with prop firm rules
python run_safe_backtest.py --risk-profile prop_firm

# Test with different modes
python run_backtest.py --mode ml_only --days 30
```

### Monitor Performance:
```bash
# Analyze results
python analyze_poor_performance.py
```

## 6. Emergency Actions

If drawdown exceeds limits during live trading:

1. **Stop Trading Immediately**:
```bash
# Graceful shutdown
python graceful_stop.py
```

2. **Review All Positions**:
```bash
# Check open trades
python scripts/control_panel.py
```

3. **Analyze What Went Wrong**:
```bash
python analyze_poor_performance.py
```

## Summary

- ✅ Risk parameters are configured in settings
- ✅ High confidence threshold (85%) provides protection  
- ✅ Position sizing limited to 1% risk
- ⚠️ Drawdown limits need manual monitoring
- 📝 Full automation requires code integration

The current setup is MUCH safer than before (50% DD → max 10% DD) but requires you to monitor drawdown manually until full automation is added.