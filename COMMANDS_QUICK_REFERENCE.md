# GPT Trader V1 - Quick Reference Guide

## 🚀 Essential Commands

### Start Trading
```bash
# Windows
launcher.bat                    # Full system with all checks
start_trading.bat              # Quick start

# Direct Python
python trading_loop.py         # Main trading system
```

### Stop Trading Safely
```bash
# Create stop file (recommended)
echo > STOP_TRADING

# Or use script
python graceful_stop.py
```

### Dashboards
```bash
# Main Dashboard (http://localhost:8501)
start_unified_dashboard.bat
python scripts/run_unified_dashboard.py

# ML Monitor
start_ml_monitor.bat
python scripts/run_ml_monitor.py

# GPT Flow Monitor
python scripts/gpt_flow_dashboard.py
```

## 📊 Backtesting

```bash
# Standard backtest (auto mode)
python run_backtest.py

# Different modes
python run_backtest.py --mode full_council   # All 7 agents (accurate, expensive)
python run_backtest.py --mode simplified     # Cheaper models (balanced)
python run_backtest.py --mode ml_only       # ML only (free, needs models)

# Compare all modes
python run_backtest.py --compare-modes

# Specific symbols and days
python run_backtest.py --symbols EURUSD GBPUSD --days 90

# Advanced options
python run_backtest.py --walk-forward 5     # Walk-forward analysis
python run_backtest.py --optimize           # Parameter optimization
python run_backtest.py --position-trading   # Use D1/W1 timeframes

# Other backtest scripts
python run_safe_backtest.py                 # No side effects
python run_offline_backtest.py              # No live data needed
```

## 🧪 Testing & Validation

```bash
# MT5 Connection
python test_mt5_simple.py         # Basic test
python test_mt5_auth.py          # With authentication
python debug_mt5.py              # Debug issues

# Symbol Management
python find_oil_symbol.py         # Find symbols
python enable_recommended_symbols.py  # Enable defaults
python mt5_symbol_manager.py     # Symbol manager

# ML Testing
python test_simple_ml.py         # Test ML models
```

## 🤖 ML Model Training

```bash
# Production training
python scripts/train_ml_production.py

# With specific symbols
python scripts/train_ml_production.py --symbols EURUSD,GBPUSD

# Continuous improvement
python scripts/ml_continuous_improvement.py
```

## ⚙️ Configuration Files

### .env (Required)
```bash
OPENAI_API_KEY=your-key
MT5_FILES_DIR=C:/...path.../MQL5/Files
TELEGRAM_TOKEN=bot-token (optional)
TELEGRAM_CHAT_ID=chat-id (optional)
ML_ENABLED=false
RISK_PROFILE=moderate
```

### Risk Profiles
- `conservative`: 0.5% risk, 3% daily DD
- `moderate`: 1% risk, 5% daily DD
- `aggressive`: 1.5% risk, 7% daily DD
- `prop_firm`: Strict prop trading limits

### Trading Symbols
Edit `config/symbols.py`:
- Conservative: EURUSD, GBPUSD, USDJPY
- Moderate: AUDUSD, USDCAD, NZDUSD
- Aggressive: GBPJPY, XAUUSD, EURJPY

## 📁 Important Directories

```
/logs/              # System logs
/models/            # ML models
/backtest_results/  # Backtest reports
/screenshots/       # Chart screenshots
/data/              # Cache and data files
```

## 🚨 Emergency Commands

```bash
# Stop trading immediately
echo > STOP_TRADING

# Check system status
tail -f logs/trading_system.log

# Check MT5 state
check_mt5_state.bat

# Restart with safe settings
# Edit .env: RISK_PROFILE=conservative
python trading_loop.py
```

## 📈 Daily Workflow

1. **Start System**
   ```bash
   launcher.bat
   ```

2. **Open Dashboard**
   ```bash
   start_unified_dashboard.bat
   # Navigate to http://localhost:8501
   ```

3. **Monitor**
   - Check Telegram alerts
   - Watch dashboard metrics
   - Review open positions

4. **End of Day**
   ```bash
   echo > STOP_TRADING
   ```

## 🔍 Common Issues

**MT5 Not Connected:**
- Ensure MT5 terminal is running
- Check MT5_FILES_DIR path
- Run `python test_mt5_auth.py`

**Symbol Not Found:**
- Run `python find_oil_symbol.py`
- Check broker-specific names

**ML Not Working:**
- Set ML_ENABLED=true in .env
- Train models: `python scripts/train_ml_production.py`

## 📊 Key Metrics to Monitor

- **Drawdown**: Keep < 10% total, < 5% daily
- **Circuit Breaker**: Check status in dashboard
- **Margin Level**: Keep > 200%
- **Consecutive Losses**: Max 3
- **Risk per Trade**: 0.5-1.5% based on profile

## 🎯 Best Practices

1. Always test on demo first
2. Start with conservative risk profile
3. Monitor first few trades closely
4. Keep Telegram notifications on
5. Review logs daily
6. Backup database regularly
7. Update ML models weekly
8. Check news feed status
9. Verify market hours config
10. Use graceful shutdown

**Remember: Capital preservation > Profit generation**