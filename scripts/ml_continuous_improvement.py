#!/usr/bin/env python3
"""
ML Continuous Improvement System
Analyzes trading results and retrains models based on actual performance
"""

import asyncio
import logging
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import json

import sys
sys.path.append(str(Path(__file__).parent.parent))

from core.infrastructure.database.repositories import TradeRepository, SignalRepository
from core.infrastructure.database.ml_prediction_logger import MLPredictionLogger
from core.ml.continuous_improvement import ContinuousImprovementSystem
from core.domain.models import TradeStatus, TradeResult
from config.settings import get_settings
from scripts.train_ml_production import TradingPatternLearner

logger = logging.getLogger(__name__)


class MLPerformanceAnalyzer:
    """Analyzes ML prediction performance and identifies improvement areas"""
    
    def __init__(self):
        self.settings = get_settings()
        db_path = self.settings.database.db_path
        self.trade_repo = TradeRepository(db_path)
        self.signal_repo = SignalRepository(db_path)
        self.ml_logger = MLPredictionLogger(db_path)
        
    async def analyze_recent_performance(self, days: int = 30) -> Dict:
        """Analyze ML performance over recent period"""
        
        # Get recent ML predictions and their outcomes
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        predictions = await self.ml_logger.get_predictions_with_outcomes(
            start_date=start_date,
            end_date=end_date
        )
        
        if not predictions:
            return {
                'status': 'no_data',
                'message': 'No ML predictions found in the specified period'
            }
        
        # Analyze by symbol
        symbol_performance = {}
        
        for pred in predictions:
            symbol = pred['symbol']
            if symbol not in symbol_performance:
                symbol_performance[symbol] = {
                    'total': 0,
                    'correct': 0,
                    'profitable': 0,
                    'losses': 0,
                    'buy_accuracy': {'correct': 0, 'total': 0},
                    'sell_accuracy': {'correct': 0, 'total': 0},
                    'confidence_buckets': {}
                }
            
            stats = symbol_performance[symbol]
            stats['total'] += 1
            
            # Check if prediction was correct
            if pred['actual_outcome']:
                if pred['predicted_signal'] == pred['actual_outcome']:
                    stats['correct'] += 1
                
                # Track profitability
                if pred.get('profit_loss', 0) > 0:
                    stats['profitable'] += 1
                else:
                    stats['losses'] += 1
                
                # Track accuracy by signal type
                if pred['predicted_signal'] == 'BUY':
                    stats['buy_accuracy']['total'] += 1
                    if pred['actual_outcome'] == 'BUY' and pred.get('profit_loss', 0) > 0:
                        stats['buy_accuracy']['correct'] += 1
                elif pred['predicted_signal'] == 'SELL':
                    stats['sell_accuracy']['total'] += 1
                    if pred['actual_outcome'] == 'SELL' and pred.get('profit_loss', 0) > 0:
                        stats['sell_accuracy']['correct'] += 1
            
            # Track performance by confidence level
            confidence_bucket = int(pred['confidence'] / 10) * 10
            if confidence_bucket not in stats['confidence_buckets']:
                stats['confidence_buckets'][confidence_bucket] = {
                    'total': 0,
                    'profitable': 0
                }
            stats['confidence_buckets'][confidence_bucket]['total'] += 1
            if pred.get('profit_loss', 0) > 0:
                stats['confidence_buckets'][confidence_bucket]['profitable'] += 1
        
        # Calculate overall metrics
        total_predictions = sum(s['total'] for s in symbol_performance.values())
        total_correct = sum(s['correct'] for s in symbol_performance.values())
        total_profitable = sum(s['profitable'] for s in symbol_performance.values())
        
        return {
            'status': 'success',
            'period_days': days,
            'total_predictions': total_predictions,
            'overall_accuracy': (total_correct / total_predictions * 100) if total_predictions > 0 else 0,
            'profit_rate': (total_profitable / total_predictions * 100) if total_predictions > 0 else 0,
            'symbol_performance': symbol_performance,
            'recommendations': self._generate_recommendations(symbol_performance)
        }
    
    def _generate_recommendations(self, symbol_performance: Dict) -> List[str]:
        """Generate improvement recommendations based on performance"""
        recommendations = []
        
        for symbol, stats in symbol_performance.items():
            if stats['total'] < 10:
                continue
                
            accuracy = (stats['correct'] / stats['total'] * 100) if stats['total'] > 0 else 0
            profit_rate = (stats['profitable'] / stats['total'] * 100) if stats['total'] > 0 else 0
            
            # Check overall performance
            if profit_rate < 50:
                recommendations.append(f"{symbol}: Low profit rate ({profit_rate:.1f}%). Consider retraining with recent data.")
            
            # Check buy/sell imbalance
            buy_total = stats['buy_accuracy']['total']
            sell_total = stats['sell_accuracy']['total']
            if buy_total > 0 and sell_total > 0:
                buy_acc = stats['buy_accuracy']['correct'] / buy_total * 100
                sell_acc = stats['sell_accuracy']['correct'] / sell_total * 100
                
                if abs(buy_acc - sell_acc) > 20:
                    if buy_acc > sell_acc:
                        recommendations.append(f"{symbol}: SELL signals underperforming ({sell_acc:.1f}% vs {buy_acc:.1f}% for BUY)")
                    else:
                        recommendations.append(f"{symbol}: BUY signals underperforming ({buy_acc:.1f}% vs {sell_acc:.1f}% for SELL)")
            
            # Check confidence calibration
            for conf_level, conf_stats in stats['confidence_buckets'].items():
                if conf_stats['total'] >= 5:
                    conf_profit_rate = (conf_stats['profitable'] / conf_stats['total'] * 100)
                    if conf_level >= 70 and conf_profit_rate < 60:
                        recommendations.append(f"{symbol}: High confidence ({conf_level}%+) predictions have low success ({conf_profit_rate:.1f}%)")
        
        return recommendations


class AdaptiveMLTrainer:
    """Retrains ML models based on recent performance"""
    
    def __init__(self):
        self.pattern_learner = TradingPatternLearner()
        self.analyzer = MLPerformanceAnalyzer()
        
    async def adaptive_retrain(self, lookback_days: int = 90, performance_threshold: float = 55.0):
        """Retrain models that are underperforming"""
        
        logger.info("Starting adaptive ML retraining...")
        
        # Analyze recent performance
        performance = await self.analyzer.analyze_recent_performance(days=30)
        
        if performance['status'] != 'success':
            logger.error(f"Failed to analyze performance: {performance.get('message')}")
            return
        
        # Identify symbols that need retraining
        symbols_to_retrain = []
        
        for symbol, stats in performance['symbol_performance'].items():
            if stats['total'] < 20:  # Need minimum samples
                continue
                
            profit_rate = (stats['profitable'] / stats['total'] * 100) if stats['total'] > 0 else 0
            
            if profit_rate < performance_threshold:
                symbols_to_retrain.append({
                    'symbol': symbol,
                    'profit_rate': profit_rate,
                    'total_trades': stats['total']
                })
        
        if not symbols_to_retrain:
            logger.info("All models performing above threshold. No retraining needed.")
            return
        
        # Retrain underperforming models
        logger.info(f"Retraining {len(symbols_to_retrain)} underperforming models...")
        
        for item in symbols_to_retrain:
            symbol = item['symbol']
            logger.info(f"Retraining {symbol} (current profit rate: {item['profit_rate']:.1f}%)")
            
            # Use more recent data for retraining
            end_date = datetime.now()
            start_date = end_date - timedelta(days=lookback_days)
            
            try:
                result = await self.pattern_learner.learn_from_patterns(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date
                )
                
                if result['status'] == 'success':
                    logger.info(f"✓ Successfully retrained {symbol}")
                    logger.info(f"  New model performance: {result['performance']}")
                else:
                    logger.warning(f"✗ Failed to retrain {symbol}: {result.get('reason', 'Unknown error')}")
                    
            except Exception as e:
                logger.error(f"Error retraining {symbol}: {e}")
        
        logger.info("Adaptive retraining completed.")


class MLFeedbackLoop:
    """Implements continuous learning from trading results"""
    
    def __init__(self):
        self.settings = get_settings()
        self.improvement_system = ContinuousImprovementSystem(
            ml_logger=MLPredictionLogger(self.settings.database.db_path),
            model_dir=Path("models"),
            min_samples_for_update=50
        )
        
    async def update_from_recent_trades(self, days: int = 7):
        """Update models based on recent trading results"""
        
        logger.info(f"Updating ML models from last {days} days of trading...")
        
        # Get recent trades with ML predictions
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Process feedback
        updates = await self.improvement_system.process_feedback_batch(
            start_date=start_date,
            end_date=end_date
        )
        
        if updates:
            logger.info(f"Processed {len(updates)} feedback updates")
            
            # Generate report
            report = {
                'timestamp': datetime.now().isoformat(),
                'period_days': days,
                'updates_processed': len(updates),
                'models_updated': list(set(u['symbol'] for u in updates)),
                'average_confidence_shift': np.mean([u.get('confidence_adjustment', 0) for u in updates])
            }
            
            # Save report
            report_path = Path("reports/ml_feedback_report.json")
            report_path.parent.mkdir(exist_ok=True)
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=2)
                
            logger.info(f"Feedback report saved to {report_path}")
        else:
            logger.info("No updates to process")


async def main():
    """Run ML improvement pipeline"""
    
    import argparse
    parser = argparse.ArgumentParser(description='ML Continuous Improvement System')
    parser.add_argument('--analyze', action='store_true', help='Analyze recent ML performance')
    parser.add_argument('--retrain', action='store_true', help='Retrain underperforming models')
    parser.add_argument('--feedback', action='store_true', help='Update models from recent trades')
    parser.add_argument('--days', type=int, default=30, help='Days to look back')
    parser.add_argument('--threshold', type=float, default=55.0, help='Performance threshold for retraining')
    
    args = parser.parse_args()
    
    if args.analyze:
        analyzer = MLPerformanceAnalyzer()
        performance = await analyzer.analyze_recent_performance(days=args.days)
        
        print("\n" + "="*60)
        print("ML PERFORMANCE ANALYSIS")
        print("="*60)
        print(f"Period: Last {args.days} days")
        print(f"Total Predictions: {performance.get('total_predictions', 0)}")
        print(f"Overall Accuracy: {performance.get('overall_accuracy', 0):.1f}%")
        print(f"Profit Rate: {performance.get('profit_rate', 0):.1f}%")
        
        print("\nPerformance by Symbol:")
        for symbol, stats in performance.get('symbol_performance', {}).items():
            profit_rate = (stats['profitable'] / stats['total'] * 100) if stats['total'] > 0 else 0
            print(f"  {symbol}: {stats['total']} trades, {profit_rate:.1f}% profitable")
        
        print("\nRecommendations:")
        for rec in performance.get('recommendations', []):
            print(f"  - {rec}")
    
    if args.retrain:
        trainer = AdaptiveMLTrainer()
        await trainer.adaptive_retrain(
            lookback_days=args.days,
            performance_threshold=args.threshold
        )
    
    if args.feedback:
        feedback_loop = MLFeedbackLoop()
        await feedback_loop.update_from_recent_trades(days=args.days)
    
    if not any([args.analyze, args.retrain, args.feedback]):
        print("Please specify an action: --analyze, --retrain, or --feedback")
        parser.print_help()


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    asyncio.run(main())