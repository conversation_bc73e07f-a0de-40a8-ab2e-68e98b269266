"""
Cost-effective backtesting configuration for GPT Trading System.
Uses cheaper models and optimized prompts to reduce costs by 90%+ while maintaining accuracy.
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import logging

from core.domain.enums import GPTModels

logger = logging.getLogger(__name__)


class BacktestModelTier(Enum):
    """Model tiers for backtesting with cost/performance tradeoffs"""
    ULTRA_LOW_COST = "gpt-3.5-turbo"  # $0.0015/1K input, $0.002/1K output
    LOW_COST = "gpt-3.5-turbo-1106"  # Latest 3.5 with better performance
    BALANCED = "gpt-4o-mini"  # Good balance of cost/performance
    HIGH_ACCURACY = "gpt-4-turbo-preview"  # For critical decisions only
    PRODUCTION = "gpt-4o"  # Full production model (expensive)


@dataclass
class BacktestModelConfig:
    """Configuration for a specific model tier"""
    model_name: str
    max_tokens: int = 500  # Reduced for cost savings
    temperature: float = 0.1  # Low for consistency
    use_structured_output: bool = True  # Force JSON responses
    prompt_style: str = "concise"  # concise, balanced, detailed
    batch_size: int = 1  # Number of decisions per API call
    cache_responses: bool = True  # Cache similar market conditions
    
    # Cost tracking
    cost_per_1k_input: float = 0.0
    cost_per_1k_output: float = 0.0
    
    def __post_init__(self):
        """Set cost based on model"""
        costs = {
            "gpt-3.5-turbo": (0.0015, 0.002),
            "gpt-3.5-turbo-1106": (0.001, 0.002),
            "gpt-4o-mini": (0.015, 0.06),
            "gpt-4-turbo-preview": (0.01, 0.03),
            "gpt-4o": (0.005, 0.015)
        }
        if self.model_name in costs:
            self.cost_per_1k_input, self.cost_per_1k_output = costs[self.model_name]


@dataclass
class BacktestGPTConfig:
    """GPT configuration optimized for backtesting"""
    
    # Model selection by agent type
    agent_models: Dict[str, str] = field(default_factory=lambda: {
        "technical_analyst": BacktestModelTier.ULTRA_LOW_COST.value,
        "fundamental_analyst": BacktestModelTier.ULTRA_LOW_COST.value,
        "sentiment_reader": BacktestModelTier.ULTRA_LOW_COST.value,
        "momentum_trader": BacktestModelTier.ULTRA_LOW_COST.value,
        "contrarian_trader": BacktestModelTier.ULTRA_LOW_COST.value,
        "risk_manager": BacktestModelTier.LOW_COST.value,  # Slightly better for risk
        "head_trader": BacktestModelTier.LOW_COST.value  # Better for synthesis
    })
    
    # Simplified prompts for backtesting
    use_simplified_prompts: bool = True
    
    # Batch processing settings
    enable_batching: bool = True
    batch_size: int = 5  # Process 5 market states per API call
    
    # Response caching
    enable_response_cache: bool = True
    cache_similarity_threshold: float = 0.95  # High threshold for exact matches
    cache_ttl_hours: int = 168  # 1 week cache for backtesting
    
    # Cost optimization settings
    max_tokens_per_agent: int = 300  # Reduced from 2000+
    skip_debates: bool = True  # Skip multi-round debates in backtest
    use_structured_outputs: bool = True  # Force JSON for easy parsing
    
    # Parallel processing
    max_parallel_requests: int = 10  # Process multiple symbols/times in parallel
    
    # Cost tracking
    track_costs: bool = True
    cost_alert_threshold: float = 10.0  # Alert if session exceeds $10
    
    # Model fallback strategy
    fallback_on_error: bool = True
    fallback_model: str = BacktestModelTier.ULTRA_LOW_COST.value
    
    # Accuracy vs cost tradeoff
    accuracy_threshold: float = 0.85  # Target 85% accuracy vs production
    
    def get_model_config(self, agent_type: str) -> BacktestModelConfig:
        """Get model configuration for specific agent"""
        model_name = self.agent_models.get(agent_type, self.fallback_model)
        
        # Adjust settings based on agent importance
        if agent_type == "risk_manager":
            return BacktestModelConfig(
                model_name=model_name,
                max_tokens=400,  # Slightly more for risk analysis
                temperature=0.05,  # Very low for consistency
                prompt_style="balanced"
            )
        elif agent_type == "head_trader":
            return BacktestModelConfig(
                model_name=model_name,
                max_tokens=350,
                temperature=0.1,
                prompt_style="balanced"
            )
        else:
            return BacktestModelConfig(
                model_name=model_name,
                max_tokens=250,  # Minimal for other agents
                temperature=0.1,
                prompt_style="concise"
            )


class BacktestPromptTemplates:
    """Simplified prompt templates for backtesting"""
    
    # Ultra-concise technical analysis prompt
    TECHNICAL_CONCISE = """Analyze {symbol} H1 chart:
Current: {current_price}
H1 Data: {h1_summary}
H4 Context: {h4_summary}

Return JSON only:
{{
  "signal": "BUY/SELL/WAIT",
  "confidence": 0-100,
  "entry": price,
  "sl": price,
  "tp": price,
  "reasons": ["reason1", "reason2", "reason3"]
}}"""

    # Simplified fundamental analysis
    FUNDAMENTAL_CONCISE = """News analysis for {symbol}:
Current: {current_price}
News: {news_summary}

Return JSON only:
{{
  "signal": "BUY/SELL/WAIT", 
  "confidence": 0-100,
  "impact": "positive/negative/neutral",
  "reasons": ["reason1", "reason2"]
}}"""

    # Batch analysis prompt (multiple symbols/times)
    BATCH_ANALYSIS = """Analyze multiple scenarios:
{scenarios_json}

Return JSON array with signal for each scenario:
[
  {{"id": 1, "signal": "BUY/SELL/WAIT", "confidence": 0-100}},
  {{"id": 2, "signal": "BUY/SELL/WAIT", "confidence": 0-100}}
]"""

    # Risk analysis simplified
    RISK_CONCISE = """Risk check for {symbol}:
Signal: {signal_type}
Entry: {entry_price}
SL: {stop_loss}
TP: {take_profit}
Current risk: {open_trades}/{max_trades} trades

Return JSON only:
{{
  "approved": true/false,
  "risk_score": 0-100,
  "concerns": ["concern1", "concern2"]
}}"""


@dataclass 
class BacktestCostTracker:
    """Track costs during backtesting"""
    total_requests: int = 0
    total_input_tokens: int = 0
    total_output_tokens: int = 0
    total_cost: float = 0.0
    cost_by_agent: Dict[str, float] = field(default_factory=dict)
    cost_by_model: Dict[str, float] = field(default_factory=dict)
    cache_hits: int = 0
    cache_misses: int = 0
    
    def add_request(
        self, 
        agent_type: str,
        model: str,
        input_tokens: int,
        output_tokens: int,
        cached: bool = False
    ):
        """Track a single request"""
        self.total_requests += 1
        
        if cached:
            self.cache_hits += 1
            return  # No cost for cached responses
        
        self.cache_misses += 1
        self.total_input_tokens += input_tokens
        self.total_output_tokens += output_tokens
        
        # Calculate cost
        model_config = BacktestModelConfig(model_name=model)
        input_cost = (input_tokens / 1000) * model_config.cost_per_1k_input
        output_cost = (output_tokens / 1000) * model_config.cost_per_1k_output
        request_cost = input_cost + output_cost
        
        self.total_cost += request_cost
        self.cost_by_agent[agent_type] = self.cost_by_agent.get(agent_type, 0) + request_cost
        self.cost_by_model[model] = self.cost_by_model.get(model, 0) + request_cost
    
    def get_summary(self) -> Dict[str, Any]:
        """Get cost summary"""
        cache_rate = (self.cache_hits / max(1, self.total_requests)) * 100
        
        return {
            "total_requests": self.total_requests,
            "total_cost": round(self.total_cost, 4),
            "average_cost_per_request": round(self.total_cost / max(1, self.total_requests), 4),
            "total_tokens": self.total_input_tokens + self.total_output_tokens,
            "cache_hit_rate": round(cache_rate, 2),
            "cost_by_agent": {k: round(v, 4) for k, v in self.cost_by_agent.items()},
            "cost_by_model": {k: round(v, 4) for k, v in self.cost_by_model.items()},
            "cost_savings_vs_production": self._calculate_savings()
        }
    
    def _calculate_savings(self) -> float:
        """Calculate savings vs using production models"""
        # Estimate production cost (GPT-4 for all agents)
        production_cost = (
            (self.total_input_tokens / 1000) * 0.03 +  # GPT-4 input cost
            (self.total_output_tokens / 1000) * 0.06   # GPT-4 output cost
        )
        
        savings_percent = ((production_cost - self.total_cost) / max(0.01, production_cost)) * 100
        return round(savings_percent, 2)


# Preset configurations for different use cases
BACKTEST_PRESETS = {
    "ultra_low_cost": BacktestGPTConfig(
        agent_models={agent: BacktestModelTier.ULTRA_LOW_COST.value for agent in [
            "technical_analyst", "fundamental_analyst", "sentiment_reader",
            "momentum_trader", "contrarian_trader", "risk_manager", "head_trader"
        ]},
        max_tokens_per_agent=200,
        skip_debates=True,
        enable_batching=True,
        batch_size=10
    ),
    
    "balanced": BacktestGPTConfig(
        agent_models={
            "technical_analyst": BacktestModelTier.ULTRA_LOW_COST.value,
            "fundamental_analyst": BacktestModelTier.ULTRA_LOW_COST.value,
            "sentiment_reader": BacktestModelTier.ULTRA_LOW_COST.value,
            "momentum_trader": BacktestModelTier.ULTRA_LOW_COST.value,
            "contrarian_trader": BacktestModelTier.ULTRA_LOW_COST.value,
            "risk_manager": BacktestModelTier.LOW_COST.value,
            "head_trader": BacktestModelTier.BALANCED.value
        },
        max_tokens_per_agent=300,
        skip_debates=True,
        enable_batching=True,
        batch_size=5
    ),
    
    "high_accuracy": BacktestGPTConfig(
        agent_models={
            "technical_analyst": BacktestModelTier.LOW_COST.value,
            "fundamental_analyst": BacktestModelTier.LOW_COST.value,
            "sentiment_reader": BacktestModelTier.LOW_COST.value,
            "momentum_trader": BacktestModelTier.LOW_COST.value,
            "contrarian_trader": BacktestModelTier.LOW_COST.value,
            "risk_manager": BacktestModelTier.BALANCED.value,
            "head_trader": BacktestModelTier.BALANCED.value
        },
        max_tokens_per_agent=400,
        skip_debates=False,
        enable_batching=True,
        batch_size=3
    )
}


def get_backtest_config(preset: str = "ultra_low_cost") -> BacktestGPTConfig:
    """Get a preset backtesting configuration"""
    if preset not in BACKTEST_PRESETS:
        logger.warning(f"Unknown preset {preset}, using ultra_low_cost")
        preset = "ultra_low_cost"
    
    config = BACKTEST_PRESETS[preset]
    logger.info(f"Using backtest preset: {preset}")
    
    # Log estimated cost reduction
    prod_tokens_estimate = 2000  # Average tokens per decision in production
    backtest_tokens_estimate = config.max_tokens_per_agent * 2  # Input + output
    
    cost_reduction = (1 - (backtest_tokens_estimate / prod_tokens_estimate)) * 100
    logger.info(f"Estimated token reduction: {cost_reduction:.1f}%")
    
    return config