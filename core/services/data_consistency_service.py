"""
Unified Data Consistency Service
Ensures data consistency across all system components with versioning and conflict resolution.
"""

import asyncio
import hashlib
import json
import threading
from typing import Dict, Any, Optional, List, Tuple, Set
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass, field
from enum import Enum
import pickle
from pathlib import Path

from core.domain.models import MarketData, Candle, Trade, TradingSignal
from core.domain.exceptions import DataConsistencyError, ErrorContext
from core.utils.structured_logger import get_logger
from core.utils.error_decorator import handle_errors, handle_database_errors
from core.utils.resource_managers import CacheManager

logger = get_logger(__name__)


class DataType(Enum):
    """Types of data managed by the consistency service"""
    MARKET_DATA = "market_data"
    TRADE_DATA = "trade_data"
    SIGNAL_DATA = "signal_data"
    NEWS_DATA = "news_data"
    ACCOUNT_DATA = "account_data"
    CONFIG_DATA = "config_data"


class ConflictResolutionStrategy(Enum):
    """Strategies for resolving data conflicts"""
    LATEST_WINS = "latest_wins"
    HIGHEST_VERSION_WINS = "highest_version_wins"
    SOURCE_PRIORITY = "source_priority"
    MANUAL_RESOLUTION = "manual_resolution"


@dataclass
class DataVersion:
    """Represents a version of data"""
    version: int
    timestamp: datetime
    source: str
    checksum: str
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DataEntry:
    """Entry in the data consistency layer"""
    key: str
    data_type: DataType
    value: Any
    version: DataVersion
    ttl_seconds: Optional[int] = None
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    accessed_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    access_count: int = 0


class DataConsistencyService:
    """
    Service for maintaining data consistency across the trading system.
    Provides single source of truth with versioning and conflict resolution.
    """
    
    def __init__(
        self,
        cache_dir: Path,
        max_memory_entries: int = 10000,
        default_ttl_seconds: int = 3600,
        enable_persistence: bool = True
    ):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.max_memory_entries = max_memory_entries
        self.default_ttl_seconds = default_ttl_seconds
        self.enable_persistence = enable_persistence
        
        # In-memory cache
        self._memory_cache: Dict[str, DataEntry] = {}
        self._lock = threading.RLock()
        
        # Version tracking
        self._version_counter: Dict[str, int] = {}
        
        # Source priorities for conflict resolution
        self._source_priorities = {
            "mt5": 1,
            "database": 2,
            "cache": 3,
            "calculation": 4
        }
        
        # Subscribers for data updates
        self._subscribers: Dict[DataType, List[callable]] = {
            dt: [] for dt in DataType
        }
        
        # Load persisted data if enabled
        if self.enable_persistence:
            self._load_persisted_data()
        
        # Start background cleanup task
        self._cleanup_task = None
        self._start_cleanup_task()
    
    def _generate_key(self, data_type: DataType, identifier: str) -> str:
        """Generate unique key for data entry"""
        return f"{data_type.value}:{identifier}"
    
    def _calculate_checksum(self, data: Any) -> str:
        """Calculate checksum for data"""
        try:
            # Serialize data for checksum
            if hasattr(data, 'to_dict'):
                data_bytes = json.dumps(data.to_dict(), sort_keys=True).encode()
            else:
                data_bytes = pickle.dumps(data)
            
            return hashlib.sha256(data_bytes).hexdigest()
        except Exception as e:
            logger.error(f"Failed to calculate checksum: {e}")
            return "error"
    
    @handle_errors(
        component="DataConsistencyService",
        operation="set_data",
        retryable=True
    )
    async def set_data(
        self,
        data_type: DataType,
        identifier: str,
        value: Any,
        source: str,
        ttl_seconds: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> DataVersion:
        """
        Set data with versioning and consistency checks.
        
        Args:
            data_type: Type of data being stored
            identifier: Unique identifier for the data
            value: The data value
            source: Source of the data
            ttl_seconds: Time to live in seconds
            metadata: Additional metadata
            
        Returns:
            DataVersion of the stored data
        """
        key = self._generate_key(data_type, identifier)
        
        with self._lock:
            # Get current version
            current_version = self._version_counter.get(key, 0)
            new_version = current_version + 1
            self._version_counter[key] = new_version
            
            # Create version info
            version = DataVersion(
                version=new_version,
                timestamp=datetime.now(timezone.utc),
                source=source,
                checksum=self._calculate_checksum(value),
                metadata=metadata or {}
            )
            
            # Check for conflicts
            if key in self._memory_cache:
                existing = self._memory_cache[key]
                if existing.version.checksum != version.checksum:
                    # Data has changed, resolve conflict
                    should_update = await self._resolve_conflict(
                        existing=existing,
                        new_value=value,
                        new_version=version
                    )
                    
                    if not should_update:
                        logger.info(f"Conflict resolution rejected update for {key}")
                        return existing.version
            
            # Create entry
            entry = DataEntry(
                key=key,
                data_type=data_type,
                value=value,
                version=version,
                ttl_seconds=ttl_seconds or self.default_ttl_seconds
            )
            
            # Store in memory
            self._memory_cache[key] = entry
            
            # Persist if enabled
            if self.enable_persistence:
                await self._persist_entry(entry)
            
            # Notify subscribers
            await self._notify_subscribers(data_type, identifier, value, version)
            
            # Enforce memory limit
            if len(self._memory_cache) > self.max_memory_entries:
                await self._evict_entries()
            
            logger.debug(f"Stored {key} v{new_version} from {source}")
            return version
    
    @handle_errors(
        component="DataConsistencyService",
        operation="get_data",
        retryable=True,
        default_return=None
    )
    async def get_data(
        self,
        data_type: DataType,
        identifier: str,
        max_age_seconds: Optional[int] = None
    ) -> Optional[Tuple[Any, DataVersion]]:
        """
        Get data with version information.
        
        Args:
            data_type: Type of data to retrieve
            identifier: Unique identifier
            max_age_seconds: Maximum age of data to accept
            
        Returns:
            Tuple of (data, version) or None if not found
        """
        key = self._generate_key(data_type, identifier)
        
        with self._lock:
            if key not in self._memory_cache:
                # Try to load from persistence
                if self.enable_persistence:
                    entry = await self._load_entry(key)
                    if entry:
                        self._memory_cache[key] = entry
                else:
                    return None
            
            if key in self._memory_cache:
                entry = self._memory_cache[key]
                
                # Check age if specified
                if max_age_seconds:
                    age = (datetime.now(timezone.utc) - entry.version.timestamp).total_seconds()
                    if age > max_age_seconds:
                        logger.debug(f"Data for {key} is too old ({age:.1f}s)")
                        return None
                
                # Check TTL
                if entry.ttl_seconds:
                    age = (datetime.now(timezone.utc) - entry.created_at).total_seconds()
                    if age > entry.ttl_seconds:
                        logger.debug(f"Data for {key} has expired")
                        del self._memory_cache[key]
                        return None
                
                # Update access info
                entry.accessed_at = datetime.now(timezone.utc)
                entry.access_count += 1
                
                return entry.value, entry.version
        
        return None
    
    async def get_latest(
        self,
        data_type: DataType,
        identifiers: List[str]
    ) -> Dict[str, Tuple[Any, DataVersion]]:
        """Get latest data for multiple identifiers"""
        results = {}
        
        for identifier in identifiers:
            data = await self.get_data(data_type, identifier)
            if data:
                results[identifier] = data
        
        return results
    
    async def _resolve_conflict(
        self,
        existing: DataEntry,
        new_value: Any,
        new_version: DataVersion,
        strategy: ConflictResolutionStrategy = ConflictResolutionStrategy.SOURCE_PRIORITY
    ) -> bool:
        """
        Resolve conflicts between existing and new data.
        
        Returns:
            True if new data should replace existing, False otherwise
        """
        if strategy == ConflictResolutionStrategy.LATEST_WINS:
            return new_version.timestamp > existing.version.timestamp
        
        elif strategy == ConflictResolutionStrategy.HIGHEST_VERSION_WINS:
            return new_version.version > existing.version.version
        
        elif strategy == ConflictResolutionStrategy.SOURCE_PRIORITY:
            existing_priority = self._source_priorities.get(existing.version.source, 99)
            new_priority = self._source_priorities.get(new_version.source, 99)
            
            # Lower number = higher priority
            if new_priority < existing_priority:
                return True
            elif new_priority == existing_priority:
                # Same priority, use timestamp
                return new_version.timestamp > existing.version.timestamp
            else:
                return False
        
        else:
            # Manual resolution - log conflict
            logger.warning(
                f"Data conflict requires manual resolution: "
                f"existing v{existing.version.version} from {existing.version.source} "
                f"vs new v{new_version.version} from {new_version.source}"
            )
            return False
    
    async def _persist_entry(self, entry: DataEntry):
        """Persist entry to disk"""
        try:
            filepath = self.cache_dir / f"{entry.key}.pkl"
            
            with open(filepath, 'wb') as f:
                pickle.dump(entry, f)
            
        except Exception as e:
            logger.error(f"Failed to persist entry {entry.key}: {e}")
    
    async def _load_entry(self, key: str) -> Optional[DataEntry]:
        """Load entry from disk"""
        try:
            filepath = self.cache_dir / f"{key}.pkl"
            
            if filepath.exists():
                with open(filepath, 'rb') as f:
                    return pickle.load(f)
            
        except Exception as e:
            logger.error(f"Failed to load entry {key}: {e}")
        
        return None
    
    def _load_persisted_data(self):
        """Load all persisted data on startup"""
        try:
            loaded = 0
            for filepath in self.cache_dir.glob("*.pkl"):
                try:
                    with open(filepath, 'rb') as f:
                        entry = pickle.load(f)
                        
                    # Check if expired
                    if entry.ttl_seconds:
                        age = (datetime.now(timezone.utc) - entry.created_at).total_seconds()
                        if age > entry.ttl_seconds:
                            filepath.unlink()
                            continue
                    
                    self._memory_cache[entry.key] = entry
                    loaded += 1
                    
                except Exception as e:
                    logger.error(f"Failed to load {filepath}: {e}")
            
            if loaded > 0:
                logger.info(f"Loaded {loaded} persisted entries")
                
        except Exception as e:
            logger.error(f"Failed to load persisted data: {e}")
    
    async def _evict_entries(self):
        """Evict least recently used entries"""
        with self._lock:
            # Sort by access time
            entries = sorted(
                self._memory_cache.items(),
                key=lambda x: x[1].accessed_at
            )
            
            # Remove oldest entries
            to_remove = len(entries) - int(self.max_memory_entries * 0.9)
            
            for key, _ in entries[:to_remove]:
                del self._memory_cache[key]
                
                # Remove persisted file
                if self.enable_persistence:
                    filepath = self.cache_dir / f"{key}.pkl"
                    if filepath.exists():
                        filepath.unlink()
            
            logger.debug(f"Evicted {to_remove} entries")
    
    async def _notify_subscribers(
        self,
        data_type: DataType,
        identifier: str,
        value: Any,
        version: DataVersion
    ):
        """Notify subscribers of data updates"""
        subscribers = self._subscribers.get(data_type, [])
        
        for callback in subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(identifier, value, version)
                else:
                    callback(identifier, value, version)
            except Exception as e:
                logger.error(f"Subscriber callback failed: {e}")
    
    def subscribe(self, data_type: DataType, callback: callable):
        """Subscribe to data updates"""
        self._subscribers[data_type].append(callback)
    
    def unsubscribe(self, data_type: DataType, callback: callable):
        """Unsubscribe from data updates"""
        if callback in self._subscribers[data_type]:
            self._subscribers[data_type].remove(callback)
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            total_entries = len(self._memory_cache)
            
            type_counts = {}
            for entry in self._memory_cache.values():
                dt = entry.data_type.value
                type_counts[dt] = type_counts.get(dt, 0) + 1
            
            # Calculate memory usage
            total_size = 0
            for entry in self._memory_cache.values():
                total_size += len(pickle.dumps(entry))
            
            return {
                'total_entries': total_entries,
                'type_counts': type_counts,
                'memory_usage_mb': total_size / 1024 / 1024,
                'version_counts': len(self._version_counter),
                'subscriber_counts': {
                    dt.value: len(subs) 
                    for dt, subs in self._subscribers.items()
                }
            }
    
    def _start_cleanup_task(self):
        """Start background cleanup task"""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(300)  # Run every 5 minutes
                    
                    with self._lock:
                        expired = []
                        now = datetime.now(timezone.utc)
                        
                        for key, entry in self._memory_cache.items():
                            if entry.ttl_seconds:
                                age = (now - entry.created_at).total_seconds()
                                if age > entry.ttl_seconds:
                                    expired.append(key)
                        
                        for key in expired:
                            del self._memory_cache[key]
                            
                            if self.enable_persistence:
                                filepath = self.cache_dir / f"{key}.pkl"
                                if filepath.exists():
                                    filepath.unlink()
                        
                        if expired:
                            logger.debug(f"Cleaned up {len(expired)} expired entries")
                    
                except Exception as e:
                    logger.error(f"Cleanup task error: {e}")
        
        # Start cleanup task
        loop = asyncio.get_event_loop()
        self._cleanup_task = loop.create_task(cleanup_loop())
    
    async def shutdown(self):
        """Shutdown the service"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            
        # Final persistence
        if self.enable_persistence:
            for entry in self._memory_cache.values():
                await self._persist_entry(entry)


# Global instance
_data_consistency_service: Optional[DataConsistencyService] = None


def get_data_consistency_service(
    cache_dir: Optional[Path] = None
) -> DataConsistencyService:
    """Get or create the global data consistency service"""
    global _data_consistency_service
    
    if _data_consistency_service is None:
        if cache_dir is None:
            cache_dir = Path("data/consistency_cache")
        
        _data_consistency_service = DataConsistencyService(cache_dir)
    
    return _data_consistency_service