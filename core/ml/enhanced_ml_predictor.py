"""
Enhanced ML Predictor with GPT Model Support
Handles both standard and GPT-enhanced models seamlessly
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional
import numpy as np
from datetime import datetime, timezone

from core.ml.ml_predictor import MLPredictor
from core.ml.enhanced_feature_engineering import GPTEnhancedFeatureEngineer
from core.domain.models import MarketData, SignalType
from core.ml.model_loader import load_model_safe

logger = logging.getLogger(__name__)


class EnhancedMLPredictor(MLPredictor):
    """
    Enhanced ML predictor that supports GPT-enhanced models
    """
    
    def __init__(self, models_dir: Path = Path("models")):
        super().__init__(models_dir)
        
        # Also check for GPT-enhanced models
        self._load_gpt_enhanced_models()
        
    def _load_gpt_enhanced_models(self):
        """Load GPT-enhanced models if available"""
        gpt_models_dir = self.models_dir / "gpt_enhanced"
        
        if not gpt_models_dir.exists():
            logger.info("No GPT-enhanced models directory found")
            return
            
        # Load GPT-enhanced models
        model_files = sorted(gpt_models_dir.glob("*_gpt_enhanced_*.pkl"))
        
        for model_file in model_files:
            try:
                symbol = model_file.name.split('_')[0]
                
                # Load the model
                model_package = load_model_safe(model_file)
                
                # Check if this is a GPT-enhanced model
                if model_package.get('includes_gpt_insights'):
                    # Override standard model with GPT-enhanced version
                    self.loaded_models[symbol] = {
                        'package': model_package,
                        'file_path': model_file,
                        'loaded_at': datetime.now(timezone.utc),
                        'is_gpt_enhanced': True
                    }
                    
                    logger.info(
                        f"Loaded GPT-enhanced ML model for {symbol} "
                        f"from {model_file.name}"
                    )
                    
            except Exception as e:
                logger.error(f"Failed to load GPT-enhanced model {model_file}: {e}")
    
    async def get_ml_prediction(
        self,
        symbol: str,
        market_data: Dict[str, MarketData],
        gpt_decision: Optional[Any] = None
    ) -> Dict[str, Any]:
        """
        Get ML prediction, using GPT-enhanced model if available
        
        Args:
            symbol: Trading symbol
            market_data: Market data dict
            gpt_decision: Optional GPT council decision for real-time features
            
        Returns:
            Prediction dictionary
        """
        if symbol not in self.loaded_models:
            return {
                'ml_enabled': False,
                'ml_signal': None,
                'ml_confidence': None,
                'ml_metadata': {'reason': 'No ML model available'}
            }
            
        try:
            model_info = self.loaded_models[symbol]
            model_package = model_info['package']
            is_gpt_enhanced = model_info.get('is_gpt_enhanced', False)
            
            if is_gpt_enhanced:
                # Use GPT-enhanced prediction
                return await self._get_gpt_enhanced_prediction(
                    symbol, market_data, model_package, gpt_decision
                )
            else:
                # Use standard prediction
                return await super().get_ml_prediction(symbol, market_data)
                
        except Exception as e:
            logger.error(f"ML prediction failed for {symbol}: {e}")
            return {
                'ml_enabled': False,
                'ml_signal': None,
                'ml_confidence': None,
                'ml_metadata': {'reason': f'Prediction error: {str(e)}'}
            }
    
    async def _get_gpt_enhanced_prediction(
        self,
        symbol: str,
        market_data: Dict[str, MarketData],
        model_package: Dict[str, Any],
        gpt_decision: Optional[Any] = None
    ) -> Dict[str, Any]:
        """
        Get prediction from GPT-enhanced model
        """
        pipeline = model_package.get('pipeline')
        feature_names = model_package.get('feature_names', [])
        
        if not pipeline:
            return {
                'ml_enabled': False,
                'ml_signal': None,
                'ml_confidence': None,
                'ml_metadata': {'reason': 'Invalid model package'}
            }
        
        # For GPT-enhanced models, we need GPT features
        if gpt_decision is None:
            # Use only GPT features from historical data
            logger.info(
                f"No real-time GPT decision provided for {symbol}, "
                "using historical GPT features only"
            )
        
        # Extract features
        try:
            # If we have a real-time GPT decision, extract its features
            if gpt_decision:
                from core.ml.gpt_insight_extractor import GPTInsightExtractor
                extractor = GPTInsightExtractor()
                gpt_features = extractor.extract_features_from_decision(gpt_decision)
                
                # Create feature vector
                feature_vector = []
                for fname in feature_names:
                    if fname.startswith('gpt_'):
                        feature_vector.append(gpt_features.get(fname, 0))
                    else:
                        # For non-GPT features, use default values
                        # In production, you'd extract these from market_data
                        feature_vector.append(0)
                
                features = np.array([feature_vector])
            else:
                # Create zero features if no GPT decision
                features = np.zeros((1, len(feature_names)))
                
        except Exception as e:
            logger.error(f"Feature extraction failed: {e}")
            return {
                'ml_enabled': False,
                'ml_signal': None,
                'ml_confidence': None,
                'ml_metadata': {'reason': f'Feature extraction error: {str(e)}'}
            }
        
        # Make prediction
        try:
            prediction = pipeline.predict(features)[0]
            
            # Get probability if available
            if hasattr(pipeline, 'predict_proba'):
                probabilities = pipeline.predict_proba(features)[0]
                confidence = float(np.max(probabilities) * 100)
            else:
                # Use model performance as confidence
                model_performance = model_package.get('performance', {})
                confidence = model_performance.get('test_accuracy', 0.7) * 100
            
            # Convert prediction to signal
            if prediction == 1:
                signal = SignalType.BUY
            elif prediction == -1:
                signal = SignalType.SELL
            else:
                signal = SignalType.WAIT
            
            # Get metadata
            metadata = {
                'model_type': model_package.get('model_type', 'unknown'),
                'is_gpt_enhanced': True,
                'training_date': model_package.get('training_date', 'unknown'),
                'model_performance': model_package.get('performance', {}),
                'uses_realtime_gpt': gpt_decision is not None,
                'top_gpt_features': self._get_top_gpt_features(
                    model_package.get('feature_importance', {}),
                    5
                )
            }
            
            logger.info(
                f"GPT-enhanced ML prediction for {symbol}: {signal.value} "
                f"with confidence {confidence:.1f}%"
            )
            
            return {
                'ml_enabled': True,
                'ml_signal': signal,
                'ml_confidence': confidence,
                'ml_metadata': metadata
            }
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return {
                'ml_enabled': False,
                'ml_signal': None,
                'ml_confidence': None,
                'ml_metadata': {'reason': f'Prediction error: {str(e)}'}
            }
    
    def _get_top_gpt_features(
        self, 
        feature_importance: Dict[str, float], 
        n: int = 5
    ) -> Dict[str, float]:
        """Get top N GPT features by importance"""
        gpt_features = {
            k: v for k, v in feature_importance.items() 
            if k.startswith('gpt_')
        }
        
        sorted_features = sorted(
            gpt_features.items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        
        return dict(sorted_features[:n])