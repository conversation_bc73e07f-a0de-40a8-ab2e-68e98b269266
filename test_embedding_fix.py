#!/usr/bin/env python3
"""
Test script to verify that the sentence-transformers authentication issue is resolved.
This script tests the EmbeddingGenerator class with the new authentication features.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_embedding_generator():
    """Test the EmbeddingGenerator with authentication"""
    try:
        from core.services.memory_service import EmbeddingGenerator, EMBEDDINGS_AVAILABLE
        
        if not EMBEDDINGS_AVAILABLE:
            logger.error("❌ Sentence transformers or FAISS not available")
            return False
        
        logger.info("🔧 Testing EmbeddingGenerator with authentication...")
        
        # Test without authentication first (should work for public models)
        logger.info("📝 Testing without authentication...")
        try:
            generator = EmbeddingGenerator(
                model_name="sentence-transformers/all-MiniLM-L6-v2",
                hf_token=None,
                retry_attempts=2,
                retry_delay=1.0
            )
            
            # Test embedding generation
            test_text = "This is a test sentence for embedding generation."
            embedding = generator.generate_embedding(test_text)
            
            logger.info(f"✅ Successfully generated embedding with shape: {embedding.shape}")
            logger.info(f"✅ Embedding dimension: {generator.embedding_dim}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize without authentication: {e}")
            
            # Check if it's an authentication error
            if "401" in str(e) or "Unauthorized" in str(e):
                logger.info("🔑 Authentication required. Please set HF_TOKEN environment variable.")
                logger.info("📋 Instructions:")
                logger.info("   1. Go to https://huggingface.co/settings/tokens")
                logger.info("   2. Create a new token (read access is sufficient)")
                logger.info("   3. Add HF_TOKEN=your_token_here to your .env file")
                logger.info("   4. Or set it as environment variable: export HF_TOKEN=your_token_here")
                return False
            else:
                logger.error(f"❌ Unexpected error: {e}")
                return False
                
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.error("💡 Make sure sentence-transformers and faiss-cpu are installed:")
        logger.error("   pip install sentence-transformers faiss-cpu")
        return False

def test_memory_service():
    """Test the MemoryService initialization"""
    try:
        from core.services.memory_service import MemoryService
        from core.infrastructure.database.repositories import MemoryCaseRepository
        from config.settings import DatabaseSettings, HuggingFaceSettings
        
        logger.info("🔧 Testing MemoryService initialization...")
        
        # Create mock configurations
        db_config = DatabaseSettings()
        hf_config = HuggingFaceSettings()
        
        # Create a temporary repository (this might fail if DB doesn't exist, but that's OK)
        try:
            repo = MemoryCaseRepository("data/test_memory.db")
            
            # Test MemoryService initialization
            memory_service = MemoryService(
                repository=repo,
                database_config=db_config,
                huggingface_config=hf_config
            )
            
            logger.info("✅ MemoryService initialized successfully")
            return True
            
        except Exception as e:
            logger.warning(f"⚠️  MemoryService test failed (expected if no DB): {e}")
            return True  # This is expected if database doesn't exist
            
    except Exception as e:
        logger.error(f"❌ MemoryService test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🚀 Starting embedding authentication fix test...")
    
    # Check if HF_TOKEN is set
    hf_token = os.getenv('HF_TOKEN') or os.getenv('HUGGINGFACE_HUB_TOKEN')
    if hf_token:
        logger.info("🔑 Hugging Face token found in environment")
    else:
        logger.info("⚠️  No Hugging Face token found in environment")
    
    # Run tests
    tests_passed = 0
    total_tests = 2
    
    if test_embedding_generator():
        tests_passed += 1
        logger.info("✅ EmbeddingGenerator test passed")
    else:
        logger.error("❌ EmbeddingGenerator test failed")
    
    if test_memory_service():
        tests_passed += 1
        logger.info("✅ MemoryService test passed")
    else:
        logger.error("❌ MemoryService test failed")
    
    # Summary
    logger.info(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        logger.info("🎉 All tests passed! The embedding authentication fix is working.")
        return True
    else:
        logger.error("💥 Some tests failed. Please check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
