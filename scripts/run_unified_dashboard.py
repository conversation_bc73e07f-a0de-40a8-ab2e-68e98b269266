#!/usr/bin/env python
"""
Launcher for the Unified Trading Dashboard
"""

import subprocess
import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def main():
    """Launch the unified dashboard"""
    print("🚀 Starting Unified Trading Dashboard...")
    print("=" * 50)
    
    # Set environment variables
    env = os.environ.copy()
    env['PYTHONPATH'] = str(project_root)
    
    # Launch Streamlit
    cmd = [
        sys.executable,
        "-m",
        "streamlit",
        "run",
        str(project_root / "scripts" / "unified_trading_dashboard.py"),
        "--server.port", "8501",
        "--server.address", "localhost",
        "--browser.gatherUsageStats", "false"
    ]
    
    try:
        print(f"Running command: {' '.join(cmd)}")
        print(f"Dashboard will be available at: http://localhost:8501")
        print("=" * 50)
        
        # Run the dashboard
        process = subprocess.run(cmd, env=env)
        
        if process.returncode != 0:
            print(f"❌ Dashboard exited with code: {process.returncode}")
            sys.exit(process.returncode)
            
    except KeyboardInterrupt:
        print("\n⏹️ Dashboard stopped by user")
    except Exception as e:
        print(f"❌ Error running dashboard: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()