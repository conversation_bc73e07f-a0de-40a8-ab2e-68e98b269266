#!/usr/bin/env python3
"""
Convert ForexFactory CSV data to JSON format expected by the trading system.
This script handles the conversion from ff_calendar_thisweek.csv to forexfactory_week.json
"""

import csv
import json
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ForexFactoryConverter:
    """Converts ForexFactory CSV data to JSON format"""
    
    def __init__(self):
        self.data_dir = Path("data")
        self.csv_file = self.data_dir / "ff_calendar_thisweek.csv"
        self.json_file = self.data_dir / "forexfactory_week.json"
        
        # Impact level mapping
        self.impact_mapping = {
            'High': 'high',
            'Medium': 'medium', 
            'Low': 'low',
            '': 'low'  # Default for empty impact
        }
        
        # Currency mapping for countries
        self.country_currency_map = {
            'USD': 'USD',
            'EUR': 'EUR', 
            'GBP': 'GBP',
            'JPY': 'JPY',
            'CAD': 'CAD',
            'AUD': 'AUD',
            'CHF': 'CHF',
            'NZD': 'NZD',
            'CNY': 'CNY',
            'US': 'USD',
            'EU': 'EUR',
            'UK': 'GBP',
            'GB': 'GBP',
            'JP': 'JPY',
            'CA': 'CAD',
            'AU': 'AUD',
            'CH': 'CHF',
            'NZ': 'NZD',
            'CN': 'CNY'
        }
    
    def convert_csv_to_json(self) -> bool:
        """
        Convert CSV file to JSON format
        
        Returns:
            True if conversion successful
        """
        try:
            if not self.csv_file.exists():
                logger.error(f"CSV file not found: {self.csv_file}")
                return False
            
            logger.info(f"Converting {self.csv_file} to {self.json_file}")
            
            # Read CSV data
            events = []
            with open(self.csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    event = self._convert_row_to_event(row)
                    if event:
                        events.append(event)
            
            # Create JSON structure
            json_data = {
                'events': events,
                'last_updated': datetime.now(timezone.utc).isoformat(),
                'source': 'ForexFactory CSV Conversion',
                'total_events': len(events)
            }
            
            # Save JSON file
            with open(self.json_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ Successfully converted {len(events)} events to JSON")
            logger.info(f"📁 Output file: {self.json_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Conversion failed: {e}")
            return False
    
    def _convert_row_to_event(self, row: Dict[str, str]) -> Dict[str, Any]:
        """Convert a CSV row to an event dictionary"""
        try:
            # Parse date and time
            date_str = row.get('Date', '').strip()
            time_str = row.get('Time', '').strip()
            
            if not date_str:
                logger.warning(f"Skipping event with no date: {row.get('Title', 'Unknown')}")
                return None
            
            # Convert date format (assuming MM-DD-YYYY format)
            try:
                if '/' in date_str:
                    month, day, year = date_str.split('/')
                elif '-' in date_str:
                    month, day, year = date_str.split('-')
                else:
                    logger.warning(f"Unknown date format: {date_str}")
                    return None
                
                # Handle time parsing
                hour, minute = 0, 0
                if time_str and time_str.lower() != 'all day':
                    try:
                        # Handle formats like "9:20pm", "10:30am"
                        if 'pm' in time_str.lower():
                            time_part = time_str.lower().replace('pm', '').strip()
                            if ':' in time_part:
                                hour, minute = map(int, time_part.split(':'))
                            else:
                                hour = int(time_part)
                            if hour != 12:
                                hour += 12
                        elif 'am' in time_str.lower():
                            time_part = time_str.lower().replace('am', '').strip()
                            if ':' in time_part:
                                hour, minute = map(int, time_part.split(':'))
                            else:
                                hour = int(time_part)
                            if hour == 12:
                                hour = 0
                        else:
                            # 24-hour format
                            if ':' in time_str:
                                hour, minute = map(int, time_str.split(':'))
                    except ValueError:
                        logger.warning(f"Could not parse time: {time_str}")
                
                # Create datetime
                event_datetime = datetime(
                    year=int(year),
                    month=int(month), 
                    day=int(day),
                    hour=hour,
                    minute=minute,
                    tzinfo=timezone.utc
                )
                
            except ValueError as e:
                logger.warning(f"Could not parse date/time {date_str} {time_str}: {e}")
                return None
            
            # Get currency from country
            country = row.get('Country', '').strip().upper()
            currency = self.country_currency_map.get(country, country)
            
            # Map impact level
            impact = row.get('Impact', '').strip()
            impact_level = self.impact_mapping.get(impact, 'low')
            
            # Create event
            event = {
                'datetime': event_datetime.isoformat(),
                'date': event_datetime.strftime('%Y-%m-%d'),
                'time': event_datetime.strftime('%H:%M'),
                'currency': currency,
                'country': country,
                'event': row.get('Title', '').strip(),
                'title': row.get('Title', '').strip(),
                'impact': impact_level,
                'forecast': row.get('Forecast', '').strip() or None,
                'previous': row.get('Previous', '').strip() or None,
                'actual': None,  # Not available in CSV
                'url': row.get('URL', '').strip() or None
            }
            
            return event
            
        except Exception as e:
            logger.warning(f"Failed to convert row: {e}")
            return None
    
    def create_fallback_data(self) -> bool:
        """Create fallback news data if no CSV file exists"""
        try:
            logger.info("Creating fallback news data...")
            
            # Create minimal news data structure
            fallback_data = {
                'events': [],
                'last_updated': datetime.now(timezone.utc).isoformat(),
                'source': 'Fallback - No Data Available',
                'total_events': 0,
                'note': 'This is fallback data. Please update with real ForexFactory data.'
            }
            
            with open(self.json_file, 'w', encoding='utf-8') as f:
                json.dump(fallback_data, f, indent=2)
            
            logger.info(f"✅ Created fallback data file: {self.json_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create fallback data: {e}")
            return False

def main():
    """Main function"""
    logger.info("🔄 Starting ForexFactory CSV to JSON conversion...")
    
    converter = ForexFactoryConverter()
    
    # Try to convert existing CSV
    if converter.csv_file.exists():
        success = converter.convert_csv_to_json()
        if success:
            logger.info("🎉 Conversion completed successfully!")
        else:
            logger.error("💥 Conversion failed!")
            return False
    else:
        logger.warning(f"📄 CSV file not found: {converter.csv_file}")
        logger.info("🔧 Creating fallback data...")
        success = converter.create_fallback_data()
        if success:
            logger.info("✅ Fallback data created. Please update with real ForexFactory data.")
        else:
            logger.error("💥 Failed to create fallback data!")
            return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
