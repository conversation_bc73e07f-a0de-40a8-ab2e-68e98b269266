# GPT Trading System - Comprehensive Usage Guide

## Table of Contents
1. [Quick Start](#quick-start)
2. [Configuration Overview](#configuration-overview)
3. [Trading Modes](#trading-modes)
4. [Environment Variables](#environment-variables)
5. [Configuration Files](#configuration-files)
6. [Running the System](#running-the-system)
7. [Backtesting](#backtesting)
8. [Monitoring & Dashboards](#monitoring--dashboards)
9. [Common Scenarios](#common-scenarios)
10. [Troubleshooting](#troubleshooting)

---

## Quick Start

### 1. Initial Setup
```bash
# Clone repository
git clone https://github.com/yourusername/gpt_trader_v1.git
cd gpt_trader_v1

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy environment template
cp .env.example .env
# Edit .env with your credentials
```

### 2. Essential Configuration
Edit `.env` file with your credentials:
```env
# Required
OPENAI_API_KEY=your-openai-api-key
MT5_FILES_DIR=C:/path/to/MetaTrader5/MQL5/Files

# Optional but recommended
TELEGRAM_TOKEN=your-telegram-bot-token
TELEGRAM_CHAT_ID=your-chat-id
```

### 3. Quick Test
```bash
# Test MT5 connection
python test_mt5_simple.py

# Run a quick backtest
python run_backtest.py --mode ml_only --days 7
```

---

## Configuration Overview

The system uses multiple configuration layers:

1. **Environment Variables** (`.env`) - Credentials and runtime settings
2. **Settings Files** (`config/`) - Trading parameters and strategies
3. **Command-line Arguments** - Override defaults for specific runs

---

## Trading Modes

### 1. Scalping Mode (Default)
- **Timeframes**: H1 entry, H4 background
- **Holding Period**: 4-8 hours
- **Stop Loss**: 1-2x ATR
- **Risk/Reward**: 1.5:1 to 2:1

### 2. Position Trading Mode
- **Timeframes**: D1 entry, W1 background
- **Holding Period**: 5-20 days
- **Stop Loss**: 3-5x ATR
- **Risk/Reward**: 3:1 to 5:1

### 3. Hybrid Mode
- Automatically switches based on market conditions
- Uses position trading in trending markets
- Reverts to scalping in ranging conditions

---

## Environment Variables

### Core Settings (.env file)

```env
# === API Keys ===
OPENAI_API_KEY=sk-...                    # Required: OpenAI API key
MARKETAUX_API_KEY=your-key               # Optional: MarketAux news
TELEGRAM_TOKEN=bot-token                 # Optional: Telegram notifications
TELEGRAM_CHAT_ID=chat-id                # Optional: Your Telegram chat

# === MT5 Configuration ===
MT5_FILES_DIR=C:/path/to/MQL5/Files     # Required: MT5 files directory
MT5_ACCOUNT=12345                        # Optional: Default account
MT5_PASSWORD=password                    # Optional: Account password
MT5_SERVER=YourBroker-Server            # Optional: MT5 server

# === Trading Mode ===
POSITION_TRADING_MODE=false              # Enable position trading
TRADING_TIMEFRAMES=["H1", "H4"]         # Default timeframes
# For position trading: ["D1", "W1"]

# === Risk Management ===
RISK_PER_TRADE=1.0                      # Risk % per trade
MAX_DAILY_TRADES=5                      # Maximum trades per day
MAX_CONCURRENT_TRADES=3                 # Maximum open positions
PORTFOLIO_HEAT_LIMIT=6.0                # Maximum portfolio risk %

# === Trading Symbols ===
TRADING_SYMBOLS=["EURUSD","GBPUSD","XAUUSD"]  # Symbols to trade
# Or use groups:
SYMBOL_GROUPS=["conservative_forex","moderate_commodities"]

# === Council Configuration ===
COUNCIL_CONFIDENCE_THRESHOLD=75.0       # Minimum confidence to trade
COUNCIL_QUICK_MODE=false                # Skip debates for speed
COUNCIL_LLM_WEIGHT=0.7                  # LLM vs ML weight (0-1)
COUNCIL_ML_WEIGHT=0.3                   # ML contribution

# === ML Configuration ===
ML_ENABLED=true                         # Enable ML predictions
ML_CONFIDENCE_THRESHOLD=0.7             # ML confidence threshold
ML_MODELS_PATH=./models                 # Path to ML models

# === Backtesting ===
BACKTEST_MODE=auto                      # auto, full_council, simplified, ml_only
BACKTEST_USE_CHEAP_MODELS=true          # Use GPT-3.5 for backtesting
BACKTEST_CACHE_ENABLED=true             # Cache backtesting results

# === Performance & Optimization ===
CACHE_ENABLED=true                      # Enable market state caching
CACHE_TTL_MINUTES=60                    # Cache validity period
CACHE_SIMILARITY_THRESHOLD=0.85         # Similarity for cache hits
PRE_FILTER_ENABLED=true                 # Enable pre-trade filtering
OFFLINE_VALIDATION_ENABLED=true         # Enable offline validation
OFFLINE_VALIDATION_THRESHOLD=60         # Minimum validation score

# === News & Sentiment ===
NEWS_ENABLED=true                       # Enable news filtering
NEWS_BLACKOUT_MINUTES=30                # Blackout before/after news
MARKETAUX_ENABLED=false                 # Enable MarketAux sentiment
MARKETAUX_SENTIMENT_WEIGHT=0.3          # Sentiment impact weight

# === Logging & Monitoring ===
LOG_LEVEL=INFO                          # DEBUG, INFO, WARNING, ERROR
LOG_TO_FILE=true                        # Log to files
LOG_FILE_PATH=./logs                    # Log directory
ENABLE_TELEGRAM_LOGS=true               # Send logs to Telegram
```

---

## Configuration Files

### 1. Main Settings (`config/settings.py`)

Key parameters to adjust:

```python
class TradingSettings(BaseModel):
    # Risk Management
    risk_per_trade_percent: float = 1.0    # Risk per trade
    max_daily_loss_percent: float = 3.0    # Daily loss limit
    max_positions: int = 3                 # Concurrent positions
    
    # Entry Criteria
    min_atr: float = 0.0010               # Minimum volatility
    max_spread_atr_ratio: float = 0.3     # Max spread as % of ATR
    
    # Timeframes
    entry_timeframe: str = "H1"           # Primary timeframe
    background_timeframe: str = "H4"       # Context timeframe
    
    # Council Settings
    council_confidence_threshold: float = 75.0
    council_quick_mode: bool = False
    council_debate_rounds: int = 3
```

### 2. Position Trading (`config/position_trading_config.py`)

```python
@dataclass
class PositionTradingConfig:
    # Timeframes
    entry_timeframe: str = "D1"
    background_timeframe: str = "W1"
    
    # Risk Parameters
    stop_loss_atr_multiplier: float = 4.0   # 3-5x daily ATR
    take_profit_atr_multiplier: float = 12.0 # For 3:1 R:R
    
    # Position Management
    max_positions: int = 4
    partial_profits: List[float] = field(default_factory=lambda: [1.5, 3.0, 4.0])
    trailing_stop_activation: float = 2.0    # Activate at 2R
    
    # Timing Constraints
    min_hours_between_trades: float = 24
    min_hours_same_symbol: float = 48
    max_holding_days: int = 30
```

### 3. Symbols Configuration (`config/symbols.py`)

```python
# Add custom symbols
CUSTOM_SYMBOLS = {
    "my_selection": ["EURUSD", "GBPUSD", "XAUUSD", "US500.cash"]
}

# Or modify existing groups
CONSERVATIVE_SYMBOLS = ["EURUSD", "USDJPY", "USDCHF"]
MODERATE_SYMBOLS = ["GBPUSD", "AUDUSD", "EURJPY"]
AGGRESSIVE_SYMBOLS = ["GBPJPY", "XAUUSD", "NATGAS"]
```

### 4. Backtest Configuration (`config/backtest_config.py`)

```python
# Preset configurations
BACKTEST_PRESETS = {
    "ultra_low_cost": {
        "default_model": BacktestModelTier.ULTRA_LOW_COST,
        "simplified_agents": True,
        "skip_debates": True,
        "cache_aggressively": True
    },
    "balanced": {
        "default_model": BacktestModelTier.BALANCED,
        "simplified_agents": False,
        "skip_debates": True
    }
}
```

---

## Running the System

### 1. Production Trading

#### Standard Mode
```bash
# Run with default settings
python trading_loop.py

# With specific symbols
python trading_loop.py --symbols EURUSD,GBPUSD,XAUUSD

# With symbol groups
python trading_loop.py --groups conservative_forex,moderate_commodities
```

#### Position Trading Mode
```bash
# Enable position trading
export POSITION_TRADING_MODE=true
python trading_loop.py

# Or via command line
python trading_loop.py --position-trading
```

### 2. Backtesting

#### Quick ML Backtest
```bash
# Ultra-fast ML-only mode
python run_backtest.py --mode ml_only --days 30
```

#### Comprehensive Backtest
```bash
# Compare all modes
python run_backtest.py --compare-modes --days 180

# With walk-forward optimization
python run_backtest.py --walk-forward 3 --days 365
```

#### Parameter Optimization
```bash
# Optimize key parameters
python run_backtest.py --optimize --mode simplified
```

### 3. Training ML Models

```bash
# Train/update ML models
python scripts/train_ml_production.py

# Train backtest simulator
python run_backtest.py --train-simulator
```

---

## Monitoring & Dashboards

### 1. Trading Dashboard
```bash
# Main dashboard
python scripts/run_dashboard.py

# Simple dashboard
python scripts/run_simple_dashboard.py

# With GPT flow visualization
python scripts/start_dashboard_with_gpt_flow.bat
```

### 2. ML Performance Monitor
```bash
# Monitor ML predictions
python scripts/run_ml_monitor.py

# ML improvement dashboard
python scripts/ml_improvement_dashboard.py
```

### 3. Position Monitor
```bash
# Monitor open positions
python scripts/position_monitor_dashboard.py
```

### 4. Performance Analytics
```bash
# Generate performance report
python scripts/performance_analytics.py --days 30

# Council visualization
python scripts/visualize_council.py
```

---

## Common Scenarios

### 1. Conservative Trading Setup
```env
# .env settings
RISK_PER_TRADE=0.5
MAX_CONCURRENT_TRADES=2
COUNCIL_CONFIDENCE_THRESHOLD=80.0
TRADING_SYMBOLS=["EURUSD","USDJPY","USDCHF"]
POSITION_TRADING_MODE=true
```

### 2. Aggressive Scalping Setup
```env
# .env settings
RISK_PER_TRADE=2.0
MAX_CONCURRENT_TRADES=5
COUNCIL_CONFIDENCE_THRESHOLD=70.0
TRADING_SYMBOLS=["GBPJPY","XAUUSD","US30.cash"]
COUNCIL_QUICK_MODE=true
```

### 3. Testing New Strategy
```bash
# 1. Quick ML backtest
python run_backtest.py --mode ml_only --days 30

# 2. If promising, run simplified backtest
python run_backtest.py --mode simplified --days 90

# 3. Finally, full council backtest
python run_backtest.py --mode full_council --days 180
```

### 4. FTMO Challenge Setup
```env
# Conservative settings for prop firm
RISK_PER_TRADE=1.0
MAX_DAILY_TRADES=3
PORTFOLIO_HEAT_LIMIT=5.0
TRADING_SYMBOLS=["EURUSD","GBPUSD","XAUUSD","US500.cash","GER40.cash"]
NEWS_BLACKOUT_MINUTES=60
POSITION_TRADING_MODE=true
```

---

## Troubleshooting

### MT5 Connection Issues
```bash
# Test connection
python test_mt5_connection.py

# Find available symbols
python test_symbol_finder.py

# Enable symbols interactively
python test_symbol_finder.py --quick
```

### API Cost Issues
```bash
# Check costs
python check_gpt_requests.py

# Use cheaper models
export BACKTEST_USE_CHEAP_MODELS=true
export COUNCIL_QUICK_MODE=true
```

### Performance Issues
```bash
# Enable caching
export CACHE_ENABLED=true
export PRE_FILTER_ENABLED=true

# Reduce council debates
export COUNCIL_QUICK_MODE=true
```

### Database Issues
```bash
# Backup database
python scripts/automation/database_backup.py

# Run migrations
python core/infrastructure/database/migrations.py
```

---

## Best Practices

1. **Start Conservative**: Begin with low risk settings and increase gradually
2. **Test First**: Always backtest changes before live trading
3. **Monitor Costs**: Use dashboards to track API usage
4. **Regular Backups**: Backup database and configs regularly
5. **Update Models**: Retrain ML models weekly
6. **Review Logs**: Check logs daily for issues
7. **Use Position Mode**: For lower costs and better R:R
8. **Diversify**: Trade multiple uncorrelated instruments

---

## Additional Resources

- **CLAUDE.md**: Codebase instructions and recent updates
- **docs/**: Detailed documentation for specific features
- **ML_USAGE_GUIDE.md**: Machine learning integration guide
- **POSITION_TRADING_GUIDE.md**: Position trading specifics
- **PHASE*_SUMMARY.md**: Detailed implementation summaries

For support, check the logs in `logs/` directory or use the health check:
```bash
python scripts/automation/health_check.py
```