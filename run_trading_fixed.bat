@echo off
REM Fixed trading launcher that handles MT5 authorization issues

echo GPT Trading System - Fixed Launcher
echo ==================================
echo.

REM Set environment variables
set PYTHONPATH=D:\gpt_trader_v1
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

REM Change to project directory
cd /d D:\gpt_trader_v1

echo Ensuring MT5 is properly initialized...
echo.

REM Kill any existing MT5 instances to start fresh
echo Closing any existing MT5 instances...
taskkill /F /IM terminal64.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Starting trading system...
echo.

REM Run with explicit UTF-8 encoding
venv\Scripts\python.exe -X utf8 trading_loop.py

pause