"""
Standardized error handling decorators for the trading system.
Provides consistent error handling, logging, and recovery strategies.
"""

import asyncio
import functools
import logging
import time
from typing import TypeVar, Callable, Optional, Any, Dict, Type, Union, Tuple
from enum import Enum
from datetime import datetime, timezone

from core.domain.exceptions import (
    TradingSystemError, ErrorContext, ConfigurationError, 
    MT5ConnectionError, GPTAPIError, DatabaseError,
    ValidationError, InsufficientDataError, ServiceError
)
from core.utils.structured_logger import get_logger

T = TypeVar('T')
logger = get_logger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification"""
    CONFIGURATION = "configuration"
    CONNECTION = "connection"
    DATA = "data"
    VALIDATION = "validation"
    BUSINESS_LOGIC = "business_logic"
    EXTERNAL_SERVICE = "external_service"
    UNKNOWN = "unknown"


class RetryStrategy:
    """Retry configuration for error recovery"""
    def __init__(
        self,
        max_attempts: int = 3,
        initial_delay: float = 1.0,
        backoff_factor: float = 2.0,
        max_delay: float = 60.0,
        jitter: bool = True
    ):
        self.max_attempts = max_attempts
        self.initial_delay = initial_delay
        self.backoff_factor = backoff_factor
        self.max_delay = max_delay
        self.jitter = jitter
    
    def get_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt"""
        delay = min(
            self.initial_delay * (self.backoff_factor ** (attempt - 1)),
            self.max_delay
        )
        
        if self.jitter:
            # Add random jitter (±25%)
            import random
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)


class ErrorClassifier:
    """Classifies errors for appropriate handling"""
    
    @staticmethod
    def classify(error: Exception) -> Tuple[ErrorCategory, ErrorSeverity]:
        """Classify an error by category and severity"""
        
        # Configuration errors
        if isinstance(error, ConfigurationError):
            return ErrorCategory.CONFIGURATION, ErrorSeverity.CRITICAL
        
        # Connection errors
        if isinstance(error, (MT5ConnectionError, ConnectionError)):
            return ErrorCategory.CONNECTION, ErrorSeverity.HIGH
        
        # External service errors
        if isinstance(error, GPTAPIError):
            return ErrorCategory.EXTERNAL_SERVICE, ErrorSeverity.HIGH
        
        # Data errors
        if isinstance(error, (InsufficientDataError, DatabaseError)):
            return ErrorCategory.DATA, ErrorSeverity.MEDIUM
        
        # Validation errors
        if isinstance(error, ValidationError):
            return ErrorCategory.VALIDATION, ErrorSeverity.LOW
        
        # Business logic errors
        if isinstance(error, ServiceError):
            return ErrorCategory.BUSINESS_LOGIC, ErrorSeverity.MEDIUM
        
        # Default classification
        return ErrorCategory.UNKNOWN, ErrorSeverity.HIGH


def handle_errors(
    *,
    component: str,
    operation: Optional[str] = None,
    retryable: bool = True,
    retry_strategy: Optional[RetryStrategy] = None,
    error_mappings: Optional[Dict[Type[Exception], Type[Exception]]] = None,
    default_return: Any = None,
    log_args: bool = True,
    notify_on_error: bool = True
):
    """
    Comprehensive error handling decorator.
    
    Args:
        component: Component name for error context
        operation: Specific operation being performed
        retryable: Whether to retry on failure
        retry_strategy: Custom retry strategy
        error_mappings: Map specific exceptions to custom exceptions
        default_return: Default value to return on unrecoverable error
        log_args: Whether to log function arguments
        notify_on_error: Whether to send notifications on errors
    """
    if retry_strategy is None:
        retry_strategy = RetryStrategy()
    
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> T:
            start_time = time.time()
            attempt = 0
            last_error = None
            
            # Create error context
            with ErrorContext(component, operation=operation or func.__name__) as ctx:
                if log_args and logger.isEnabledFor(logging.DEBUG):
                    # Be careful not to log sensitive data
                    safe_args = _sanitize_args(args, kwargs)
                    ctx.add_detail("args", safe_args)
                
                while attempt <= retry_strategy.max_attempts:
                    attempt += 1
                    
                    try:
                        # Log attempt if retrying
                        if attempt > 1:
                            logger.info(
                                f"Retry attempt {attempt}/{retry_strategy.max_attempts} "
                                f"for {component}.{operation or func.__name__}"
                            )
                        
                        # Execute function
                        result = await func(*args, **kwargs)
                        
                        # Log success metrics
                        elapsed = time.time() - start_time
                        if attempt > 1:
                            logger.info(
                                f"Operation succeeded after {attempt} attempts "
                                f"({elapsed:.2f}s)"
                            )
                        
                        return result
                    
                    except Exception as e:
                        last_error = e
                        category, severity = ErrorClassifier.classify(e)
                        
                        # Add error details to context
                        ctx.add_detail("error_type", type(e).__name__)
                        ctx.add_detail("error_message", str(e))
                        ctx.add_detail("attempt", attempt)
                        ctx.add_detail("category", category.value)
                        ctx.add_detail("severity", severity.value)
                        
                        # Check if error should be retried
                        should_retry = (
                            retryable and
                            attempt < retry_strategy.max_attempts and
                            _is_retryable_error(e, category)
                        )
                        
                        if should_retry:
                            delay = retry_strategy.get_delay(attempt)
                            logger.warning(
                                f"Retryable error in {component}.{operation or func.__name__}: "
                                f"{type(e).__name__}: {str(e)}. "
                                f"Retrying in {delay:.1f}s..."
                            )
                            await asyncio.sleep(delay)
                            continue
                        
                        # Log final error
                        if severity in (ErrorSeverity.HIGH, ErrorSeverity.CRITICAL):
                            logger.error(
                                f"Critical error in {component}.{operation or func.__name__}: "
                                f"{type(e).__name__}: {str(e)}",
                                exc_info=True
                            )
                        else:
                            logger.warning(
                                f"Error in {component}.{operation or func.__name__}: "
                                f"{type(e).__name__}: {str(e)}"
                            )
                        
                        # Map to custom exception if configured
                        if error_mappings and type(e) in error_mappings:
                            raise error_mappings[type(e)](str(e)) from e
                        
                        # Re-raise or return default
                        if default_return is not None:
                            return default_return
                        raise
                
                # All retries exhausted
                logger.error(
                    f"All retry attempts exhausted for {component}.{operation or func.__name__}. "
                    f"Last error: {type(last_error).__name__}: {str(last_error)}"
                )
                
                if default_return is not None:
                    return default_return
                raise last_error
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> T:
            # Simplified sync version without retry logic
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(
                    f"Error in {component}.{operation or func.__name__}: "
                    f"{type(e).__name__}: {str(e)}",
                    exc_info=True
                )
                
                if error_mappings and type(e) in error_mappings:
                    raise error_mappings[type(e)](str(e)) from e
                
                if default_return is not None:
                    return default_return
                raise
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def _is_retryable_error(error: Exception, category: ErrorCategory) -> bool:
    """Determine if an error should be retried"""
    
    # Never retry configuration errors
    if category == ErrorCategory.CONFIGURATION:
        return False
    
    # Always retry connection errors
    if category == ErrorCategory.CONNECTION:
        return True
    
    # Retry external service errors unless it's a client error
    if category == ErrorCategory.EXTERNAL_SERVICE:
        if isinstance(error, GPTAPIError):
            # Don't retry 4xx errors (client errors)
            error_msg = str(error).lower()
            if any(code in error_msg for code in ['400', '401', '403', '404']):
                return False
        return True
    
    # Retry data errors if they might be transient
    if category == ErrorCategory.DATA:
        return isinstance(error, InsufficientDataError)
    
    # Don't retry validation or business logic errors
    if category in (ErrorCategory.VALIDATION, ErrorCategory.BUSINESS_LOGIC):
        return False
    
    # Default: retry unknown errors
    return True


def _sanitize_args(args: tuple, kwargs: dict) -> Dict[str, Any]:
    """Sanitize function arguments for logging"""
    sanitized = {"args": [], "kwargs": {}}
    
    # Sanitize positional arguments
    for arg in args:
        if hasattr(arg, '__class__'):
            sanitized["args"].append(f"<{arg.__class__.__name__}>")
        else:
            sanitized["args"].append(str(arg)[:100])  # Truncate long strings
    
    # Sanitize keyword arguments
    sensitive_keys = {'password', 'api_key', 'secret', 'token', 'credential'}
    for key, value in kwargs.items():
        if any(sensitive in key.lower() for sensitive in sensitive_keys):
            sanitized["kwargs"][key] = "<REDACTED>"
        elif hasattr(value, '__class__'):
            sanitized["kwargs"][key] = f"<{value.__class__.__name__}>"
        else:
            sanitized["kwargs"][key] = str(value)[:100]
    
    return sanitized


# Convenience decorators for common use cases

def handle_mt5_errors(operation: str):
    """Decorator specifically for MT5 operations"""
    return handle_errors(
        component="MT5",
        operation=operation,
        retryable=True,
        retry_strategy=RetryStrategy(max_attempts=3, initial_delay=2.0),
        error_mappings={
            ConnectionError: MT5ConnectionError,
            TimeoutError: MT5ConnectionError
        }
    )


def handle_gpt_errors(operation: str):
    """Decorator specifically for GPT operations"""
    return handle_errors(
        component="GPT",
        operation=operation,
        retryable=True,
        retry_strategy=RetryStrategy(max_attempts=3, initial_delay=5.0, max_delay=30.0),
        error_mappings={
            ConnectionError: GPTAPIError,
            TimeoutError: GPTAPIError
        }
    )


def handle_database_errors(operation: str):
    """Decorator specifically for database operations"""
    return handle_errors(
        component="Database",
        operation=operation,
        retryable=True,
        retry_strategy=RetryStrategy(max_attempts=2, initial_delay=0.5),
        error_mappings={
            ConnectionError: DatabaseError
        }
    )


def handle_service_errors(service: str, operation: str):
    """Decorator for service layer operations"""
    return handle_errors(
        component=f"Service.{service}",
        operation=operation,
        retryable=False,  # Service errors usually shouldn't be retried
        default_return=None
    )