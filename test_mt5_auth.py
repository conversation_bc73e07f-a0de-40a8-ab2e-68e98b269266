"""
Quick MT5 authentication test
"""
import MetaTrader5 as mt5
import time

print("Testing MT5 connection...")

# Try simple initialization without parameters
print("\n1. Trying mt5.initialize() without parameters...")
result = mt5.initialize()
print(f"Result: {result}")

if not result:
    error = mt5.last_error()
    print(f"Error: {error}")
    
    # Try waiting and retrying
    print("\n2. Waiting 2 seconds and retrying...")
    time.sleep(2)
    result = mt5.initialize()
    print(f"Result: {result}")
    
    if not result:
        error = mt5.last_error()
        print(f"Error: {error}")
        
        # Try with explicit path
        print("\n3. Trying with terminal64.exe path...")
        # Common paths
        paths = [
            r"C:\Program Files\MetaTrader 5\terminal64.exe",
            r"C:\Program Files (x86)\MetaTrader 5\terminal64.exe",
            r"C:\Program Files\MetaQuotes\Terminal\terminal64.exe",
        ]
        
        for path in paths:
            print(f"Trying: {path}")
            result = mt5.initialize(path)
            if result:
                print(f"Success with path: {path}")
                break
            else:
                print(f"Failed: {mt5.last_error()}")

if result:
    # Get account info
    account_info = mt5.account_info()
    if account_info:
        print(f"\nConnected successfully!")
        print(f"Account: {account_info.login}")
        print(f"Server: {account_info.server}")
        print(f"Balance: {account_info.balance}")
        print(f"Currency: {account_info.currency}")
    else:
        print("\nConnected but no account info available")
        
    # Get terminal info
    terminal_info = mt5.terminal_info()
    if terminal_info:
        print(f"\nTerminal path: {terminal_info.path}")
        print(f"Data path: {terminal_info.data_path}")
else:
    print("\nFailed to connect to MT5")
    print("Possible solutions:")
    print("1. Make sure MT5 is installed and has been opened at least once")
    print("2. Make sure you're logged into an account in MT5")
    print("3. Try running this script as administrator")
    print("4. Check if MT5 is already running")

# Cleanup
mt5.shutdown()
print("\nTest complete.")