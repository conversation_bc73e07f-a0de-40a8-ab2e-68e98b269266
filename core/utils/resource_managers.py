"""
Resource management context managers for the trading system.
Ensures proper cleanup of connections, files, and other resources.
"""

import asyncio
import sqlite3
import threading
from contextlib import contextmanager, asynccontextmanager
from typing import Optional, Any, Dict, List, Callable
from pathlib import Path
import logging
import time
from datetime import datetime, timezone

from core.domain.exceptions import MT5ConnectionError, DatabaseError
from core.utils.structured_logger import get_logger

logger = get_logger(__name__)


class ResourceTracker:
    """Tracks active resources for monitoring and cleanup"""
    
    def __init__(self):
        self._resources: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
    
    def register(self, resource_id: str, resource_type: str, details: Dict[str, Any]):
        """Register a resource"""
        with self._lock:
            self._resources[resource_id] = {
                'type': resource_type,
                'created_at': datetime.now(timezone.utc),
                'details': details,
                'active': True
            }
    
    def unregister(self, resource_id: str):
        """Unregister a resource"""
        with self._lock:
            if resource_id in self._resources:
                self._resources[resource_id]['active'] = False
                self._resources[resource_id]['closed_at'] = datetime.now(timezone.utc)
    
    def get_active_resources(self) -> List[Dict[str, Any]]:
        """Get list of active resources"""
        with self._lock:
            return [
                {**info, 'id': rid}
                for rid, info in self._resources.items()
                if info.get('active', False)
            ]
    
    def cleanup_stale_resources(self, max_age_seconds: int = 3600):
        """Clean up stale resource entries"""
        with self._lock:
            now = datetime.now(timezone.utc)
            to_remove = []
            
            for rid, info in self._resources.items():
                if not info.get('active', False):
                    closed_at = info.get('closed_at')
                    if closed_at and (now - closed_at).total_seconds() > max_age_seconds:
                        to_remove.append(rid)
            
            for rid in to_remove:
                del self._resources[rid]


# Global resource tracker
_resource_tracker = ResourceTracker()


@contextmanager
def mt5_connection(mt5_client):
    """
    Context manager for MT5 connections.
    Ensures proper initialization and shutdown.
    """
    resource_id = f"mt5_{id(mt5_client)}_{time.time()}"
    initialized = False
    
    try:
        # Register resource
        _resource_tracker.register(
            resource_id,
            'mt5_connection',
            {'client_id': id(mt5_client)}
        )
        
        # Initialize if not already initialized
        if not mt5_client.initialized:
            logger.info("Initializing MT5 connection")
            if not mt5_client.initialize():
                raise MT5ConnectionError("Failed to initialize MT5 connection")
            initialized = True
        
        yield mt5_client
        
    except Exception as e:
        logger.error(f"Error in MT5 connection context: {e}")
        raise
    
    finally:
        # Only shutdown if we initialized it
        if initialized:
            try:
                logger.info("Shutting down MT5 connection")
                mt5_client.shutdown()
            except Exception as e:
                logger.error(f"Error shutting down MT5: {e}")
        
        # Unregister resource
        _resource_tracker.unregister(resource_id)


@asynccontextmanager
async def async_mt5_connection(mt5_client):
    """
    Async context manager for MT5 connections.
    """
    resource_id = f"async_mt5_{id(mt5_client)}_{time.time()}"
    initialized = False
    
    try:
        # Register resource
        _resource_tracker.register(
            resource_id,
            'async_mt5_connection',
            {'client_id': id(mt5_client)}
        )
        
        # Initialize if not already initialized
        if not mt5_client.initialized:
            logger.info("Initializing MT5 connection (async)")
            # Run initialization in thread pool since MT5 is sync
            loop = asyncio.get_event_loop()
            initialized = await loop.run_in_executor(None, mt5_client.initialize)
            if not initialized:
                raise MT5ConnectionError("Failed to initialize MT5 connection")
        
        yield mt5_client
        
    except Exception as e:
        logger.error(f"Error in async MT5 connection context: {e}")
        raise
    
    finally:
        # Only shutdown if we initialized it
        if initialized:
            try:
                logger.info("Shutting down MT5 connection (async)")
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, mt5_client.shutdown)
            except Exception as e:
                logger.error(f"Error shutting down MT5: {e}")
        
        # Unregister resource
        _resource_tracker.unregister(resource_id)


@contextmanager
def database_connection(db_path: str, timeout: float = 30.0):
    """
    Context manager for database connections.
    Ensures proper connection handling and cleanup.
    """
    resource_id = f"db_{Path(db_path).name}_{time.time()}"
    conn = None
    
    try:
        # Register resource
        _resource_tracker.register(
            resource_id,
            'database_connection',
            {'db_path': db_path}
        )
        
        # Create connection
        logger.debug(f"Opening database connection to {db_path}")
        conn = sqlite3.connect(db_path, timeout=timeout)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        
        # Enable foreign keys
        conn.execute("PRAGMA foreign_keys = ON")
        
        yield conn
        
    except sqlite3.Error as e:
        logger.error(f"Database error: {e}")
        raise DatabaseError(f"Database operation failed: {e}") from e
    
    except Exception as e:
        logger.error(f"Unexpected error in database context: {e}")
        raise
    
    finally:
        if conn:
            try:
                conn.close()
                logger.debug(f"Closed database connection to {db_path}")
            except Exception as e:
                logger.error(f"Error closing database connection: {e}")
        
        # Unregister resource
        _resource_tracker.unregister(resource_id)


@asynccontextmanager
async def async_database_connection(db_path: str, timeout: float = 30.0):
    """
    Async context manager for database connections.
    Uses thread pool for SQLite operations.
    """
    resource_id = f"async_db_{Path(db_path).name}_{time.time()}"
    conn = None
    
    try:
        # Register resource
        _resource_tracker.register(
            resource_id,
            'async_database_connection',
            {'db_path': db_path}
        )
        
        # Create connection in thread pool
        loop = asyncio.get_event_loop()
        
        def create_connection():
            conn = sqlite3.connect(db_path, timeout=timeout)
            conn.row_factory = sqlite3.Row
            conn.execute("PRAGMA foreign_keys = ON")
            return conn
        
        logger.debug(f"Opening async database connection to {db_path}")
        conn = await loop.run_in_executor(None, create_connection)
        
        yield conn
        
    except sqlite3.Error as e:
        logger.error(f"Database error: {e}")
        raise DatabaseError(f"Database operation failed: {e}") from e
    
    except Exception as e:
        logger.error(f"Unexpected error in async database context: {e}")
        raise
    
    finally:
        if conn:
            try:
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, conn.close)
                logger.debug(f"Closed async database connection to {db_path}")
            except Exception as e:
                logger.error(f"Error closing database connection: {e}")
        
        # Unregister resource
        _resource_tracker.unregister(resource_id)


class CacheManager:
    """
    Context manager for cache resources.
    Ensures proper cache lifecycle management.
    """
    
    def __init__(self, cache_dir: Path, max_size_mb: int = 100):
        self.cache_dir = Path(cache_dir)
        self.max_size_mb = max_size_mb
        self._lock = threading.Lock()
        self._cache_files: Dict[str, Dict[str, Any]] = {}
    
    def __enter__(self):
        # Create cache directory if it doesn't exist
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Register resource
        _resource_tracker.register(
            f"cache_{id(self)}",
            'cache_manager',
            {'cache_dir': str(self.cache_dir)}
        )
        
        # Clean up old cache files on entry
        self._cleanup_old_files()
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Unregister resource
        _resource_tracker.unregister(f"cache_{id(self)}")
        
        # Optionally clean up on exit
        if exc_type is not None:
            logger.error(f"Cache manager exiting with error: {exc_val}")
    
    def _cleanup_old_files(self, max_age_days: int = 7):
        """Remove cache files older than max_age_days"""
        try:
            now = time.time()
            cutoff_time = now - (max_age_days * 24 * 3600)
            
            for file_path in self.cache_dir.glob("*"):
                if file_path.is_file():
                    if file_path.stat().st_mtime < cutoff_time:
                        logger.info(f"Removing old cache file: {file_path}")
                        file_path.unlink()
        
        except Exception as e:
            logger.error(f"Error cleaning up cache files: {e}")
    
    @contextmanager
    def cache_file(self, filename: str):
        """Context manager for individual cache files"""
        file_path = self.cache_dir / filename
        
        try:
            with self._lock:
                self._cache_files[filename] = {
                    'path': file_path,
                    'opened_at': datetime.now(timezone.utc)
                }
            
            yield file_path
            
        finally:
            with self._lock:
                if filename in self._cache_files:
                    del self._cache_files[filename]


class ResourcePool:
    """
    Generic resource pool for managing reusable resources.
    """
    
    def __init__(
        self,
        resource_factory: Callable[[], Any],
        max_size: int = 10,
        timeout: float = 30.0
    ):
        self.resource_factory = resource_factory
        self.max_size = max_size
        self.timeout = timeout
        self._pool: List[Any] = []
        self._in_use: Dict[int, Any] = {}
        self._lock = threading.Lock()
        self._semaphore = threading.Semaphore(max_size)
        
        # Register pool
        _resource_tracker.register(
            f"pool_{id(self)}",
            'resource_pool',
            {'max_size': max_size}
        )
    
    @contextmanager
    def acquire(self):
        """Acquire a resource from the pool"""
        resource = None
        acquired = False
        
        try:
            # Wait for available resource
            acquired = self._semaphore.acquire(timeout=self.timeout)
            if not acquired:
                raise TimeoutError(f"Timeout acquiring resource from pool")
            
            with self._lock:
                # Try to get from pool
                if self._pool:
                    resource = self._pool.pop()
                else:
                    # Create new resource
                    resource = self.resource_factory()
                
                # Track in-use resource
                self._in_use[id(resource)] = resource
            
            yield resource
            
        finally:
            if resource is not None:
                with self._lock:
                    # Remove from in-use
                    self._in_use.pop(id(resource), None)
                    
                    # Return to pool if healthy
                    if len(self._pool) < self.max_size:
                        self._pool.append(resource)
                    else:
                        # Pool is full, dispose of resource
                        self._dispose_resource(resource)
            
            if acquired:
                self._semaphore.release()
    
    def _dispose_resource(self, resource: Any):
        """Dispose of a resource"""
        try:
            if hasattr(resource, 'close'):
                resource.close()
            elif hasattr(resource, 'shutdown'):
                resource.shutdown()
        except Exception as e:
            logger.error(f"Error disposing resource: {e}")
    
    def shutdown(self):
        """Shutdown the pool and all resources"""
        with self._lock:
            # Dispose all pooled resources
            while self._pool:
                resource = self._pool.pop()
                self._dispose_resource(resource)
            
            # Note: in-use resources should be returned before shutdown
            if self._in_use:
                logger.warning(f"Shutting down pool with {len(self._in_use)} resources still in use")
        
        # Unregister pool
        _resource_tracker.unregister(f"pool_{id(self)}")


def get_resource_tracker() -> ResourceTracker:
    """Get the global resource tracker"""
    return _resource_tracker


# Cleanup function for application shutdown
def cleanup_all_resources():
    """Clean up all tracked resources"""
    active_resources = _resource_tracker.get_active_resources()
    
    if active_resources:
        logger.warning(f"Cleaning up {len(active_resources)} active resources")
        
        for resource in active_resources:
            logger.info(f"Active resource: {resource['type']} (ID: {resource['id']})")
    
    # Clean up stale entries
    _resource_tracker.cleanup_stale_resources()