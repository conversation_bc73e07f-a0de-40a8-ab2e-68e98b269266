# Claude Code Implementation Prompt: Trading System Production Fixes

## Objective
Implement the critical fixes and safety systems identified in the GPT Trader V1 analysis to make the system production-ready. Focus on financial safety, operational reliability, and systematic risk management.

## Implementation Priority Order

### PHASE 1: CRITICAL SAFETY FIXES (Week 1-2)
Implement these fixes FIRST as they prevent financial loss:

#### 1. Fix Drawdown Calculation Bug
```python
# CRITICAL: Current drawdown calculation can exceed 100%
# Location: Likely in performance metrics or portfolio tracking
# Required: Implement proper drawdown validation and capping

TASK: Find and fix drawdown calculation
- Locate drawdown calculation functions
- Add validation to prevent >100% values
- Implement proper peak-to-trough calculation
- Add logging for drawdown validation failures
- Create unit tests for edge cases
```

#### 2. Integrate Trading Circuit Breaker System
```python
# NEW SYSTEM: Automatic trading suspension on failure conditions
# Integration points: Order execution, position management, error handling

TASK: Implement circuit breaker integration
- Create CircuitBreakerManager class
- Add hooks to all order execution paths
- Implement automatic trading suspension
- Add manual override capabilities
- Create alerting system for breaker trips
```

#### 3. Implement Forex Session Validator
```python
# NEW SYSTEM: Prevent trading during market closures
# Integration points: Strategy execution, order placement

TASK: Add forex market validation
- Create ForexSessionValidator class
- Integrate with order placement logic
- Add holiday calendar support
- Implement session overlap detection
- Add configuration for different brokers
```

### PHASE 2: HIGH PRIORITY FIXES (Week 3-4)

#### 4. Fix Race Conditions in Order Management
```python
# CRITICAL: Concurrent order modifications causing state corruption
# Locations: Order execution loops, position updates

TASK: Implement thread-safe order management
- Add proper locking mechanisms
- Implement atomic position updates
- Create order state validation
- Add deadlock prevention
- Implement retry logic with exponential backoff
```

#### 5. Enhance Position Sizing Safety
```python
# HIGH: Position sizing vulnerabilities leading to over-leverage
# Locations: Risk management, order sizing calculations

TASK: Bulletproof position sizing
- Add pre-trade risk validation
- Implement maximum position limits
- Add correlation-based sizing adjustments
- Create position size override protections
- Add real-time leverage monitoring
```

#### 6. Implement Comprehensive Error Recovery
```python
# HIGH: System failures causing lost trades or positions
# Locations: API error handling, state persistence

TASK: Create robust error recovery system
- Implement state persistence and recovery
- Add automatic reconnection logic
- Create orphaned order detection
- Implement position reconciliation
- Add health check monitoring
```

## Specific Implementation Requirements

### Code Integration Standards
```python
# All new code must follow these patterns:

1. LOGGING: Every critical operation must be logged
   logger.info(f"CIRCUIT_BREAKER: Trading suspended - {reason}")
   
2. VALIDATION: All inputs must be validated
   if not self._validate_position_size(size):
       raise ValidationError(f"Invalid position size: {size}")
       
3. ERROR HANDLING: Graceful degradation required
   try:
       result = execute_trade()
   except BrokerAPIError as e:
       self.circuit_breaker.trip(f"Broker API failure: {e}")
       return None
       
4. THREAD SAFETY: Use locks for shared state
   with self.position_lock:
       self._update_position(symbol, quantity)
```

### Configuration Management
```yaml
# Create production-ready configuration structure
trading_config:
  circuit_breaker:
    max_daily_loss_pct: 5.0
    max_drawdown_pct: 10.0
    consecutive_losses: 5
    
  forex_sessions:
    enabled: true
    enforce_major_sessions: true
    holiday_calendar: "forex_global"
    
  risk_management:
    max_position_size_pct: 2.0
    max_total_leverage: 3.0
    correlation_limit: 0.7
```

### Testing Requirements
```python
# Implement comprehensive testing for all fixes

1. UNIT TESTS: Test each component in isolation
   - Drawdown calculation edge cases
   - Circuit breaker trigger conditions
   - Forex session validation logic
   - Position sizing calculations

2. INTEGRATION TESTS: Test component interactions
   - Order flow with circuit breaker active
   - Position updates during forex closures
   - Error recovery during API failures

3. STRESS TESTS: Test under adverse conditions
   - High frequency order scenarios
   - Network failure recovery
   - Extreme market condition handling
```

## Implementation Checklist

### For Each Fix, Implement:
- [ ] **Core functionality** with proper error handling
- [ ] **Configuration management** for production flexibility
- [ ] **Comprehensive logging** for debugging and monitoring
- [ ] **Unit tests** covering normal and edge cases
- [ ] **Integration points** with existing codebase
- [ ] **Documentation** for operators and developers
- [ ] **Monitoring hooks** for production observability

### Integration Safety Measures:
- [ ] **Backup current codebase** before any changes
- [ ] **Feature flags** to enable/disable new functionality
- [ ] **Gradual rollout** capability for production deployment
- [ ] **Rollback procedures** if issues are discovered
- [ ] **Performance benchmarks** to ensure no degradation

## File Organization
```
Create new files following this structure:

src/
  safety/
    circuit_breaker.py        # Trading suspension system
    session_validator.py      # Forex market hours validation
    drawdown_calculator.py    # Fixed drawdown calculations
  
  risk_management/
    position_validator.py     # Enhanced position sizing
    correlation_monitor.py    # Position correlation tracking
  
  recovery/
    state_manager.py         # System state persistence
    health_monitor.py        # System health checking
  
  tests/
    test_safety_systems.py   # Safety system tests
    test_risk_management.py  # Risk management tests
    test_integration.py      # End-to-end integration tests
```

## Code Quality Requirements

### Documentation Standards
```python
class CircuitBreakerManager:
    """
    Manages trading circuit breakers to prevent cascade failures.
    
    The circuit breaker monitors various risk metrics and automatically
    suspends trading when dangerous conditions are detected.
    
    Critical Safety Features:
    - Daily loss limit monitoring
    - Consecutive loss tracking  
    - Maximum drawdown protection
    - Manual override capabilities
    
    Integration Points:
    - Order execution pipeline
    - Position management system
    - Risk monitoring dashboard
    """
```

### Performance Requirements
- **Latency**: New safety checks must add <1ms to order execution
- **Memory**: Additional monitoring should use <50MB RAM
- **CPU**: Safety systems should use <5% additional CPU

### Production Deployment
```python
# Implement staged deployment capability
class ProductionDeployment:
    def __init__(self):
        self.stage = os.getenv('DEPLOYMENT_STAGE', 'development')
        self.safety_level = os.getenv('SAFETY_LEVEL', 'maximum')
    
    def enable_feature(self, feature_name):
        """Enable features based on deployment stage"""
        if self.stage == 'paper_trading':
            return feature_name in PAPER_TRADING_FEATURES
        elif self.stage == 'limited_live':
            return feature_name in LIMITED_LIVE_FEATURES
        return True  # Full production
```

## Success Criteria

### Phase 1 Complete When:
- [ ] All critical bugs are fixed and tested
- [ ] Circuit breaker system is fully integrated
- [ ] Forex session validation is operational
- [ ] System passes all unit and integration tests
- [ ] Paper trading shows stable operation

### Phase 2 Complete When:
- [ ] Race conditions are eliminated
- [ ] Position sizing is bulletproofed
- [ ] Error recovery is comprehensive
- [ ] System handles stress tests successfully
- [ ] Production monitoring is operational

## Emergency Procedures
```python
# Implement immediate shutdown capability
def emergency_shutdown(reason: str):
    """
    EMERGENCY: Immediately halt all trading activity
    - Cancel all pending orders
    - Close all positions (if configured)
    - Suspend all strategies
    - Alert operators immediately
    """
    logger.critical(f"EMERGENCY SHUTDOWN: {reason}")
    # Implementation details...
```

## Final Validation
Before declaring production-ready:
1. **4 weeks paper trading** with all fixes active
2. **Performance benchmarks** meet latency requirements  
3. **Stress testing** passes failure scenarios
4. **Manual testing** of all emergency procedures
5. **Code review** by independent developer
6. **Security audit** of all safety systems

Remember: Financial safety takes priority over all other considerations. When in doubt, fail safe and halt trading.