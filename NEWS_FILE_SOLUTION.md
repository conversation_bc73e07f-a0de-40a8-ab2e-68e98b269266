# Simple ForexFactory News File Solution

## Problem
```
News file not found: data\forexfactory_week.json
```

## Solution
Created a simple, efficient news file manager that:

1. **Checks on startup** if `data/forexfactory_week.json` exists
2. **Downloads if missing** from `https://nfs.faireconomy.media/ff_calendar_thisweek.json`
3. **Compares hash** of local vs remote file
4. **Updates if different** hash detected

## Implementation

### Core File: `core/utils/news_file_manager.py`
- Simple class `NewsFileManager` 
- Main function `ensure_news_data()`
- Hash-based update checking
- Automatic download and validation

### Integration: `trading_loop.py`
Added news check in the `initialize()` method:
```python
# Ensure news data is available
logger.info("📰 Checking ForexFactory news data...")
if not ensure_news_data():
    logger.warning("⚠️  Could not ensure news data, continuing without it")
else:
    logger.info("✅ News data is ready")
```

## How It Works

1. **On Trading System Startup:**
   - Checks if `data/forexfactory_week.json` exists
   - If missing → downloads from API
   - If exists → compares SHA256 hash with remote
   - If hash differs → downloads update
   - If hash same → uses existing file

2. **Hash Comparison:**
   - Local file: `SHA256(data/forexfactory_week.json)`
   - Remote file: `SHA256(API response)`
   - Only downloads if hashes differ

3. **Validation:**
   - Ensures file contains valid JSON
   - Checks format is array (not object)
   - Logs event count

## Usage

### Automatic (Recommended)
Just start your trading system - it will handle news data automatically:
```bash
python trading_loop.py
```

### Manual Testing
```bash
python test_news_manager.py
```

### Direct Usage
```python
from core.utils.news_file_manager import ensure_news_data

# Call at startup
if ensure_news_data():
    print("News data ready")
else:
    print("News data unavailable")
```

## Benefits

✅ **Simple**: Single file, minimal code  
✅ **Efficient**: Only downloads when needed (hash check)  
✅ **Automatic**: Integrated into trading loop startup  
✅ **Reliable**: Handles network errors gracefully  
✅ **Fast**: Hash comparison is much faster than full download  

## Files

- `core/utils/news_file_manager.py` - Main implementation
- `trading_loop.py` - Integration point (modified)
- `test_news_manager.py` - Test script
- `data/forexfactory_week.json` - News data file (auto-created)

## Error Handling

- **Network errors**: Logs warning, continues with existing file
- **Invalid JSON**: Attempts re-download
- **Missing file**: Downloads automatically
- **Hash check fails**: Uses existing file, logs warning

The system will never crash due to news file issues - it gracefully handles all error conditions.
