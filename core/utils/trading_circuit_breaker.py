"""
Enhanced Trading Circuit Breaker System
Provides comprehensive protection against cascade failures in trading operations
"""

import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Callable
from enum import Enum
from pathlib import Path
import json

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"  # Normal operation
    OPEN = "open"      # Blocking all operations
    HALF_OPEN = "half_open"  # Testing recovery


@dataclass
class TradingCircuitConfig:
    """Configuration for trading-specific circuit breaker"""
    # Failure thresholds
    max_failures: int = 5
    time_window: timedelta = timedelta(minutes=15)
    failure_threshold_percent: float = 50.0
    
    # Recovery settings
    cooldown_period: timedelta = timedelta(minutes=30)
    half_open_max_attempts: int = 3
    recovery_time: timedelta = timedelta(minutes=5)
    
    # Trading limits
    max_daily_loss_percent: float = 5.0
    max_consecutive_losses: int = 3
    max_drawdown_percent: float = 10.0
    
    # Emergency thresholds
    emergency_stop_loss_percent: float = 3.0  # Single trade loss
    margin_call_threshold_percent: float = 20.0  # Account equity threshold
    
    # Critical errors that immediately trip
    critical_errors: Set[str] = field(default_factory=lambda: {
        "InsufficientFundsError",
        "MarginCallError",
        "MT5ConnectionError",
        "BrokerRejectionError",
        "InvalidLicenseError"
    })


class TradingMetrics:
    """Track comprehensive trading metrics"""
    
    def __init__(self):
        self.total_operations: int = 0
        self.failed_operations: int = 0
        self.total_trades: int = 0
        self.losing_trades: int = 0
        self.consecutive_losses: int = 0
        self.daily_loss_percent: float = 0.0
        self.max_drawdown_percent: float = 0.0
        self.errors_by_type: Dict[str, int] = {}
        self.last_error_time: Optional[datetime] = None
        self.last_success_time: Optional[datetime] = None
        self.session_start_balance: float = 0.0
        self.current_balance: float = 0.0
    
    @property
    def error_rate(self) -> float:
        """Calculate current error rate"""
        if self.total_operations == 0:
            return 0.0
        return (self.failed_operations / self.total_operations) * 100
    
    @property
    def win_rate(self) -> float:
        """Calculate win rate"""
        if self.total_trades == 0:
            return 0.0
        return ((self.total_trades - self.losing_trades) / self.total_trades) * 100
    
    def update_balance(self, new_balance: float):
        """Update current balance and calculate drawdown"""
        self.current_balance = new_balance
        if self.session_start_balance > 0:
            loss_percent = ((self.session_start_balance - new_balance) / self.session_start_balance) * 100
            self.max_drawdown_percent = max(self.max_drawdown_percent, loss_percent)


class TradingCircuitBreaker:
    """
    Enhanced circuit breaker for trading operations with comprehensive safety features
    """
    
    def __init__(self, name: str, config: Optional[TradingCircuitConfig] = None):
        self.name = name
        self.config = config or TradingCircuitConfig()
        self.state = CircuitState.CLOSED
        self.metrics = TradingMetrics()
        
        # Failure tracking
        self.failure_timestamps: List[datetime] = []
        self.last_state_change: datetime = datetime.now()
        self.circuit_open_until: Optional[datetime] = None
        
        # Half-open state
        self.half_open_attempts: int = 0
        self.half_open_successes: int = 0
        
        # Callbacks
        self.state_change_callbacks: List[Callable] = []
        
        # Persistence
        self.state_file = Path("data/circuit_breaker_state.json")
        self._load_state()
    
    def can_execute(self, operation: str, amount: Optional[float] = None) -> tuple[bool, str]:
        """
        Check if operation can be executed
        
        Returns:
            (allowed, reason)
        """
        self._update_state()
        
        if self.state == CircuitState.OPEN:
            time_remaining = (self.circuit_open_until - datetime.now()).total_seconds() if self.circuit_open_until else 0
            return False, f"Circuit OPEN: {operation} blocked for {time_remaining:.0f}s"
        
        if self.state == CircuitState.HALF_OPEN:
            if self.half_open_attempts >= self.config.half_open_max_attempts:
                return False, f"Circuit HALF_OPEN: max test attempts reached"
            self.half_open_attempts += 1
        
        # Check trading-specific conditions
        if amount and self.metrics.session_start_balance > 0:
            potential_loss_percent = (amount / self.metrics.session_start_balance) * 100
            if potential_loss_percent > self.config.emergency_stop_loss_percent:
                return False, f"Trade size {potential_loss_percent:.1f}% exceeds emergency limit"
        
        # Check daily loss limit
        if self.metrics.daily_loss_percent >= self.config.max_daily_loss_percent:
            return False, f"Daily loss limit reached: {self.metrics.daily_loss_percent:.1f}%"
        
        # Check consecutive losses
        if self.metrics.consecutive_losses >= self.config.max_consecutive_losses:
            return False, f"Consecutive loss limit reached: {self.metrics.consecutive_losses}"
        
        # Check margin level
        if self.metrics.session_start_balance > 0:
            equity_percent = (self.metrics.current_balance / self.metrics.session_start_balance) * 100
            if equity_percent < self.config.margin_call_threshold_percent:
                return False, f"Margin call level: equity at {equity_percent:.1f}%"
        
        return True, "OK"
    
    def record_success(self, operation: str):
        """Record successful operation"""
        self.metrics.total_operations += 1
        self.metrics.last_success_time = datetime.now()
        
        if self.state == CircuitState.HALF_OPEN:
            self.half_open_successes += 1
            if self.half_open_successes >= self.config.half_open_max_attempts:
                self._transition_to_closed()
        
        logger.debug(f"Circuit {self.name}: {operation} succeeded")
    
    def record_failure(self, operation: str, error: Exception):
        """Record failed operation"""
        error_type = type(error).__name__
        now = datetime.now()
        
        # Update metrics
        self.metrics.total_operations += 1
        self.metrics.failed_operations += 1
        self.metrics.last_error_time = now
        self.metrics.errors_by_type[error_type] = self.metrics.errors_by_type.get(error_type, 0) + 1
        
        # Record failure timestamp
        self.failure_timestamps.append(now)
        self._cleanup_old_failures()
        
        logger.warning(f"Circuit {self.name}: {operation} failed with {error_type}")
        
        # Check critical errors
        if error_type in self.config.critical_errors:
            self._transition_to_open(f"Critical error: {error_type}")
            return
        
        # Check failure thresholds
        if self._should_trip():
            self._transition_to_open(f"Failure threshold exceeded")
        
        # If half-open, return to open
        if self.state == CircuitState.HALF_OPEN:
            self._transition_to_open(f"Failure during recovery")
    
    def record_trade_result(self, profit: float, balance: float):
        """Record trade result"""
        self.metrics.total_trades += 1
        self.metrics.current_balance = balance
        
        if profit < 0:
            self.metrics.losing_trades += 1
            self.metrics.consecutive_losses += 1
            loss_percent = abs(profit / self.metrics.session_start_balance) * 100 if self.metrics.session_start_balance > 0 else 0
            self.metrics.daily_loss_percent += loss_percent
            
            # Check emergency stop
            if loss_percent >= self.config.emergency_stop_loss_percent:
                self._transition_to_open(f"Emergency stop: {loss_percent:.1f}% loss")
        else:
            self.metrics.consecutive_losses = 0
        
        # Update drawdown
        self.metrics.update_balance(balance)
        
        # Check limits
        if self.metrics.daily_loss_percent >= self.config.max_daily_loss_percent:
            self._transition_to_open(f"Daily loss limit: {self.metrics.daily_loss_percent:.1f}%")
        elif self.metrics.max_drawdown_percent >= self.config.max_drawdown_percent:
            self._transition_to_open(f"Max drawdown: {self.metrics.max_drawdown_percent:.1f}%")
    
    def _should_trip(self) -> bool:
        """Check if circuit should trip"""
        # Failure count check
        if len(self.failure_timestamps) >= self.config.max_failures:
            return True
        
        # Error rate check
        if self.metrics.error_rate >= 50:  # 50% error rate
            return True
        
        return False
    
    def _cleanup_old_failures(self):
        """Remove failures outside time window"""
        cutoff = datetime.now() - self.config.time_window
        self.failure_timestamps = [ts for ts in self.failure_timestamps if ts > cutoff]
    
    def _update_state(self):
        """Update circuit state based on time"""
        if self.state == CircuitState.OPEN:
            if self.circuit_open_until and datetime.now() >= self.circuit_open_until:
                self._transition_to_half_open()
    
    def _transition_to_open(self, reason: str):
        """Transition to OPEN state"""
        self.state = CircuitState.OPEN
        self.last_state_change = datetime.now()
        self.circuit_open_until = self.last_state_change + self.config.cooldown_period
        
        logger.critical(f"🚨 Circuit {self.name} OPENED: {reason}")
        logger.critical(f"Operations blocked until {self.circuit_open_until}")
        
        self._notify_state_change(CircuitState.OPEN, reason)
        self._save_state()
    
    def _transition_to_half_open(self):
        """Transition to HALF_OPEN state"""
        self.state = CircuitState.HALF_OPEN
        self.last_state_change = datetime.now()
        self.half_open_attempts = 0
        self.half_open_successes = 0
        
        logger.warning(f"⚠️ Circuit {self.name} HALF_OPEN: Testing recovery")
        
        self._notify_state_change(CircuitState.HALF_OPEN, "Testing recovery")
        self._save_state()
    
    def _transition_to_closed(self):
        """Transition to CLOSED state"""
        self.state = CircuitState.CLOSED
        self.last_state_change = datetime.now()
        self.circuit_open_until = None
        self.failure_timestamps.clear()
        
        logger.info(f"✅ Circuit {self.name} CLOSED: Normal operation resumed")
        
        self._notify_state_change(CircuitState.CLOSED, "Recovered")
        self._save_state()
    
    def _notify_state_change(self, new_state: CircuitState, reason: str):
        """Notify callbacks of state change"""
        for callback in self.state_change_callbacks:
            try:
                callback(self.name, new_state, reason)
            except Exception as e:
                logger.error(f"Error in state change callback: {e}")
    
    def _save_state(self):
        """Persist circuit breaker state"""
        try:
            state_data = {
                'name': self.name,
                'state': self.state.value,
                'last_state_change': self.last_state_change.isoformat(),
                'circuit_open_until': self.circuit_open_until.isoformat() if self.circuit_open_until else None,
                'metrics': {
                    'daily_loss_percent': self.metrics.daily_loss_percent,
                    'consecutive_losses': self.metrics.consecutive_losses,
                    'max_drawdown_percent': self.metrics.max_drawdown_percent
                }
            }
            
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            self.state_file.write_text(json.dumps(state_data, indent=2))
            
        except Exception as e:
            logger.error(f"Failed to save circuit breaker state: {e}")
    
    def _load_state(self):
        """Load persisted state"""
        try:
            if self.state_file.exists():
                state_data = json.loads(self.state_file.read_text())
                
                # Restore state if circuit was open
                if state_data['state'] == 'open' and state_data.get('circuit_open_until'):
                    open_until = datetime.fromisoformat(state_data['circuit_open_until'])
                    if open_until > datetime.now():
                        self.state = CircuitState.OPEN
                        self.circuit_open_until = open_until
                        logger.warning(f"Restored circuit {self.name} in OPEN state until {open_until}")
                
                # Restore metrics
                metrics = state_data.get('metrics', {})
                self.metrics.daily_loss_percent = metrics.get('daily_loss_percent', 0)
                self.metrics.consecutive_losses = metrics.get('consecutive_losses', 0)
                self.metrics.max_drawdown_percent = metrics.get('max_drawdown_percent', 0)
                
        except Exception as e:
            logger.error(f"Failed to load circuit breaker state: {e}")
    
    def reset_daily_metrics(self):
        """Reset daily metrics (call at start of trading day)"""
        self.metrics.daily_loss_percent = 0.0
        self.metrics.consecutive_losses = 0
        logger.info(f"Circuit {self.name}: Daily metrics reset")
    
    def set_session_balance(self, balance: float):
        """Set session starting balance"""
        self.metrics.session_start_balance = balance
        self.metrics.current_balance = balance
    
    def force_close(self):
        """Force circuit to closed state (use with caution)"""
        self._transition_to_closed()
        logger.warning(f"Circuit {self.name} forcefully closed by operator")
    
    def get_status(self) -> Dict:
        """Get comprehensive circuit status"""
        return {
            'name': self.name,
            'state': self.state.value,
            'can_trade': self.state == CircuitState.CLOSED,
            'metrics': {
                'error_rate': f"{self.metrics.error_rate:.1f}%",
                'win_rate': f"{self.metrics.win_rate:.1f}%",
                'daily_loss': f"{self.metrics.daily_loss_percent:.1f}%",
                'max_drawdown': f"{self.metrics.max_drawdown_percent:.1f}%",
                'consecutive_losses': self.metrics.consecutive_losses,
                'total_operations': self.metrics.total_operations,
                'failed_operations': self.metrics.failed_operations
            },
            'circuit_open_until': self.circuit_open_until.isoformat() if self.circuit_open_until else None,
            'last_state_change': self.last_state_change.isoformat()
        }


# Global instance
_trading_circuit_breaker: Optional[TradingCircuitBreaker] = None


def get_trading_circuit_breaker() -> TradingCircuitBreaker:
    """Get the global trading circuit breaker"""
    global _trading_circuit_breaker
    
    if _trading_circuit_breaker is None:
        _trading_circuit_breaker = TradingCircuitBreaker("main_trading")
    
    return _trading_circuit_breaker