#!/bin/bash
# Start trading system from WSL with proper encoding

echo "Starting GPT Trading System from WSL..."
echo "======================================"
echo

# Set UTF-8 encoding
export PYTHONIOENCODING=utf-8
export PYTHONUTF8=1

# Run the trading system with Windows Python
cmd.exe /c "set PYTHONIOENCODING=utf-8 && set PYTHONUTF8=1 && D:\\gpt_trader_v1\\venv\\Scripts\\python.exe -X utf8 D:\\gpt_trader_v1\\trading_loop.py"