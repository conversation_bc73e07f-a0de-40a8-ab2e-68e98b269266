# Project Cleanup Plan

## Summary
This document identifies files that can be safely removed from the GPT Trader v1 project to reduce clutter and improve maintainability.

## 1. Duplicate Directory (HIGHEST PRIORITY)
- **`/cleaned-repo/`** - This is an entire duplicate of the project (177MB+)
  - Contains all the same files as the main project
  - Not referenced anywhere in the main codebase
  - Safe to remove entirely

## 2. Redundant Test Files in Root Directory
These test files in the root should be moved to the `tests/` directory or removed if redundant:

### MarketAux Test Files (Multiple versions of the same test)
- `test_marketaux_improved.py` - Earlier iteration
- `test_marketaux_proper.py` - Earlier iteration  
- `test_marketaux_search.py` - Earlier iteration
- `test_marketaux_final.py` - Keep this one if needed
- `test_marketaux_optimized.py` - Keep this one (most comprehensive)

### Import and Fix Test Files (Temporary debugging files)
- `test_imports.py` - Temporary import check
- `test_circular_import_fix.py` - Temporary fix verification
- `test_decision_imports.py` - Temporary import check
- `test_backtest_imports.py` - Temporary import check
- `fix_all_imports.py` - One-time fix script

### Dashboard Test Files
- `test_dashboard_fixes.py` - Temporary fix verification
- `fix_dashboard_issues.py` - One-time fix script

### Other Test Files (Should be in tests/ directory)
- `test_forex_news.py`
- `test_cache_system.py`
- `test_ml_integration.py`
- `test_position_mode_simple.py`
- `test_position_trading.py`
- `test_position_trading_data.py`
- `test_pre_filter.py`
- `test_enhanced_backtest.py`
- `test_ml_only_backtest.py`
- `test_backtest_cost_optimization.py`

## 3. Duplicate/Abandoned Features

### Enhanced/Alternative Agent Implementations
- `core/agents/enhanced_base_agent.py` - Not used (agents inherit from base_agent.py)
- `core/agents/enhanced_technical_analyst.py` - Not used (using technical_analyst.py)
- `core/agents/vsa_trader.py` - Not integrated into council (abandoned feature)

### Duplicate Service Implementations
- `core/services/signal_service.py` - Deprecated (using council_signal_service.py)
- `core/services/enhanced_council_signal_service.py` - Check if actively used or superseded
- `core/services/ab_testing_service.py` - Not referenced anywhere
- `core/services/offline_validator.py` - Not actively used

### Duplicate Infrastructure Files
- `core/infrastructure/mt5/order_manager_updated.py` - Temporary update file
- `core/infrastructure/mt5/pooled_client.py` - Check if replaced by connection_pool.py
- `core/infrastructure/database/pooled_repositories.py` - Check if replaced by async_repositories.py

### Unused Agent Types
- `core/agents/commodity_specialist.py` - Not integrated into council
- `core/agents/index_specialist.py` - Not integrated into council
- `core/agents/simplified_agents.py` - Appears to be experimental
- `core/agents/council_types.py` - Check if still needed

### Duplicate Config Files
- `config/ftmo_symbols.py` - Check if replaced by symbols.py
- `config/position_trading_config.py` - Check if used
- `config/backtest_config.py` - Check if functionality moved to settings.py

## 4. Script Duplicates

### Dashboard Scripts (Multiple versions)
- `scripts/run_dashboard.py` - Earlier version
- `scripts/run_dashboard_fixed.py` - Fix iteration
- `scripts/trading_dashboard_simple.py` - Earlier version
Keep only:
- `scripts/comprehensive_trading_dashboard.py` - Main dashboard
- `scripts/run_simple_dashboard.py` - If still needed

### Security Scripts (One-time migrations)
- `scripts/migrate_models_to_secure.py` - One-time migration
- `scripts/secure_all_dashboards.py` - One-time security update
- `scripts/pin_dependencies.py` - One-time dependency pinning
- `scripts/check_dependencies_security.py` - One-time check

## 5. Debug and Temporary Files
- `debug_marketaux_forex.py`
- `debug_mt5_info.py`  
- `debug_order_check.py`
- `check_gpt_requests.py`
- `check_marketaux_status.py`
- `get_any_forex_news.py`

## 6. Unused Utility Modules

### Error Handling (Multiple implementations)
- `core/utils/error_handling.py` - Check which is primary
- `core/utils/exception_handling.py` - Check which is primary
- `core/utils/error_handler.py` - Check which is primary

### ML Related (Check if superseded)
- `core/ml/secure_ml_predictor.py` - If not using secure loading
- `core/ml/secure_model_loader.py` - If not using secure loading

### Services
- `core/services/llm_simulator.py` - Check if used in backtesting
- `core/services/council_optimizer.py` - Not referenced
- `core/services/pre_trade_filter.py` - Check if integrated
- `core/services/market_type_detector.py` - Check if used
- `core/services/decision_recorder.py` - Check if used
- `core/services/decision_types.py` - Check if used
- `core/services/position_monitor.py` - Check if used
- `core/services/async_trade_service.py` - Check if replaced

### Infrastructure
- `core/infrastructure/cache/` - Check if cache system is used
- `core/infrastructure/mt5/mt5_retry_patch.py` - Check if integrated

## 7. Documentation Files to Consolidate
Consider consolidating these into a single docs structure:
- Multiple MD files in root (move to docs/)
- `dashboard_fixes_summary.md`
- `AGENT_IMPROVEMENTS_SUMMARY.md`
- `FIX_SUMMARY.md`
- `ML_INTEGRATION_PLAN.md`
- `ML_INTEGRATION_SUMMARY.md`
- `MT5_TEST_FIXES.md`

## 8. Old Backup Files
Review and potentially remove old backups:
- `backups/database/trades_db_manual_20250602_051839.db.gz` - Over 1 week old
- `backups/database/trades_db_daily_20250603_130327.db.gz` - Over 1 week old

## Recommended Cleanup Order

1. **Immediate Removal** (No dependencies):
   - `/cleaned-repo/` directory
   - All test_*.py files in root (after verifying functionality)
   - Debug files (debug_*.py)
   - One-time fix scripts

2. **After Verification** (Check references):
   - Duplicate service implementations
   - Enhanced/abandoned agent implementations
   - Unused config files
   - Duplicate error handling utilities

3. **Careful Review** (May have hidden dependencies):
   - Infrastructure duplicates
   - Cache system if unused
   - Security-related files after confirming migration

## Space Savings Estimate
- Removing `/cleaned-repo/`: ~177MB
- Removing test files from root: ~200KB
- Removing unused modules: ~500KB
- Total estimated savings: ~178MB

## Next Steps
1. Back up the project before cleanup
2. Remove files in the recommended order
3. Run full test suite after each batch of removals
4. Update imports if any files are consolidated
5. Update documentation to reflect new structure