version: '3.8'

services:
  trading-system:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
    container_name: gpt-trader
    restart: unless-stopped
    
    environment:
      - PYTHONUNBUFFERED=1
      - TZ=UTC
      
    env_file:
      - ../.env
      
    volumes:
      # Data persistence
      - ../data:/app/data
      - ../logs:/app/logs
      - ../screenshots:/app/screenshots
      - ../models:/app/models
      - ../backups:/app/backups
      
      # Configuration
      - ../.env:/app/.env:ro
      
      # MT5 files directory (read-only)
      - ${MT5_FILES_DIR}:/mt5_files:ro
      
    networks:
      - trading-network
      
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # Dashboard service
  dashboard:
    build:
      context: ..
      dockerfile: deploy/Dockerfile.dashboard
    container_name: gpt-trader-dashboard
    restart: unless-stopped
    
    ports:
      - "8050:8050"  # Dash default port
      
    environment:
      - PYTHONUNBUFFERED=1
      
    volumes:
      - ../data:/app/data:ro
      - ../logs:/app/logs:ro
      
    networks:
      - trading-network
      
    depends_on:
      - trading-system

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: gpt-trader-prometheus
    restart: unless-stopped
    
    ports:
      - "9090:9090"
      
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
      
    networks:
      - trading-network
      
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: gpt-trader-grafana
    restart: unless-stopped
    
    ports:
      - "3000:3000"
      
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
      
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
      
    networks:
      - trading-network
      
    depends_on:
      - prometheus

networks:
  trading-network:
    driver: bridge

volumes:
  prometheus-data:
  grafana-data: