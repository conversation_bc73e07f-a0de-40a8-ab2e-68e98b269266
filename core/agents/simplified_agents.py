"""
Simplified Trading Agents for Cost-Effective Backtesting
Optimized for minimal token usage while maintaining essential functionality
"""

import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import logging

from core.domain.models import MarketData, TradingSignal, SignalType, RiskClass
from core.agents.base_agent import TradingAgent, AgentType, AgentAnalysis, DebateResponse
from config.backtest_config import BacktestPromptTemplates
from core.infrastructure.gpt.client import GPTClient

logger = logging.getLogger(__name__)


class SimplifiedTechnicalAnalyst(TradingAgent):
    """Simplified technical analyst for backtesting - minimal token usage"""
    
    def __init__(self, gpt_client: GPTClient):
        super().__init__(
            AgentType.TECHNICAL_ANALYST,
            "data-driven",
            "chart patterns and indicators"
        )
        self.gpt_client = gpt_client
        self.templates = BacktestPromptTemplates()
    
    def analyze(
        self,
        market_data: Dict[str, MarketData],
        news_context: Optional[List[str]] = None,
        ml_context: Optional[Dict[str, Any]] = None
    ) -> AgentAnalysis:
        """Simplified technical analysis with minimal tokens"""
        
        h1_data = market_data.get('h1')
        h4_data = market_data.get('h4')
        
        if not h1_data or not h1_data.candles:
            return self._create_wait_signal("Insufficient data")
        
        # Create ultra-concise data summary
        h1_summary = self._create_concise_summary(h1_data)
        h4_summary = self._create_concise_summary(h4_data) if h4_data else "N/A"
        
        # Use simplified prompt
        prompt = self.templates.TECHNICAL_CONCISE.format(
            symbol=h1_data.symbol,
            current_price=h1_data.candles[-1].close,
            h1_summary=h1_summary,
            h4_summary=h4_summary
        )
        
        try:
            # Call GPT with simplified settings
            response = self.gpt_client.analyze_with_response(
                prompt=prompt,
                temperature=0.1,
                agent_type=self.agent_type.value,
                symbol=h1_data.symbol
            )
            
            # Parse JSON response
            result = json.loads(response)
            
            return AgentAnalysis(
                agent_type=self.agent_type,
                recommendation=SignalType[result['signal']],
                confidence=float(result['confidence']),
                reasoning=result.get('reasons', []),
                concerns=[],  # Skip concerns in simplified mode
                entry_price=result.get('entry'),
                stop_loss=result.get('sl'),
                take_profit=result.get('tp')
            )
            
        except Exception as e:
            logger.error(f"Simplified technical analysis failed: {e}")
            return self._create_wait_signal(f"Analysis error: {str(e)}")
    
    def _create_concise_summary(self, data: MarketData) -> str:
        """Create ultra-concise market summary"""
        if not data or not data.candles:
            return "No data"
        
        candles = data.candles[-20:]  # Last 20 candles
        current = candles[-1]
        
        # Calculate key metrics
        highs = [c.high for c in candles]
        lows = [c.low for c in candles]
        closes = [c.close for c in candles]
        
        trend = "UP" if closes[-1] > closes[0] else "DOWN"
        volatility = (max(highs) - min(lows)) / current.close * 100
        
        return f"P:{current.close:.5f} T:{trend} V:{volatility:.1f}% H:{max(highs):.5f} L:{min(lows):.5f}"
    
    def _create_wait_signal(self, reason: str) -> AgentAnalysis:
        """Create a WAIT signal"""
        return AgentAnalysis(
            agent_type=self.agent_type,
            recommendation=SignalType.WAIT,
            confidence=0.0,
            reasoning=[reason],
            concerns=[]
        )
    
    def debate(
        self,
        other_analyses: List[AgentAnalysis],
        round_number: int,
        previous_responses: Optional[List[DebateResponse]] = None
    ) -> DebateResponse:
        """Skip debates in simplified mode"""
        return DebateResponse(
            agent_type=self.agent_type,
            round=round_number,
            statement="Maintaining position based on technical data",
            maintains_position=True,
            updated_confidence=None
        )


class SimplifiedFundamentalAnalyst(TradingAgent):
    """Simplified fundamental analyst for backtesting"""
    
    def __init__(self, gpt_client: GPTClient):
        super().__init__(
            AgentType.FUNDAMENTAL_ANALYST,
            "macro-aware",
            "economic news and fundamentals"
        )
        self.gpt_client = gpt_client
        self.templates = BacktestPromptTemplates()
    
    def analyze(
        self,
        market_data: Dict[str, MarketData],
        news_context: Optional[List[str]] = None,
        ml_context: Optional[Dict[str, Any]] = None
    ) -> AgentAnalysis:
        """Simplified fundamental analysis"""
        
        h1_data = market_data.get('h1')
        if not h1_data:
            return self._create_wait_signal("No market data")
        
        # If no news, return neutral
        if not news_context:
            return AgentAnalysis(
                agent_type=self.agent_type,
                recommendation=SignalType.WAIT,
                confidence=50.0,
                reasoning=["No significant news"],
                concerns=[]
            )
        
        # Create news summary
        news_summary = " | ".join(news_context[:3])  # Max 3 news items
        
        prompt = self.templates.FUNDAMENTAL_CONCISE.format(
            symbol=h1_data.symbol,
            current_price=h1_data.candles[-1].close,
            news_summary=news_summary
        )
        
        try:
            response = self.gpt_client.analyze_with_response(
                prompt=prompt,
                temperature=0.1,
                agent_type=self.agent_type.value,
                symbol=h1_data.symbol
            )
            
            result = json.loads(response)
            
            return AgentAnalysis(
                agent_type=self.agent_type,
                recommendation=SignalType[result['signal']],
                confidence=float(result['confidence']),
                reasoning=result.get('reasons', []),
                concerns=[]
            )
            
        except Exception as e:
            logger.error(f"Simplified fundamental analysis failed: {e}")
            return self._create_wait_signal(f"Analysis error: {str(e)}")
    
    def _create_wait_signal(self, reason: str) -> AgentAnalysis:
        """Create a WAIT signal"""
        return AgentAnalysis(
            agent_type=self.agent_type,
            recommendation=SignalType.WAIT,
            confidence=0.0,
            reasoning=[reason],
            concerns=[]
        )
    
    def debate(self, other_analyses, round_number, previous_responses=None):
        """Skip debates in simplified mode"""
        return DebateResponse(
            agent_type=self.agent_type,
            round=round_number,
            statement="Maintaining position",
            maintains_position=True
        )


class SimplifiedRiskManager(TradingAgent):
    """Simplified risk manager for backtesting"""
    
    def __init__(self, gpt_client: GPTClient):
        super().__init__(
            AgentType.RISK_MANAGER,
            "protective",
            "capital preservation"
        )
        self.gpt_client = gpt_client
        self.templates = BacktestPromptTemplates()
    
    def analyze(
        self,
        market_data: Dict[str, MarketData],
        news_context: Optional[List[str]] = None,
        ml_context: Optional[Dict[str, Any]] = None
    ) -> AgentAnalysis:
        """Simplified risk analysis"""
        
        # For simplified backtesting, use rule-based risk checks
        h1_data = market_data.get('h1')
        if not h1_data or not h1_data.candles:
            return self._create_veto_signal("Insufficient data")
        
        # Simple volatility check
        candles = h1_data.candles[-20:]
        highs = [c.high for c in candles]
        lows = [c.low for c in candles]
        current = candles[-1].close
        
        volatility = (max(highs) - min(lows)) / current * 100
        
        # Rule-based risk assessment
        if volatility > 5.0:  # High volatility
            confidence = 30.0
            reasoning = ["High volatility environment"]
        elif volatility > 3.0:  # Medium volatility
            confidence = 60.0
            reasoning = ["Moderate volatility"]
        else:  # Low volatility
            confidence = 80.0
            reasoning = ["Stable market conditions"]
        
        return AgentAnalysis(
            agent_type=self.agent_type,
            recommendation=SignalType.WAIT,  # Risk manager doesn't generate signals
            confidence=confidence,
            reasoning=reasoning,
            concerns=["Volatility: {:.2f}%".format(volatility)]
        )
    
    def _create_veto_signal(self, reason: str) -> AgentAnalysis:
        """Create a veto signal"""
        return AgentAnalysis(
            agent_type=self.agent_type,
            recommendation=SignalType.WAIT,
            confidence=0.0,
            reasoning=[reason],
            concerns=[reason]
        )
    
    def debate(self, other_analyses, round_number, previous_responses=None):
        """Skip debates in simplified mode"""
        return DebateResponse(
            agent_type=self.agent_type,
            round=round_number,
            statement="Risk assessment complete",
            maintains_position=True
        )


class SimplifiedHeadTrader(TradingAgent):
    """Simplified head trader for backtesting - synthesizes decisions"""
    
    def __init__(self, gpt_client: GPTClient):
        super().__init__(
            AgentType.HEAD_TRADER,
            "decisive",
            "final trade decisions"
        )
        self.gpt_client = gpt_client
    
    def analyze(
        self,
        market_data: Dict[str, MarketData],
        news_context: Optional[List[str]] = None,
        ml_context: Optional[Dict[str, Any]] = None
    ) -> AgentAnalysis:
        """Head trader doesn't analyze independently in simplified mode"""
        return AgentAnalysis(
            agent_type=self.agent_type,
            recommendation=SignalType.WAIT,
            confidence=0.0,
            reasoning=["Awaiting council input"],
            concerns=[]
        )
    
    def synthesize_decision(
        self,
        agent_analyses: List[AgentAnalysis],
        market_data: Dict[str, MarketData]
    ) -> TradingSignal:
        """Simplified decision synthesis - rule-based instead of GPT"""
        
        # Count votes
        buy_votes = sum(1 for a in agent_analyses if a.recommendation == SignalType.BUY)
        sell_votes = sum(1 for a in agent_analyses if a.recommendation == SignalType.SELL)
        
        # Calculate average confidence
        confidences = [a.confidence for a in agent_analyses if a.confidence > 0]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        # Simple majority rule
        if buy_votes > sell_votes and buy_votes >= 2:
            signal_type = SignalType.BUY
        elif sell_votes > buy_votes and sell_votes >= 2:
            signal_type = SignalType.SELL
        else:
            signal_type = SignalType.WAIT
        
        # Get entry/exit levels from technical analyst
        tech_analysis = next((a for a in agent_analyses if a.agent_type == AgentType.TECHNICAL_ANALYST), None)
        
        if signal_type != SignalType.WAIT and tech_analysis:
            h1_data = market_data.get('h1')
            
            return TradingSignal(
                symbol=h1_data.symbol,
                signal_type=signal_type,
                entry_price=tech_analysis.entry_price or h1_data.candles[-1].close,
                stop_loss=tech_analysis.stop_loss,
                take_profit=tech_analysis.take_profit,
                confidence=avg_confidence,
                risk_reward_ratio=tech_analysis.risk_reward_ratio,
                reasoning=self._combine_reasoning(agent_analyses),
                risk_class=self._determine_risk_class(avg_confidence)
            )
        
        return TradingSignal(
            symbol=market_data.get('h1').symbol,
            signal_type=SignalType.WAIT,
            confidence=avg_confidence,
            reasoning=["No consensus reached"],
            risk_class=RiskClass.NO_TRADE
        )
    
    def _combine_reasoning(self, analyses: List[AgentAnalysis]) -> List[str]:
        """Combine reasoning from all agents"""
        all_reasons = []
        for analysis in analyses:
            if analysis.reasoning:
                all_reasons.extend(analysis.reasoning[:1])  # Take top reason from each
        return all_reasons[:3]  # Max 3 reasons
    
    def _determine_risk_class(self, confidence: float) -> RiskClass:
        """Determine risk class based on confidence"""
        if confidence >= 80:
            return RiskClass.STANDARD
        elif confidence >= 60:
            return RiskClass.ELEVATED
        else:
            return RiskClass.HIGH
    
    def debate(self, other_analyses, round_number, previous_responses=None):
        """Skip debates in simplified mode"""
        return DebateResponse(
            agent_type=self.agent_type,
            round=round_number,
            statement="Decision synthesized",
            maintains_position=True
        )


# Simplified agents for other types (minimal implementation)
class SimplifiedMomentumTrader(SimplifiedTechnicalAnalyst):
    """Simplified momentum trader - reuses technical analyst logic"""
    
    def __init__(self, gpt_client: GPTClient):
        super().__init__(gpt_client)
        self.agent_type = AgentType.MOMENTUM_TRADER
        self.personality = "trend-following"
        self.specialty = "momentum and trends"


class SimplifiedContrarianTrader(SimplifiedTechnicalAnalyst):
    """Simplified contrarian trader - reuses technical analyst logic"""
    
    def __init__(self, gpt_client: GPTClient):
        super().__init__(gpt_client)
        self.agent_type = AgentType.CONTRARIAN_TRADER
        self.personality = "contrarian"
        self.specialty = "reversals and fades"


class SimplifiedSentimentReader(SimplifiedFundamentalAnalyst):
    """Simplified sentiment reader - reuses fundamental analyst logic"""
    
    def __init__(self, gpt_client: GPTClient):
        super().__init__(gpt_client)
        self.agent_type = AgentType.SENTIMENT_READER
        self.personality = "intuitive"
        self.specialty = "market psychology"


def create_simplified_agent(agent_type: AgentType, gpt_client: GPTClient) -> TradingAgent:
    """Factory function to create simplified agents"""
    
    agent_map = {
        AgentType.TECHNICAL_ANALYST: SimplifiedTechnicalAnalyst,
        AgentType.FUNDAMENTAL_ANALYST: SimplifiedFundamentalAnalyst,
        AgentType.SENTIMENT_READER: SimplifiedSentimentReader,
        AgentType.RISK_MANAGER: SimplifiedRiskManager,
        AgentType.MOMENTUM_TRADER: SimplifiedMomentumTrader,
        AgentType.CONTRARIAN_TRADER: SimplifiedContrarianTrader,
        AgentType.HEAD_TRADER: SimplifiedHeadTrader
    }
    
    agent_class = agent_map.get(agent_type)
    if not agent_class:
        raise ValueError(f"Unknown agent type: {agent_type}")
    
    return agent_class(gpt_client)