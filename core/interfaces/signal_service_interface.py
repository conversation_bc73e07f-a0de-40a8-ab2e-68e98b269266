"""
Signal service interface for generating trading signals.
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from core.domain.models import TradingSignal


class ISignalService(ABC):
    """Interface for signal generation services"""
    
    @abstractmethod
    async def generate_signal(self, symbol: str) -> TradingSignal:
        """
        Generate a trading signal for the given symbol.
        
        Args:
            symbol: Trading symbol to analyze
            
        Returns:
            TradingSignal object with trading recommendation
        """
        pass
    
    @abstractmethod
    async def generate_signal_with_ml(self, symbol: str) -> TradingSignal:
        """
        Generate a trading signal using ML predictions.
        
        Args:
            symbol: Trading symbol to analyze
            
        Returns:
            TradingSignal object with ML-enhanced recommendation
        """
        pass
    
    @abstractmethod
    async def validate_symbol_readiness(self, symbol: str) -> bool:
        """
        Check if symbol is ready for analysis.
        
        Args:
            symbol: Trading symbol to validate
            
        Returns:
            True if symbol is ready, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_signal_history(self, symbol: str, limit: int = 10) -> list:
        """
        Get recent signal history for a symbol.
        
        Args:
            symbol: Trading symbol
            limit: Maximum number of signals to return
            
        Returns:
            List of recent signals
        """
        pass
    
    @abstractmethod
    async def analyze_market_conditions(self, symbol: str) -> Dict[str, Any]:
        """
        Analyze current market conditions for a symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Dictionary with market analysis data
        """
        pass