"""
Market service interface for market analysis and information.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime
from core.domain.models import MarketConditions


class IMarketService(ABC):
    """Interface for market analysis services"""
    
    @abstractmethod
    async def get_market_conditions(self, symbol: str) -> MarketConditions:
        """
        Get current market conditions for a symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            MarketConditions object
        """
        pass
    
    @abstractmethod
    async def is_market_open(self, symbol: str) -> bool:
        """
        Check if market is open for trading.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            True if market is open
        """
        pass
    
    @abstractmethod
    async def get_trading_sessions(self, symbol: str) -> Dict[str, Any]:
        """
        Get trading session information.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Dictionary with session times and status
        """
        pass
    
    @abstractmethod
    async def get_spread(self, symbol: str) -> float:
        """
        Get current spread for a symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Current spread in points
        """
        pass
    
    @abstractmethod
    async def get_volatility(
        self,
        symbol: str,
        period: int = 14
    ) -> Dict[str, float]:
        """
        Get volatility metrics for a symbol.
        
        Args:
            symbol: Trading symbol
            period: Period for volatility calculation
            
        Returns:
            Dictionary with volatility metrics
        """
        pass
    
    @abstractmethod
    async def get_correlation_matrix(
        self,
        symbols: List[str],
        period: int = 20
    ) -> Dict[str, Dict[str, float]]:
        """
        Get correlation matrix for multiple symbols.
        
        Args:
            symbols: List of symbols to analyze
            period: Period for correlation calculation
            
        Returns:
            Correlation matrix as nested dictionary
        """
        pass
    
    @abstractmethod
    async def get_economic_calendar(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        impact: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get economic calendar events.
        
        Args:
            start_date: Start date for events
            end_date: End date for events
            impact: Filter by impact level (high, medium, low)
            
        Returns:
            List of economic events
        """
        pass