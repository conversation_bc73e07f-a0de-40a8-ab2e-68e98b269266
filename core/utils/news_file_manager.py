"""
Simple ForexFactory news file manager.
Checks file existence and updates based on hash comparison.
"""

import hashlib
import json
import logging
import requests
from pathlib import Path
from typing import Optional

logger = logging.getLogger(__name__)

class NewsFileManager:
    """Simple news file manager with hash-based updates"""
    
    def __init__(self):
        self.data_dir = Path("data")
        self.news_file = self.data_dir / "forexfactory_week.json"
        self.api_url = "https://nfs.faireconomy.media/ff_calendar_thisweek.json"
        self.timeout = 30
    
    def ensure_news_file(self) -> bool:
        """
        Ensure news file exists and is up to date.
        Downloads if missing, updates if hash differs.
        
        Returns:
            True if file is available and valid
        """
        try:
            # Create data directory if needed
            self.data_dir.mkdir(exist_ok=True)
            
            # If file doesn't exist, download it
            if not self.news_file.exists():
                logger.info("News file not found, downloading...")
                return self._download_news_file()
            
            # File exists, check if it needs updating
            logger.debug("News file exists, checking for updates...")
            return self._check_and_update()
            
        except Exception as e:
            logger.error(f"Error ensuring news file: {e}")
            return False
    
    def _download_news_file(self) -> bool:
        """Download news file from API"""
        try:
            logger.info(f"Downloading news data from {self.api_url}")
            
            response = requests.get(self.api_url, timeout=self.timeout)
            response.raise_for_status()
            
            # Get JSON data
            news_data = response.json()
            
            # Save to file
            with open(self.news_file, 'w', encoding='utf-8') as f:
                json.dump(news_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"News file downloaded successfully: {self.news_file}")
            return True
            
        except requests.RequestException as e:
            logger.error(f"Failed to download news file: {e}")
            return False
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in response: {e}")
            return False
        except Exception as e:
            logger.error(f"Error downloading news file: {e}")
            return False
    
    def _check_and_update(self) -> bool:
        """Check if file needs updating based on hash comparison"""
        try:
            # Get hash of local file
            local_hash = self._get_file_hash(self.news_file)
            if not local_hash:
                logger.warning("Could not get local file hash, re-downloading...")
                return self._download_news_file()
            
            # Get hash of remote file
            remote_hash = self._get_remote_hash()
            if not remote_hash:
                logger.warning("Could not get remote file hash, using local file")
                return True  # Use existing file if can't check remote
            
            # Compare hashes
            if local_hash != remote_hash:
                logger.info("News file is outdated, updating...")
                return self._download_news_file()
            else:
                logger.debug("News file is up to date")
                return True
                
        except Exception as e:
            logger.error(f"Error checking file hash: {e}")
            return True  # Use existing file on error
    
    def _get_file_hash(self, file_path: Path) -> Optional[str]:
        """Get SHA256 hash of local file"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            return hashlib.sha256(content).hexdigest()
        except Exception as e:
            logger.error(f"Error getting file hash: {e}")
            return None
    
    def _get_remote_hash(self) -> Optional[str]:
        """Get SHA256 hash of remote file"""
        try:
            response = requests.get(self.api_url, timeout=self.timeout)
            response.raise_for_status()
            
            content = response.content
            return hashlib.sha256(content).hexdigest()
            
        except Exception as e:
            logger.error(f"Error getting remote hash: {e}")
            return None
    
    def validate_file(self) -> bool:
        """Validate that the news file is properly formatted"""
        try:
            if not self.news_file.exists():
                return False
            
            with open(self.news_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Check if it's a list (expected format)
            if isinstance(data, list):
                logger.debug(f"News file is valid with {len(data)} events")
                return True
            else:
                logger.warning("News file format is invalid (not a list)")
                return False
                
        except json.JSONDecodeError as e:
            logger.error(f"News file contains invalid JSON: {e}")
            return False
        except Exception as e:
            logger.error(f"Error validating news file: {e}")
            return False


def ensure_news_data() -> bool:
    """
    Convenience function to ensure news data is available.
    Call this at the start of your trading loop.
    
    Returns:
        True if news data is available
    """
    manager = NewsFileManager()
    
    # Ensure file exists and is current
    if not manager.ensure_news_file():
        logger.error("Failed to ensure news file")
        return False
    
    # Validate file format
    if not manager.validate_file():
        logger.warning("News file is invalid, attempting to re-download...")
        if not manager._download_news_file():
            logger.error("Failed to download valid news file")
            return False
        
        # Validate again
        if not manager.validate_file():
            logger.error("Downloaded file is still invalid")
            return False
    
    logger.info("News data is ready")
    return True


# Export the main function
__all__ = ['ensure_news_data', 'NewsFileManager']
