# ForexFactory News Data Solution

## Problem
The trading system was failing with the error:
```
News file not found: data\forexfactory_week.json
```

The system expects a JSON file with ForexFactory economic calendar data, but the file was missing or in the wrong format.

## Root Cause
1. **Missing File**: The `data/forexfactory_week.json` file didn't exist
2. **Format Mismatch**: The system expects a **direct JSON array** of events, not an object with metadata
3. **No Auto-Update**: No mechanism to automatically fetch fresh ForexFactory data

## Solution Implemented

### 1. ForexFactory Data Updater (`scripts/update_forexfactory_news.py`)
- **Purpose**: Fetches latest ForexFactory data from the API endpoint
- **Source**: `https://nfs.faireconomy.media/ff_calendar_thisweek.json`
- **Output**: Properly formatted `data/forexfactory_week.json`
- **Features**:
  - Automatic data transformation to expected format
  - Currency mapping (US → USD, EU → EUR, etc.)
  - Impact level normalization (High → high, etc.)
  - Error handling and retry logic
  - Summary reporting

### 2. Fallback Data Creator (`scripts/create_fallback_news.py`)
- **Purpose**: Creates minimal news data when API is unavailable
- **Output**: Basic `data/forexfactory_week.json` with sample events
- **Use Case**: Prevents system crashes when ForexFactory API is down

### 3. News Data Manager (`scripts/manage_news_data.py`)
- **Purpose**: Comprehensive news data management
- **Features**:
  - Status checking (file age, validity, event count)
  - Automatic update strategy (API first, fallback if needed)
  - Data validation
  - Multiple operation modes

### 4. Enhanced News Service Error Handling
- **Updated**: `core/services/news_service.py`
- **Improvement**: Better error messages with solution suggestions
- **User Guidance**: Clear instructions on how to fix missing news data

### 5. Easy-to-Use Batch File (`update_news.bat`)
- **Purpose**: One-click news data update for Windows users
- **Features**: Automatic fallback if API update fails

## Expected JSON Format

The system expects `data/forexfactory_week.json` to contain a **direct array** of events:

```json
[
  {
    "datetime": "2025-06-25T13:30:00+00:00",
    "date": "2025-06-25",
    "time": "13:30",
    "currency": "USD",
    "country": "US",
    "event": "Unemployment Claims",
    "title": "Unemployment Claims",
    "impact": "medium",
    "forecast": "220K",
    "previous": "218K",
    "actual": null,
    "url": "https://www.forexfactory.com/calendar/..."
  }
]
```

**Important**: The file must be a **direct array**, not wrapped in an object with metadata.

## Usage Instructions

### Quick Fix (Recommended)
```bash
# Windows
update_news.bat

# Linux/Mac
python scripts/manage_news_data.py ensure
```

### Manual Operations
```bash
# Check current status
python scripts/manage_news_data.py status

# Force update from API
python scripts/manage_news_data.py update

# Create fallback data
python scripts/manage_news_data.py fallback

# Validate existing data
python scripts/manage_news_data.py validate
```

### Automated Updates
Add to your trading system startup:
```python
# Before starting trading
from pathlib import Path
import subprocess
import sys

def ensure_news_data():
    try:
        result = subprocess.run([
            sys.executable, 
            "scripts/manage_news_data.py", 
            "ensure"
        ], timeout=60)
        return result.returncode == 0
    except:
        return False

# Call before trading starts
if not ensure_news_data():
    logger.warning("Could not update news data, using existing/fallback")
```

## Maintenance

### Daily Updates
The news data should be updated daily. Consider adding to your system:
1. **Startup Check**: Update news data when trading system starts
2. **Scheduled Task**: Daily update via Windows Task Scheduler or cron
3. **Manual Update**: Run `update_news.bat` when needed

### Monitoring
- **File Age**: News data older than 24 hours should be updated
- **Event Count**: Typical week has 50-200 events
- **High Impact Events**: Monitor for important economic releases

### Troubleshooting

#### "News file not found" Error
```bash
# Quick fix
python scripts/create_fallback_news.py

# Or get real data
python scripts/update_forexfactory_news.py
```

#### "News data must be a list" Error
The JSON file is corrupted or in wrong format:
```bash
# Recreate the file
python scripts/update_forexfactory_news.py
```

#### API Update Fails
```bash
# Use fallback data
python scripts/create_fallback_news.py

# Check internet connection and try again later
```

## Files Created/Modified

### New Files
- `scripts/update_forexfactory_news.py` - Main updater
- `scripts/create_fallback_news.py` - Fallback creator  
- `scripts/manage_news_data.py` - Comprehensive manager
- `update_news.bat` - Windows batch file
- `FOREXFACTORY_NEWS_SOLUTION.md` - This documentation

### Modified Files
- `core/services/news_service.py` - Enhanced error handling

## Benefits
1. **Reliability**: Multiple fallback strategies prevent system crashes
2. **Automation**: Automatic data updates with minimal manual intervention
3. **Monitoring**: Clear status reporting and validation
4. **User-Friendly**: Simple batch file for non-technical users
5. **Maintainable**: Modular design for easy updates and debugging

The solution ensures your trading system always has access to ForexFactory news data, preventing the "News file not found" error and maintaining system stability.
