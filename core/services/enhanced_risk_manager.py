"""
Enhanced Risk Management Service
Integrates all risk management components for comprehensive protection
"""

import logging
from typing import Dict, Optional, Tuple, List, Any
from datetime import datetime, timezone
from dataclasses import dataclass
import asyncio

from core.domain.models import TradingSignal, Trade, TradeStatus
from core.domain.exceptions import RiskManagementError, ErrorContext
from core.infrastructure.mt5.client import MT5Client
from core.services.portfolio_risk_service import PortfolioRiskManager
from core.utils.trading_circuit_breaker import get_trading_circuit_breaker
from core.utils.forex_session_validator import get_forex_validator
from config.risk_management_config import get_risk_limits, DrawdownTracker
from config.settings import TradingSettings

logger = logging.getLogger(__name__)


@dataclass
class RiskAssessment:
    """Complete risk assessment for a trade"""
    allowed: bool
    reason: str
    adjusted_position_size: Optional[float] = None
    risk_checks: Dict[str, Dict[str, Any]] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.risk_checks is None:
            self.risk_checks = {}
        if self.warnings is None:
            self.warnings = []


class EnhancedRiskManager:
    """
    Comprehensive risk management service that coordinates:
    - Position sizing with margin requirements
    - Correlation risk tracking
    - Circuit breaker protection
    - Market hours validation
    - Drawdown management
    - Portfolio-level risk limits
    """
    
    def __init__(
        self,
        mt5_client: MT5Client,
        portfolio_risk_manager: PortfolioRiskManager,
        trading_config: TradingSettings,
        risk_profile: str = "moderate"
    ):
        self.mt5_client = mt5_client
        self.portfolio_risk_manager = portfolio_risk_manager
        self.trading_config = trading_config
        self.risk_limits = get_risk_limits(risk_profile)
        
        # Initialize drawdown tracker
        account_info = mt5_client.get_account_info()
        if account_info:
            balance = account_info.get('balance', 10000)
            self.drawdown_tracker = DrawdownTracker(
                starting_balance=balance,
                current_balance=balance,
                daily_starting_balance=balance,
                max_balance=balance
            )
        else:
            raise RiskManagementError("Failed to initialize risk manager: no account info")
        
        # Get singletons
        self.circuit_breaker = get_trading_circuit_breaker()
        self.forex_validator = get_forex_validator()
        
        logger.info(f"Enhanced Risk Manager initialized with {risk_profile} profile")
    
    async def assess_trade_risk(self, signal: TradingSignal) -> RiskAssessment:
        """
        Perform comprehensive risk assessment for a trading signal
        
        Returns:
            RiskAssessment with all checks and adjusted position size
        """
        with ErrorContext("Risk assessment", symbol=signal.symbol) as ctx:
            ctx.add_detail("signal_type", signal.signal.value)
            ctx.add_detail("risk_class", signal.risk_class.value)
            
            assessment = RiskAssessment(allowed=True, reason="")
            
            # 1. Check market hours
            market_check = self._check_market_hours(signal.symbol)
            assessment.risk_checks['market_hours'] = market_check
            if not market_check['passed']:
                assessment.allowed = False
                assessment.reason = market_check['message']
                return assessment
            
            # 2. Check circuit breaker
            cb_check = self._check_circuit_breaker(signal)
            assessment.risk_checks['circuit_breaker'] = cb_check
            if not cb_check['passed']:
                assessment.allowed = False
                assessment.reason = cb_check['message']
                return assessment
            
            # 3. Check drawdown limits
            dd_check = self._check_drawdown_limits(signal.symbol)
            assessment.risk_checks['drawdown_limits'] = dd_check
            if not dd_check['passed']:
                assessment.allowed = False
                assessment.reason = dd_check['message']
                return assessment
            
            # 4. Check portfolio risk
            portfolio_allowed, portfolio_details = await self.portfolio_risk_manager.check_signal_risk(signal)
            assessment.risk_checks['portfolio_risk'] = {
                'passed': portfolio_allowed,
                'details': portfolio_details
            }
            if not portfolio_allowed:
                assessment.allowed = False
                assessment.reason = "Portfolio risk limits exceeded"
                return assessment
            
            # 5. Calculate position size with margin requirements
            position_size = await self._calculate_safe_position_size(signal, portfolio_details)
            assessment.risk_checks['position_sizing'] = {
                'passed': position_size > 0,
                'calculated_size': position_size,
                'message': f"Position size: {position_size:.2f} lots"
            }
            
            if position_size <= 0:
                assessment.allowed = False
                assessment.reason = "Cannot calculate safe position size"
                return assessment
            
            # 6. Check margin requirements
            margin_check = await self._check_margin_requirements(signal, position_size)
            assessment.risk_checks['margin_requirements'] = margin_check
            if not margin_check['passed']:
                assessment.allowed = False
                assessment.reason = margin_check['message']
                return assessment
            
            # 7. Check confidence requirements
            conf_check = self._check_confidence_requirements(signal)
            assessment.risk_checks['confidence'] = conf_check
            if not conf_check['passed']:
                assessment.allowed = False
                assessment.reason = conf_check['message']
                return assessment
            
            # 8. Check risk/reward ratio
            rr_check = self._check_risk_reward_ratio(signal)
            assessment.risk_checks['risk_reward'] = rr_check
            if not rr_check['passed']:
                assessment.allowed = False
                assessment.reason = rr_check['message']
                return assessment
            
            # Generate warnings for borderline conditions
            assessment.warnings = self._generate_risk_warnings(assessment.risk_checks)
            
            # Set final values
            assessment.adjusted_position_size = position_size
            if assessment.allowed:
                assessment.reason = "All risk checks passed"
            
            # Log assessment summary
            logger.info(f"Risk assessment for {signal.symbol}: {assessment.reason}")
            if assessment.warnings:
                logger.warning(f"Risk warnings: {', '.join(assessment.warnings)}")
            
            return assessment
    
    def _check_market_hours(self, symbol: str) -> Dict[str, Any]:
        """Check if market is open for trading"""
        is_open = self.forex_validator.is_market_open()
        session = self.forex_validator.get_current_session()
        
        return {
            'passed': is_open,
            'session': session.value,
            'message': f"Market {'open' if is_open else 'closed'} - {session.value}"
        }
    
    def _check_circuit_breaker(self, signal: TradingSignal) -> Dict[str, Any]:
        """Check circuit breaker status"""
        # Estimate risk amount
        account_info = self.mt5_client.get_account_info()
        balance = account_info.get('balance', 0) if account_info else 0
        risk_amount = balance * (self.trading_config.risk_per_trade_percent / 100)
        
        can_trade, reason = self.circuit_breaker.can_execute("open_position", risk_amount)
        status = self.circuit_breaker.get_status()
        
        return {
            'passed': can_trade,
            'state': status['state'],
            'metrics': status['metrics'],
            'message': reason
        }
    
    def _check_drawdown_limits(self, symbol: str) -> Dict[str, Any]:
        """Check drawdown-based trading limits"""
        can_trade, reason = self.drawdown_tracker.can_trade(symbol, self.risk_limits)
        
        return {
            'passed': can_trade,
            'current_drawdown': self.drawdown_tracker.current_drawdown_percent,
            'daily_drawdown': self.drawdown_tracker.daily_drawdown_percent,
            'consecutive_losses': self.drawdown_tracker.consecutive_losses,
            'message': reason
        }
    
    def _check_confidence_requirements(self, signal: TradingSignal) -> Dict[str, Any]:
        """Check if signal meets confidence requirements"""
        min_confidence = self.risk_limits.min_confidence_to_trade
        signal_confidence = getattr(signal, 'confidence', 0) * 100  # Convert to percentage
        
        passed = signal_confidence >= min_confidence
        
        return {
            'passed': passed,
            'signal_confidence': signal_confidence,
            'required_confidence': min_confidence,
            'message': f"Confidence: {signal_confidence:.0f}% / {min_confidence:.0f}% required"
        }
    
    def _check_risk_reward_ratio(self, signal: TradingSignal) -> Dict[str, Any]:
        """Check if signal meets risk/reward requirements"""
        min_rr = self.risk_limits.min_risk_reward_ratio
        signal_rr = signal.risk_reward or 0
        
        passed = signal_rr >= min_rr
        
        return {
            'passed': passed,
            'signal_rr': signal_rr,
            'required_rr': min_rr,
            'message': f"Risk/Reward: {signal_rr:.1f} / {min_rr:.1f} required"
        }
    
    async def _calculate_safe_position_size(
        self,
        signal: TradingSignal,
        portfolio_details: Dict[str, Any]
    ) -> float:
        """Calculate position size with all safety considerations"""
        # Get account info
        account_info = self.mt5_client.get_account_info()
        if not account_info:
            return 0.0
        
        balance = account_info.get('balance', 0)
        leverage = account_info.get('leverage', 100)
        
        # Get symbol info
        symbol_info = self.mt5_client.get_symbol_info(signal.symbol)
        if not symbol_info:
            logger.error(f"Cannot get symbol info for {signal.symbol}")
            return 0.0
        
        # Calculate stop loss distance in points
        sl_distance = abs(signal.entry - signal.stop_loss)
        point_value = symbol_info.get('point', 0.00001)
        sl_points = sl_distance / point_value
        
        # Get pip value
        contract_size = symbol_info.get('trade_contract_size', 100000)
        pip_value = point_value * contract_size * 10  # 10 points = 1 pip
        
        # Calculate base position size using risk limits
        position_size = self.risk_limits.calculate_position_size(
            account_balance=balance,
            stop_loss_pips=sl_points / 10,  # Convert points to pips
            pip_value=pip_value,
            current_drawdown=self.drawdown_tracker.current_drawdown_percent
        )
        
        # Apply portfolio risk adjustment
        if portfolio_details and 'suggested_position_size' in portfolio_details:
            portfolio_adjustment = portfolio_details['suggested_position_size']
            if portfolio_adjustment < 1.0:
                position_size *= portfolio_adjustment
                logger.info(f"Position size adjusted by portfolio risk: {portfolio_adjustment:.2f}")
        
        # Apply minimum and maximum lot sizes
        min_lot = symbol_info.get('volume_min', 0.01)
        max_lot = symbol_info.get('volume_max', 100.0)
        lot_step = symbol_info.get('volume_step', 0.01)
        
        # Round to valid lot size
        position_size = max(min_lot, round(position_size / lot_step) * lot_step)
        position_size = min(position_size, max_lot)
        
        # Final safety check - ensure position doesn't exceed max position size limit
        max_position_value = balance * (self.risk_limits.max_position_size_percent / 100)
        max_lots_by_value = max_position_value / (signal.entry * contract_size)
        position_size = min(position_size, max_lots_by_value)
        
        return position_size
    
    async def _check_margin_requirements(
        self,
        signal: TradingSignal,
        position_size: float
    ) -> Dict[str, Any]:
        """Check if account has sufficient margin for position"""
        account_info = self.mt5_client.get_account_info()
        if not account_info:
            return {
                'passed': False,
                'message': "Cannot get account information"
            }
        
        free_margin = account_info.get('margin_free', 0)
        leverage = account_info.get('leverage', 100)
        
        # Get symbol info
        symbol_info = self.mt5_client.get_symbol_info(signal.symbol)
        if not symbol_info:
            return {
                'passed': False,
                'message': f"Cannot get symbol info for {signal.symbol}"
            }
        
        # Calculate required margin
        contract_size = symbol_info.get('trade_contract_size', 100000)
        margin_initial = symbol_info.get('margin_initial', 0)
        
        # Calculate position value and required margin
        position_value = position_size * signal.entry * contract_size
        
        if margin_initial > 0:
            # Use symbol-specific margin requirement
            required_margin = position_size * margin_initial
        else:
            # Calculate based on leverage
            required_margin = position_value / leverage
        
        # Add safety buffer (20%)
        required_margin *= 1.2
        
        # Check if sufficient margin
        margin_ratio = free_margin / required_margin if required_margin > 0 else 0
        passed = margin_ratio >= 1.0
        
        # Check margin level after position
        current_margin = account_info.get('margin', 0)
        equity = account_info.get('equity', 0)
        new_margin = current_margin + required_margin
        new_margin_level = (equity / new_margin * 100) if new_margin > 0 else 0
        
        # Ensure margin level stays above 200%
        if new_margin_level < 200:
            passed = False
        
        return {
            'passed': passed,
            'free_margin': free_margin,
            'required_margin': required_margin,
            'margin_ratio': margin_ratio,
            'new_margin_level': new_margin_level,
            'message': f"Margin: ${free_margin:.2f} free / ${required_margin:.2f} required"
        }
    
    def _generate_risk_warnings(self, risk_checks: Dict[str, Dict[str, Any]]) -> List[str]:
        """Generate warnings for borderline risk conditions"""
        warnings = []
        
        # Check drawdown warnings
        dd_check = risk_checks.get('drawdown_limits', {})
        if dd_check.get('current_drawdown', 0) > 7:
            warnings.append(f"High drawdown: {dd_check['current_drawdown']:.1f}%")
        
        # Check margin warnings
        margin_check = risk_checks.get('margin_requirements', {})
        if margin_check.get('margin_ratio', 0) < 2:
            warnings.append(f"Low margin ratio: {margin_check['margin_ratio']:.1f}")
        
        # Check circuit breaker warnings
        cb_check = risk_checks.get('circuit_breaker', {})
        cb_metrics = cb_check.get('metrics', {})
        if cb_metrics.get('error_rate', 0) > 0.1:
            warnings.append(f"High error rate: {cb_metrics['error_rate']:.1%}")
        
        # Check portfolio risk warnings
        portfolio_check = risk_checks.get('portfolio_risk', {})
        portfolio_details = portfolio_check.get('details', {})
        if portfolio_details.get('risk_score', 0) > 70:
            warnings.append(f"High portfolio risk score: {portfolio_details['risk_score']}")
        
        return warnings
    
    def record_trade_result(self, symbol: str, profit: float, new_balance: float):
        """Record trade result in all risk tracking systems"""
        # Update drawdown tracker
        self.drawdown_tracker.record_trade_result(symbol, profit)
        self.drawdown_tracker.update_balance(new_balance)
        
        # Update circuit breaker
        self.circuit_breaker.record_trade_result(profit, new_balance)
        
        # Log risk metrics after trade
        logger.info(f"Risk metrics after {symbol} trade (P&L: ${profit:.2f}):")
        logger.info(f"  - Drawdown: {self.drawdown_tracker.current_drawdown_percent:.1f}%")
        logger.info(f"  - Daily DD: {self.drawdown_tracker.daily_drawdown_percent:.1f}%")
        logger.info(f"  - Consecutive losses: {self.drawdown_tracker.consecutive_losses}")
        
        cb_status = self.circuit_breaker.get_status()
        logger.info(f"  - Circuit breaker: {cb_status['state']}")
    
    def reset_daily_stats(self):
        """Reset daily statistics"""
        self.drawdown_tracker.reset_daily_stats()
        logger.info("Daily risk statistics reset")
    
    async def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk status summary"""
        # Get portfolio summary
        portfolio_summary = await self.portfolio_risk_manager.get_portfolio_summary()
        
        # Get circuit breaker status
        cb_status = self.circuit_breaker.get_status()
        
        # Get market status
        market_open = self.forex_validator.is_market_open()
        session = self.forex_validator.get_current_session()
        
        return {
            'drawdown': {
                'current': f"{self.drawdown_tracker.current_drawdown_percent:.1f}%",
                'daily': f"{self.drawdown_tracker.daily_drawdown_percent:.1f}%",
                'max_allowed': f"{self.risk_limits.max_total_drawdown_percent:.1f}%"
            },
            'trading_stats': {
                'consecutive_losses': self.drawdown_tracker.consecutive_losses,
                'daily_trades': self.drawdown_tracker.daily_trades,
                'can_trade': self.drawdown_tracker.can_trade("ANY", self.risk_limits)[0]
            },
            'circuit_breaker': {
                'state': cb_status['state'],
                'can_trade': cb_status['can_trade'],
                'metrics': cb_status['metrics']
            },
            'portfolio': portfolio_summary,
            'market': {
                'is_open': market_open,
                'session': session.value
            },
            'risk_limits': {
                'profile': self.risk_limits.__class__.__name__,
                'max_risk_per_trade': f"{self.risk_limits.max_risk_per_trade_percent}%",
                'min_confidence': f"{self.risk_limits.min_confidence_to_trade}%",
                'min_rr_ratio': self.risk_limits.min_risk_reward_ratio
            }
        }


# Export
__all__ = ['EnhancedRiskManager', 'RiskAssessment']