#!/usr/bin/env python3
"""
Run backtest with strict risk management limits
Ensures max 5% per trade drawdown and 10% total drawdown
"""

import sys
import subprocess
from pathlib import Path
import argparse
from datetime import datetime, timedelta

# Add project root to Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from config.risk_management_config import get_risk_limits, RISK_PROFILES


def run_safe_backtest(args):
    """Run backtest with strict risk limits"""
    
    # Get risk limits
    risk_limits = get_risk_limits(args.risk_profile)
    
    print("="*80)
    print("SAFE BACKTESTING WITH STRICT RISK LIMITS")
    print("="*80)
    print(f"\nRisk Profile: {args.risk_profile.upper()}")
    print(f"- Max Risk per Trade: {risk_limits.max_risk_per_trade_percent}%")
    print(f"- Max Loss per Trade: {risk_limits.max_loss_per_trade_percent}%")
    print(f"- Max Daily Drawdown: {risk_limits.max_daily_drawdown_percent}%")
    print(f"- Max Total Drawdown: {risk_limits.max_total_drawdown_percent}%")
    print(f"- Min Confidence: {risk_limits.min_confidence_to_trade}%")
    print(f"- Min R:R Ratio: {risk_limits.min_risk_reward_ratio}:1")
    print("\n" + "="*80)
    
    print("\nNOTE: Risk limits are enforced through .env settings:")
    print("- COUNCIL_CONFIDENCE_THRESHOLD=85.0")
    print("- RISK_PER_TRADE=1.0")
    print("- TRADING_MAX_DAILY_DRAWDOWN_PERCENT=5.0")
    print("- TRADING_MAX_TOTAL_DRAWDOWN_PERCENT=10.0")
    print("\nMake sure these are set in your .env file!\n")
    
    # Build command
    cmd = [
        sys.executable,
        "run_backtest.py",
        "--mode", args.mode
    ]
    
    # Calculate days between start and end
    from datetime import datetime
    start_date = datetime.strptime(args.start, "%Y-%m-%d")
    end_date = datetime.strptime(args.end, "%Y-%m-%d")
    days = (end_date - start_date).days
    if days > 0:
        cmd.extend(["--days", str(days)])
    
    if args.symbols:
        cmd.extend(["--symbols"] + args.symbols)
    
    print(f"\nRunning: {' '.join(cmd)}\n")
    
    # Run backtest
    result = subprocess.run(cmd, capture_output=False)
    
    if result.returncode != 0:
        print(f"\nBacktest failed with return code: {result.returncode}")
        return 1
    
    print("\n" + "="*80)
    print("BACKTEST COMPLETED WITH RISK CONTROLS")
    print("="*80)
    
    return 0


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Run backtest with strict risk management",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Risk Profiles:
  conservative  - 0.5% risk, 3% daily DD, 10% total DD, 90% confidence
  moderate      - 1.0% risk, 5% daily DD, 10% total DD, 85% confidence  
  aggressive    - 1.5% risk, 7% daily DD, 15% total DD, 80% confidence
  prop_firm     - 1.0% risk, 5% daily DD, 10% total DD, 85% confidence (FTMO/similar)

Examples:
  # Conservative 30-day backtest
  python run_safe_backtest.py --risk-profile conservative
  
  # Prop firm rules with specific symbols
  python run_safe_backtest.py --risk-profile prop_firm --symbols EURUSD GBPUSD
  
  # ML-only mode with moderate risk
  python run_safe_backtest.py --risk-profile moderate --mode ml_only
        """
    )
    
    # Date arguments
    default_end = datetime.now().date()
    default_start = default_end - timedelta(days=30)
    
    parser.add_argument("--start", default=str(default_start), 
                       help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end", default=str(default_end),
                       help="End date (YYYY-MM-DD)")
    
    # Risk profile
    parser.add_argument("--risk-profile", choices=list(RISK_PROFILES.keys()),
                       default="prop_firm", help="Risk management profile")
    
    # Trading mode
    parser.add_argument("--mode", choices=["full_council", "simplified", "ml_only", "auto", "legacy"],
                       default="full_council", help="Trading mode")
    
    # Model preset
    parser.add_argument("--preset", choices=["ultra_low_cost", "balanced", "high_accuracy"],
                       default="balanced", help="Model cost/accuracy preset")
    
    # Symbols
    parser.add_argument("--symbols", nargs="+", 
                       help="Symbols to trade (default: from settings)")
    
    args = parser.parse_args()
    
    # Run backtest
    return run_safe_backtest(args)


if __name__ == "__main__":
    sys.exit(main())