"""
Forex Session Validator
Ensures trading only occurs during valid forex market hours
"""

import logging
from datetime import datetime, timezone, time, timedelta
from typing import Optional, Tuple, Dict, Set
from enum import Enum
import holidays

logger = logging.getLogger(__name__)


class ForexSession(Enum):
    """Major forex trading sessions"""
    SYDNEY = "Sydney"
    TOKYO = "Tokyo"
    LONDON = "London"
    NEW_YORK = "New York"
    CLOSED = "Closed"


class MarketCondition(Enum):
    """Market trading conditions"""
    NORMAL = "normal"
    LOW_LIQUIDITY = "low_liquidity"
    HIGH_VOLATILITY = "high_volatility"
    CLOSED = "closed"
    HOLIDAY = "holiday"


class ForexSessionValidator:
    """
    Validates forex market hours and trading conditions.
    
    Forex market is open 24/5:
    - Opens: Sunday 22:00 UTC (Sydney open)
    - Closes: Friday 22:00 UTC (New York close)
    """
    
    # Session times in UTC
    SESSION_TIMES = {
        ForexSession.SYDNEY: (time(21, 0), time(6, 0)),    # 21:00 - 06:00 UTC
        ForexSession.TOKYO: (time(0, 0), time(9, 0)),      # 00:00 - 09:00 UTC
        ForexSession.LONDON: (time(8, 0), time(17, 0)),    # 08:00 - 17:00 UTC
        ForexSession.NEW_YORK: (time(13, 0), time(22, 0))  # 13:00 - 22:00 UTC
    }
    
    # Overlap times (high liquidity periods)
    OVERLAP_TIMES = {
        "LONDON_NY": (time(13, 0), time(17, 0)),  # Best liquidity
        "TOKYO_LONDON": (time(8, 0), time(9, 0)),
        "SYDNEY_TOKYO": (time(0, 0), time(6, 0))
    }
    
    # Low liquidity periods (avoid trading)
    LOW_LIQUIDITY_TIMES = [
        (time(22, 0), time(23, 59)),  # Late Friday
        (time(0, 0), time(1, 0)),     # Early Monday
        (time(21, 0), time(22, 0))    # Pre-Sydney
    ]
    
    # Currency-specific best sessions
    CURRENCY_SESSIONS = {
        "EUR": [ForexSession.LONDON, ForexSession.NEW_YORK],
        "GBP": [ForexSession.LONDON, ForexSession.NEW_YORK],
        "USD": [ForexSession.NEW_YORK, ForexSession.LONDON],
        "JPY": [ForexSession.TOKYO, ForexSession.LONDON],
        "AUD": [ForexSession.SYDNEY, ForexSession.TOKYO],
        "NZD": [ForexSession.SYDNEY, ForexSession.TOKYO],
        "CAD": [ForexSession.NEW_YORK, ForexSession.LONDON],
        "CHF": [ForexSession.LONDON, ForexSession.NEW_YORK]
    }
    
    def __init__(self):
        """Initialize the forex session validator"""
        # Major financial centers for holiday checking
        self.holiday_calendars = {
            'US': holidays.US(),
            'UK': holidays.UK(),
            'JP': holidays.Japan(),
            'EU': holidays.Germany(),  # Using Germany as EU proxy
            'AU': holidays.Australia()
        }
        
        # Cache for holiday checks
        self._holiday_cache: Dict[str, Set[datetime.date]] = {}
        self._cache_year: Optional[int] = None
    
    def is_market_open(self, timestamp: Optional[datetime] = None) -> bool:
        """
        Check if forex market is open.
        
        Args:
            timestamp: Time to check (default: current UTC time)
            
        Returns:
            bool: True if market is open
        """
        if timestamp is None:
            timestamp = datetime.now(timezone.utc)
        else:
            # Ensure UTC timezone
            if timestamp.tzinfo is None:
                timestamp = timestamp.replace(tzinfo=timezone.utc)
            else:
                timestamp = timestamp.astimezone(timezone.utc)
        
        weekday = timestamp.weekday()
        hour = timestamp.hour
        minute = timestamp.minute
        
        # Market closed on Saturday
        if weekday == 5:  # Saturday
            return False
        
        # Market closed Friday after 22:00 UTC
        if weekday == 4:  # Friday
            if hour >= 22:
                return False
        
        # Market closed Sunday before 22:00 UTC
        if weekday == 6:  # Sunday
            if hour < 22:
                return False
        
        # Check for major holidays
        if self._is_major_holiday(timestamp):
            return False
        
        return True
    
    def get_current_session(self, timestamp: Optional[datetime] = None) -> ForexSession:
        """
        Get the current active forex session.
        
        Args:
            timestamp: Time to check
            
        Returns:
            ForexSession: Current active session
        """
        if not self.is_market_open(timestamp):
            return ForexSession.CLOSED
        
        if timestamp is None:
            timestamp = datetime.now(timezone.utc)
        
        current_time = timestamp.time()
        
        # Check each session (handling overnight sessions)
        for session, (start, end) in self.SESSION_TIMES.items():
            if start <= end:
                # Normal session (doesn't cross midnight)
                if start <= current_time <= end:
                    return session
            else:
                # Overnight session (crosses midnight)
                if current_time >= start or current_time <= end:
                    return session
        
        # Shouldn't reach here if market is open
        return ForexSession.CLOSED
    
    def get_market_condition(
        self, 
        symbol: str, 
        timestamp: Optional[datetime] = None
    ) -> MarketCondition:
        """
        Get current market condition for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., "EURUSD")
            timestamp: Time to check
            
        Returns:
            MarketCondition: Current market condition
        """
        if not self.is_market_open(timestamp):
            return MarketCondition.CLOSED
        
        if timestamp is None:
            timestamp = datetime.now(timezone.utc)
        
        # Check for holidays
        if self._is_trading_holiday(symbol, timestamp):
            return MarketCondition.HOLIDAY
        
        # Check for low liquidity
        if self._is_low_liquidity_period(timestamp):
            return MarketCondition.LOW_LIQUIDITY
        
        # Check for high volatility periods (news times)
        if self._is_high_volatility_period(timestamp):
            return MarketCondition.HIGH_VOLATILITY
        
        return MarketCondition.NORMAL
    
    def is_good_trading_time(
        self, 
        symbol: str, 
        timestamp: Optional[datetime] = None
    ) -> Tuple[bool, str]:
        """
        Check if it's a good time to trade a specific symbol.
        
        Args:
            symbol: Trading symbol
            timestamp: Time to check
            
        Returns:
            (is_good, reason)
        """
        if timestamp is None:
            timestamp = datetime.now(timezone.utc)
        
        # Check if market is open
        if not self.is_market_open(timestamp):
            return False, "Market closed"
        
        # Get market condition
        condition = self.get_market_condition(symbol, timestamp)
        
        if condition == MarketCondition.CLOSED:
            return False, "Market closed"
        elif condition == MarketCondition.HOLIDAY:
            return False, "Market holiday"
        elif condition == MarketCondition.LOW_LIQUIDITY:
            return False, "Low liquidity period"
        
        # Check if it's optimal session for the currency pair
        session = self.get_current_session(timestamp)
        base_currency = symbol[:3]
        quote_currency = symbol[3:6] if len(symbol) >= 6 else "USD"
        
        optimal_sessions = set()
        optimal_sessions.update(self.CURRENCY_SESSIONS.get(base_currency, []))
        optimal_sessions.update(self.CURRENCY_SESSIONS.get(quote_currency, []))
        
        if session not in optimal_sessions:
            return True, f"Sub-optimal session ({session.value}) for {symbol}"
        
        return True, f"Good trading time - {session.value} session"
    
    def get_next_market_open(self, timestamp: Optional[datetime] = None) -> datetime:
        """
        Get the next market open time.
        
        Args:
            timestamp: Reference time
            
        Returns:
            datetime: Next market open time in UTC
        """
        if timestamp is None:
            timestamp = datetime.now(timezone.utc)
        
        if self.is_market_open(timestamp):
            return timestamp
        
        weekday = timestamp.weekday()
        
        # If Saturday or Sunday before 22:00
        if weekday == 5 or (weekday == 6 and timestamp.hour < 22):
            # Next open is Sunday 22:00
            days_until_sunday = 6 - weekday
            if weekday == 6:  # Already Sunday
                days_until_sunday = 0
            
            next_open = timestamp.replace(
                hour=22, minute=0, second=0, microsecond=0
            ) + timedelta(days=days_until_sunday)
            
        # If Friday after 22:00
        elif weekday == 4 and timestamp.hour >= 22:
            # Next open is Sunday 22:00 (2 days later)
            next_open = timestamp.replace(
                hour=22, minute=0, second=0, microsecond=0
            ) + timedelta(days=2)
        
        else:
            # Shouldn't happen if market is closed
            next_open = timestamp
        
        return next_open
    
    def _is_major_holiday(self, timestamp: datetime) -> bool:
        """Check if it's a major holiday affecting forex markets"""
        date = timestamp.date()
        
        # Update cache if needed
        if self._cache_year != date.year:
            self._update_holiday_cache(date.year)
        
        # Check major holidays
        major_holidays = [
            "New Year's Day",
            "Christmas Day",
            "Good Friday"
        ]
        
        # Check each country's holidays
        for country, holidays_cal in self.holiday_calendars.items():
            if date in holidays_cal:
                holiday_name = holidays_cal.get(date)
                if any(major in holiday_name for major in major_holidays):
                    logger.info(f"Major holiday detected: {holiday_name} in {country}")
                    return True
        
        return False
    
    def _is_trading_holiday(self, symbol: str, timestamp: datetime) -> bool:
        """Check if it's a holiday affecting specific currency pair"""
        date = timestamp.date()
        base_currency = symbol[:3]
        quote_currency = symbol[3:6] if len(symbol) >= 6 else "USD"
        
        # Map currencies to countries
        currency_countries = {
            "EUR": "EU",
            "GBP": "UK",
            "USD": "US",
            "JPY": "JP",
            "AUD": "AU",
            "NZD": "AU",  # Using AU as proxy
            "CAD": "US",  # Using US as proxy
            "CHF": "EU"   # Using EU as proxy
        }
        
        # Check holidays for both currencies
        for currency in [base_currency, quote_currency]:
            country = currency_countries.get(currency)
            if country and country in self.holiday_calendars:
                if date in self.holiday_calendars[country]:
                    return True
        
        return False
    
    def _is_low_liquidity_period(self, timestamp: datetime) -> bool:
        """Check if it's a low liquidity period"""
        current_time = timestamp.time()
        weekday = timestamp.weekday()
        
        # Friday evening low liquidity
        if weekday == 4 and timestamp.hour >= 20:
            return True
        
        # Sunday evening/Monday morning low liquidity
        if weekday == 6 and timestamp.hour >= 22:
            return True
        if weekday == 0 and timestamp.hour < 2:
            return True
        
        # Check defined low liquidity times
        for start, end in self.LOW_LIQUIDITY_TIMES:
            if start <= current_time <= end:
                return True
        
        return False
    
    def _is_high_volatility_period(self, timestamp: datetime) -> bool:
        """Check if it's a high volatility period (major news releases)"""
        # Common news release times (simplified)
        hour = timestamp.hour
        minute = timestamp.minute
        
        # Major economic releases often happen at:
        # - 08:30 UTC (US data)
        # - 13:30 UTC (US data)
        # - 10:00 UTC (EU data)
        # - 14:00 UTC (US Fed)
        
        news_times = [
            (8, 30), (10, 0), (13, 30), (14, 0), (18, 0)
        ]
        
        for news_hour, news_minute in news_times:
            # Within 30 minutes of news time
            if hour == news_hour and abs(minute - news_minute) <= 30:
                return True
            # Handle hour boundary
            if hour == news_hour - 1 and minute >= 30 and news_minute < 30:
                return True
            if hour == news_hour + 1 and minute < 30 and news_minute >= 30:
                return True
        
        return False
    
    def _update_holiday_cache(self, year: int):
        """Update holiday cache for the year"""
        self._cache_year = year
        self._holiday_cache.clear()
        
        # Pre-calculate holidays for the year
        for country, holidays_cal in self.holiday_calendars.items():
            year_holidays = set()
            for date in holidays_cal.keys():
                if date.year == year:
                    year_holidays.add(date)
            self._holiday_cache[country] = year_holidays
    
    def get_session_schedule(self, date: Optional[datetime.date] = None) -> Dict[str, Dict]:
        """Get full session schedule for a date"""
        if date is None:
            date = datetime.now(timezone.utc).date()
        
        schedule = {}
        
        for session, (start, end) in self.SESSION_TIMES.items():
            # Create datetime objects for the session
            start_dt = datetime.combine(date, start, tzinfo=timezone.utc)
            
            # Handle overnight sessions
            if end < start:
                end_dt = datetime.combine(date + timedelta(days=1), end, tzinfo=timezone.utc)
            else:
                end_dt = datetime.combine(date, end, tzinfo=timezone.utc)
            
            schedule[session.value] = {
                'start': start_dt.isoformat(),
                'end': end_dt.isoformat(),
                'duration_hours': (end_dt - start_dt).total_seconds() / 3600
            }
        
        return schedule


# Global instance
_forex_validator = ForexSessionValidator()


def get_forex_validator() -> ForexSessionValidator:
    """Get the global forex session validator"""
    return _forex_validator


# Convenience functions
def is_forex_market_open(timestamp: Optional[datetime] = None) -> bool:
    """Check if forex market is open"""
    return _forex_validator.is_market_open(timestamp)


def can_trade_symbol(symbol: str, timestamp: Optional[datetime] = None) -> Tuple[bool, str]:
    """Check if a symbol can be traded now"""
    return _forex_validator.is_good_trading_time(symbol, timestamp)