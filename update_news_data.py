"""
Update ForexFactory news data
"""
import json
import os
from datetime import datetime
from core.services.news_updater import ForexFactoryUpdater

print("Updating ForexFactory news data...")
print(f"Current time: {datetime.now()}")

# Create news updater
updater = ForexFactoryUpdater()

# Update news data
try:
    updater.update_news_data()
    print("\n✓ News data updated successfully!")
    
    # Check the updated file
    news_file = "data/forexfactory_week.json"
    if os.path.exists(news_file):
        with open(news_file, 'r') as f:
            data = json.load(f)
        
        print(f"\nNews data summary:")
        print(f"- Total events: {len(data.get('events', []))}")
        print(f"- Last updated: {data.get('last_updated', 'Unknown')}")
        
        # Show upcoming high impact events
        high_impact = [e for e in data.get('events', []) if e.get('impact') == 'High']
        print(f"- High impact events: {len(high_impact)}")
        
        if high_impact:
            print("\nUpcoming high impact events:")
            for event in high_impact[:5]:
                print(f"  - {event.get('datetime', 'Unknown time')}: {event.get('currency', 'XX')} - {event.get('event', 'Unknown event')}")
    
except Exception as e:
    print(f"\n✗ Error updating news data: {e}")
    print("\nYou may need to manually update the news data.")
    print("The news updater might require additional setup or dependencies.")

print("\nDone.")