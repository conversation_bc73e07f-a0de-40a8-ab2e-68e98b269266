# GPT Trader V1 - Complete System Commands Guide

## Table of Contents
1. [Quick Start](#quick-start)
2. [Environment Setup](#environment-setup)
3. [Main Commands](#main-commands)
4. [Configuration](#configuration)
5. [Dashboards and Monitoring](#dashboards-and-monitoring)
6. [Testing and Validation](#testing-and-validation)
7. [ML Model Management](#ml-model-management)
8. [Troubleshooting](#troubleshooting)
9. [Safety Checklist](#safety-checklist)

## Quick Start

### 1. First Time Setup
```bash
# Clone repository and navigate to it
cd /mnt/d/gpt_trader_v1

# Create virtual environment
python -m venv venv

# Activate virtual environment (Windows)
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy and configure environment file
cp .env.example .env
# Edit .env with your API keys and settings
```

### 2. Essential Configuration (.env file)
```bash
# Required API Keys
OPENAI_API_KEY=your-openai-api-key
MT5_FILES_DIR=C:/Users/<USER>/AppData/Roaming/MetaQuotes/Terminal/YOUR_TERMINAL_ID/MQL5/Files

# Optional but Recommended
TELEGRAM_TOKEN=your-telegram-bot-token
TELEGRAM_CHAT_ID=your-chat-id
MARKETAUX_API_KEY=your-marketaux-key

# ML Configuration
ML_ENABLED=false  # Set to true to enable ML predictions
ML_CONFIDENCE_THRESHOLD=0.7

# Risk Profile
RISK_PROFILE=moderate  # Options: conservative, moderate, aggressive, prop_firm

# Trading Configuration
TRADING_CACHE_ENABLED=true  # Enable intelligent caching
CACHE_SIMILARITY_THRESHOLD=85  # Cache similarity percentage
```

## Main Commands

### 1. Live Trading System
```bash
# Windows - Using launcher
launcher.bat

# Or directly with Python
python trading_loop.py

# Windows - Quick start trading
start_trading.bat

# Linux/WSL
python trading_loop.py
```

**What it does:**
- Connects to MT5 and monitors configured symbols
- Uses Trading Council (7 AI agents) to analyze markets
- Executes trades based on consensus decisions
- Manages open positions automatically
- Runs continuously during market hours

### 2. Backtesting
```bash
# Standard backtest (auto mode)
python run_backtest.py

# Full council mode (uses all 7 AI agents)
python run_backtest.py --mode full_council

# Simplified mode (cheaper, faster)
python run_backtest.py --mode simplified

# ML-only mode (no GPT costs)
python run_backtest.py --mode ml_only

# Legacy mode (old backtest engine)
python run_backtest.py --mode legacy

# Compare all modes
python run_backtest.py --compare-modes

# With specific symbols
python run_backtest.py --symbols EURUSD GBPUSD XAUUSD

# Custom time period (days)
python run_backtest.py --days 90

# Position trading mode (D1/W1 timeframes)
python run_backtest.py --position-trading

# Walk-forward analysis
python run_backtest.py --walk-forward 5

# Train LLM simulator before backtesting
python run_backtest.py --train-simulator

# Parameter optimization
python run_backtest.py --optimize

# Safe backtest (read-only, no ML training)
python run_safe_backtest.py

# Offline backtest (no live data required)
python run_offline_backtest.py
```

**Backtest Modes:**
- `full_council`: Uses all 7 AI agents for most accurate results (highest cost)
- `simplified`: Uses simplified agents with cheaper models (balanced cost/accuracy)
- `ml_only`: Uses only ML models, no GPT costs (lowest cost, requires trained models)
- `auto`: Automatically selects best mode based on available resources
- `legacy`: Uses old backtest engine for comparison

**Backtest Options:**
- `--mode`: Backtesting mode (full_council, simplified, ml_only, auto, legacy)
- `--symbols`: Space-separated list of symbols
- `--days`: Number of days to backtest (default: 180)
- `--position-trading`: Use position trading timeframes (D1/W1)
- `--walk-forward`: Number of walk-forward periods (0 to disable)
- `--train-simulator`: Train LLM simulator before backtesting
- `--compare-modes`: Compare performance across all modes
- `--optimize`: Run parameter optimization

### 3. Graceful Shutdown
```bash
# Windows - Create stop file
echo > STOP_TRADING

# Or use the graceful stop script
python graceful_stop.py
```

**What it does:**
- Creates a stop file that trading loop checks
- Allows current operations to complete
- Closes system cleanly without interrupting trades

## Configuration

### 1. Trading Symbols Configuration
Edit `config/symbols.py`:
```python
# Conservative symbols (lower risk)
CONSERVATIVE_SYMBOLS = ["EURUSD", "GBPUSD", "USDJPY"]

# Moderate symbols (medium risk)
MODERATE_SYMBOLS = ["AUDUSD", "USDCAD", "NZDUSD"]

# Aggressive symbols (higher risk)
AGGRESSIVE_SYMBOLS = ["GBPJPY", "XAUUSD", "EURJPY"]
```

### 2. Risk Management Configuration
Edit `config/risk_management_config.py` or set via .env:
```bash
# Risk profiles available:
# - conservative: 0.5% risk, 3% daily DD, 90% min confidence
# - moderate: 1% risk, 5% daily DD, 85% min confidence
# - aggressive: 1.5% risk, 7% daily DD, 80% min confidence
# - prop_firm: Strict limits for prop trading
```

### 3. Trading Hours
Edit `config/settings.py`:
```python
# Trading hours (UTC)
start_hour = 8   # 8 AM UTC
end_hour = 20    # 8 PM UTC
```

## Dashboards and Monitoring

### 1. Unified Trading Dashboard (Recommended)
```bash
# Start comprehensive dashboard
start_unified_dashboard.bat

# Or directly
python scripts/run_unified_dashboard.py

# Access at: http://localhost:8501
```

**Features:**
- Real-time trading overview
- Position monitoring
- Risk metrics display
- Performance analytics
- Trade history
- System health status

### 2. Simple Trading Dashboard
```bash
# Lightweight version
start_unified_simple.bat

# Or
python scripts/unified_dashboard_simple.py
```

### 3. GPT Flow Dashboard
```bash
# Monitor AI agent decisions
python scripts/gpt_flow_dashboard.py

# Or with main dashboard
start_dashboard_with_gpt_flow.bat
```

**Features:**
- Real-time agent deliberations
- Signal generation flow
- Consensus tracking
- Decision reasoning

### 4. ML Performance Monitor
```bash
# Monitor ML model performance
start_ml_monitor.bat

# Or
python scripts/run_ml_monitor.py
```

**Features:**
- Model accuracy tracking
- Prediction vs actual results
- Feature importance
- Performance metrics

### 5. Comprehensive Dashboard
```bash
# All features combined
python scripts/comprehensive_trading_dashboard.py
```

**Includes:**
- Trading overview
- Risk analysis
- ML predictions
- Market analysis
- Performance reports

## Testing and Validation

### 1. MT5 Connection Test
```bash
# Basic connection test
python test_mt5_simple.py

# Detailed MT5 test
python tests/manual/test_mt5_connection.py

# Test with authentication
python test_mt5_auth.py

# Debug MT5 issues
python debug_mt5.py
```

### 2. Symbol Discovery and Management
```bash
# Find available symbols interactively
python find_oil_symbol.py

# Enable recommended symbols
python enable_recommended_symbols.py

# Enable all MT5 symbols
python enable_mt5_symbols.py

# MT5 symbol manager
python mt5_symbol_manager.py
```

### 3. Check System State
```bash
# Check MT5 state
check_mt5_state.bat

# Check current configuration
python verify_settings_and_train.py
```

### 4. Test ML Models
```bash
# Simple ML test
python test_simple_ml.py

# Test ML predictions
python scripts/tests_archive/test_ml_predictions_simple.py
```

## ML Model Management

### 1. Train ML Models
```bash
# Production training (with safety checks)
python scripts/train_ml_production.py

# Secure training (encrypted models)
python scripts/train_ml_secure.py

# GPT-enhanced training
python scripts/train_gpt_enhanced_ml.py

# Specific symbols
python scripts/train_ml_production.py --symbols EURUSD,GBPUSD

# Custom date range
python scripts/train_ml_production.py --start-date 2023-01-01 --end-date 2024-12-31
```

### 2. ML Continuous Improvement
```bash
# Run continuous improvement cycle
python scripts/ml_continuous_improvement.py

# ML continuous learning
python scripts/ml_continuous_learning.py

# Analyze ML models
python analyze_ml_models.py
```

### 3. Migrate Models
```bash
# Migrate to secure format
python scripts/migrate_models_to_secure.py
```

## Monitoring Scripts

### 1. Performance Analytics
```bash
# Run performance analysis
python scripts/performance_analytics.py

# Automation tasks
python scripts/automation/performance_analytics.py
```

### 2. MarketAux Monitor
```bash
# Monitor news API usage
python scripts/marketaux_monitor.py
```

### 3. Database Management
```bash
# Backup database
python scripts/automation/database_backup.py

# Database info
python scripts/db_info.py
```

### 4. Health Monitoring
```bash
# System health check
python scripts/automation/health_check.py
```

## Utility Commands

### 1. News Updates
```bash
# Update news data
python scripts/automation/news_updater.py

# Test news service
python tests/manual/test_forex_news.py
```

### 2. Council Visualization
```bash
# Visualize trading council
python scripts/visualize_council.py
```

### 3. User Management (for dashboards)
```bash
# Manage dashboard users
python scripts/manage_dashboard_users.py

# Secure all dashboards
python scripts/secure_all_dashboards.py
```

### 4. Dependency Management
```bash
# Pin dependencies
python scripts/pin_dependencies.py

# Check security
python scripts/check_dependencies_security.py
```

## Windows Task Scheduler Setup

### 1. Create Scheduled Tasks
```bash
# Windows batch
scripts/create_scheduled_tasks.bat

# PowerShell
scripts/setup_windows_tasks.ps1
```

### 2. Start All Services
```bash
# Start everything
scripts/start_all_services.bat

# Production mode
scripts/start_production.bat
```

## Troubleshooting

### 1. Common Issues

**MT5 Connection Failed:**
```bash
# Check MT5 terminal is running
# Verify MT5_FILES_DIR path in .env
# Run connection test:
python test_mt5_auth.py
```

**Symbol Not Found:**
```bash
# Find correct symbol name
python find_oil_symbol.py
# Enable symbol in MT5
python enable_recommended_symbols.py
```

**ML Models Not Loading:**
```bash
# Check models directory
ls models/
# Retrain if needed
python scripts/train_ml_production.py
```

### 2. Debug Commands
```bash
# Debug settings
python scripts/debug_fix_archive/debug_settings.py

# Debug ML signals
python scripts/debug_fix_archive/debug_ml_signals.py

# Check broker symbols
python scripts/debug_fix_archive/check_broker_symbols.py
```

### 3. Log Files
```bash
# Main trading log
logs/trading_system.log

# Error log
logs/error.log

# GPT requests log (if enabled)
logs/gpt_requests.log
```

## Safety Checklist

### Before Going Live:
1. ✅ Test on demo account first
2. ✅ Verify all API keys in .env
3. ✅ Set appropriate risk profile
4. ✅ Test MT5 connection
5. ✅ Enable Telegram notifications
6. ✅ Set up monitoring dashboards
7. ✅ Test graceful shutdown
8. ✅ Verify risk limits
9. ✅ Check market hours configuration
10. ✅ Test with minimal capital first

### Daily Operations:
1. 📊 Check unified dashboard
2. 💰 Monitor drawdown levels
3. 🔔 Watch for Telegram alerts
4. 📈 Review open positions
5. 🎯 Check risk metrics
6. 🔍 Monitor error logs
7. 💾 Verify database backups
8. 🤖 Check ML performance
9. ⚡ Monitor circuit breaker status
10. 📰 Check news feed status

### Emergency Procedures:
```bash
# 1. Stop trading immediately
echo > STOP_TRADING

# 2. Close all positions (if needed)
# Use MT5 terminal directly

# 3. Check logs for errors
tail -f logs/trading_system.log

# 4. Review circuit breaker status
# Check dashboard or logs

# 5. Restart with conservative settings
# Edit .env: RISK_PROFILE=conservative
python trading_loop.py
```

## Quick Reference Card

```bash
# Start Trading
launcher.bat                    # Full system
start_trading.bat              # Quick start

# Stop Trading  
echo > STOP_TRADING            # Graceful stop

# Dashboards
start_unified_dashboard.bat    # Main dashboard (http://localhost:8501)
start_ml_monitor.bat          # ML monitor

# Testing
python test_mt5_simple.py      # Test connection
python run_backtest.py         # Run backtest

# ML Training
python scripts/train_ml_production.py

# Monitoring
tail -f logs/trading_system.log  # Watch logs
```

## Support Resources

- Configuration docs: `docs/`
- ML guides: `docs/ml_enhancement_guide.md`
- Symbol resolution: `docs/symbol_resolution_guide.md`
- Risk management: `PHASE1_RISK_MANAGEMENT_SUMMARY.md`
- Implementation guides: `docs/implementation/`

Remember: **Always test thoroughly on demo before live trading!**