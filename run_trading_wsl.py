#!/usr/bin/env python
"""
Wrapper script to run trading_loop.py with proper encoding from WSL
"""
import os
import sys
import subprocess

# Set UTF-8 encoding for Windows console
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'

# Get the directory of this script
script_dir = os.path.dirname(os.path.abspath(__file__))

# Build the command
python_exe = os.path.join(script_dir, 'venv', 'Scripts', 'python.exe')
trading_script = os.path.join(script_dir, 'trading_loop.py')

# Run the trading loop
cmd = [python_exe, '-X', 'utf8', trading_script] + sys.argv[1:]

# Execute
subprocess.run(cmd)