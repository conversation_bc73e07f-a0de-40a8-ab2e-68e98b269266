"""
Shared data types for decision recording system

This module contains the data classes used by both DecisionRecorder and DecisionRepository
to avoid circular imports.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from core.domain.models import TradingSignal, SignalType


@dataclass
class AgentAnalysis:
    """Individual agent's analysis and reasoning"""
    agent_name: str
    analysis: str
    signal_type: Optional[SignalType]
    confidence: float
    key_factors: List[str]
    timestamp: datetime


@dataclass
class MarketSnapshot:
    """Complete market state at decision time"""
    symbol: str
    current_price: float
    h1_data: Dict[str, Any]  # OHLC, indicators
    h4_data: Dict[str, Any]  # Background timeframe
    recent_volatility: float
    spread: float
    volume: float
    technical_indicators: Dict[str, float]
    news_sentiment: Optional[Dict[str, Any]]
    timestamp: datetime


@dataclass
class DecisionContext:
    """Full context for a trading decision"""
    decision_id: str
    session_id: str
    market_snapshot: MarketSnapshot
    agent_analyses: List[AgentAnalysis]
    debate_rounds: List[Dict[str, Any]]
    final_consensus: Dict[str, Any]
    signal_generated: Optional[TradingSignal]
    confidence_score: float
    ml_prediction: Optional[Dict[str, Any]]
    api_tokens_used: int
    api_cost_usd: float
    decision_time_ms: int
    timestamp: datetime


@dataclass
class DecisionOutcome:
    """Actual outcome of a decision"""
    decision_id: str
    trade_id: Optional[str]
    signal_executed: bool
    entry_price: Optional[float]
    exit_price: Optional[float]
    pnl: Optional[float]
    pnl_percentage: Optional[float]
    holding_time_minutes: Optional[int]
    max_drawdown: Optional[float]
    max_profit: Optional[float]
    exit_reason: Optional[str]
    timestamp: datetime