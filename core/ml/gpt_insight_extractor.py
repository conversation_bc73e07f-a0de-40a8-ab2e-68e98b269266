"""
GPT Insight Extractor for ML Feature Engineering
Extracts numerical features from GPT agent analyses for ML training
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import numpy as np

from core.agents.council_types import AgentAnalysis, CouncilDecision
from core.domain.models import SignalType, RiskClass

logger = logging.getLogger(__name__)


class GPTInsightExtractor:
    """
    Extracts numerical features from GPT agent analyses
    These features can be used to enhance ML model training
    """
    
    def __init__(self):
        self.sentiment_keywords = {
            'bullish': ['bullish', 'upward', 'rising', 'positive', 'strong buy', 'breakout', 'momentum up'],
            'bearish': ['bearish', 'downward', 'falling', 'negative', 'strong sell', 'breakdown', 'momentum down'],
            'neutral': ['neutral', 'sideways', 'ranging', 'consolidating', 'uncertain', 'mixed']
        }
        
        self.pattern_keywords = {
            'reversal': ['reversal', 'double top', 'double bottom', 'head and shoulders', 'inverse'],
            'continuation': ['continuation', 'flag', 'pennant', 'triangle', 'channel'],
            'breakout': ['breakout', 'breakthrough', 'break above', 'break below'],
            'support_resistance': ['support', 'resistance', 'key level', 'pivot']
        }
        
        self.risk_keywords = {
            'high_risk': ['high risk', 'dangerous', 'volatile', 'uncertain', 'caution'],
            'low_risk': ['low risk', 'safe', 'stable', 'confident', 'clear']
        }
    
    def extract_features_from_decision(self, decision: CouncilDecision) -> Dict[str, float]:
        """
        Extract numerical features from a complete council decision
        
        Returns:
            Dictionary of feature names to values
        """
        features = {}
        
        # Basic decision features
        features['gpt_final_confidence'] = decision.final_confidence / 100.0
        features['gpt_llm_confidence'] = decision.llm_confidence / 100.0
        features['gpt_consensus_level'] = decision.consensus_level
        
        # Signal type encoding
        features['gpt_signal_buy'] = 1.0 if decision.signal.signal == SignalType.BUY else 0.0
        features['gpt_signal_sell'] = 1.0 if decision.signal.signal == SignalType.SELL else 0.0
        features['gpt_signal_wait'] = 1.0 if decision.signal.signal == SignalType.WAIT else 0.0
        
        # Risk class encoding
        if hasattr(decision.signal, 'risk_class'):
            features['gpt_risk_low'] = 1.0 if decision.signal.risk_class == RiskClass.LOW else 0.0
            features['gpt_risk_medium'] = 1.0 if decision.signal.risk_class == RiskClass.MEDIUM else 0.0
            features['gpt_risk_high'] = 1.0 if decision.signal.risk_class == RiskClass.HIGH else 0.0
        
        # Extract from agent analyses
        if decision.agent_analyses:
            agent_features = self._extract_agent_features(decision.agent_analyses)
            features.update(agent_features)
        
        # Extract from debate insights
        if decision.debate_log:
            debate_features = self._extract_debate_features(decision.debate_log)
            features.update(debate_features)
        
        # Extract sentiment and patterns from rationale
        if decision.decision_rationale:
            rationale_features = self._extract_text_features(
                decision.decision_rationale, 
                'rationale'
            )
            features.update(rationale_features)
        
        return features
    
    def _extract_agent_features(self, analyses: List[AgentAnalysis]) -> Dict[str, float]:
        """Extract features from individual agent analyses"""
        features = {}
        
        # Count recommendations by type
        buy_count = sum(1 for a in analyses if a.recommendation == SignalType.BUY)
        sell_count = sum(1 for a in analyses if a.recommendation == SignalType.SELL)
        wait_count = sum(1 for a in analyses if a.recommendation == SignalType.WAIT)
        
        total = len(analyses)
        features['gpt_agent_buy_ratio'] = buy_count / total if total > 0 else 0.0
        features['gpt_agent_sell_ratio'] = sell_count / total if total > 0 else 0.0
        features['gpt_agent_wait_ratio'] = wait_count / total if total > 0 else 0.0
        
        # Average confidence by agent type
        agent_confidences = {}
        for analysis in analyses:
            agent_type = analysis.agent_name.lower()
            if agent_type not in agent_confidences:
                agent_confidences[agent_type] = []
            agent_confidences[agent_type].append(analysis.confidence / 100.0)
        
        for agent_type, confidences in agent_confidences.items():
            features[f'gpt_{agent_type}_confidence'] = np.mean(confidences)
        
        # Extract sentiment from key points
        all_points = []
        for analysis in analyses:
            all_points.extend(analysis.key_points)
        
        if all_points:
            sentiment_features = self._extract_text_features(
                ' '.join(all_points), 
                'agents'
            )
            features.update(sentiment_features)
        
        return features
    
    def _extract_debate_features(self, debate_log: List[Dict[str, Any]]) -> Dict[str, float]:
        """Extract features from debate rounds"""
        features = {}
        
        # Analyze debate evolution
        round_sentiments = []
        for round_data in debate_log:
            if isinstance(round_data, dict) and 'responses' in round_data:
                round_text = ' '.join(str(r) for r in round_data['responses'].values())
                sentiment = self._calculate_sentiment_score(round_text)
                round_sentiments.append(sentiment)
        
        if round_sentiments:
            features['gpt_debate_sentiment_mean'] = np.mean(round_sentiments)
            features['gpt_debate_sentiment_std'] = np.std(round_sentiments)
            features['gpt_debate_sentiment_trend'] = (
                round_sentiments[-1] - round_sentiments[0]
                if len(round_sentiments) > 1 else 0.0
            )
        
        return features
    
    def _extract_text_features(self, text: str, prefix: str) -> Dict[str, float]:
        """Extract numerical features from text"""
        features = {}
        text_lower = text.lower()
        
        # Sentiment score
        features[f'gpt_{prefix}_sentiment'] = self._calculate_sentiment_score(text_lower)
        
        # Pattern detection scores
        for pattern_type, keywords in self.pattern_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            features[f'gpt_{prefix}_{pattern_type}_score'] = score / len(keywords)
        
        # Risk assessment
        high_risk_score = sum(1 for keyword in self.risk_keywords['high_risk'] if keyword in text_lower)
        low_risk_score = sum(1 for keyword in self.risk_keywords['low_risk'] if keyword in text_lower)
        
        features[f'gpt_{prefix}_risk_score'] = (
            (high_risk_score - low_risk_score) / 
            max(1, high_risk_score + low_risk_score)
        )
        
        # Extract numerical values (confidence percentages, pip values, etc.)
        numbers = re.findall(r'\b(\d+(?:\.\d+)?)\s*(?:%|pips?|points?)\b', text_lower)
        if numbers:
            features[f'gpt_{prefix}_avg_numeric'] = np.mean([float(n) for n in numbers])
            features[f'gpt_{prefix}_max_numeric'] = np.max([float(n) for n in numbers])
        
        return features
    
    def _calculate_sentiment_score(self, text: str) -> float:
        """
        Calculate sentiment score from -1 (bearish) to 1 (bullish)
        """
        bullish_count = sum(1 for keyword in self.sentiment_keywords['bullish'] if keyword in text)
        bearish_count = sum(1 for keyword in self.sentiment_keywords['bearish'] if keyword in text)
        
        if bullish_count + bearish_count == 0:
            return 0.0
        
        return (bullish_count - bearish_count) / (bullish_count + bearish_count)
    
    def extract_features_batch(
        self, 
        decisions: List[Tuple[datetime, str, CouncilDecision]]
    ) -> List[Dict[str, Any]]:
        """
        Extract features from multiple decisions for batch processing
        
        Args:
            decisions: List of (timestamp, symbol, decision) tuples
            
        Returns:
            List of feature dictionaries with metadata
        """
        extracted_features = []
        
        for timestamp, symbol, decision in decisions:
            try:
                features = self.extract_features_from_decision(decision)
                
                # Add metadata
                features['timestamp'] = timestamp
                features['symbol'] = symbol
                features['original_signal'] = decision.signal.signal.value
                
                extracted_features.append(features)
                
            except Exception as e:
                logger.error(f"Failed to extract features for {symbol} at {timestamp}: {e}")
                continue
        
        logger.info(f"Extracted GPT features from {len(extracted_features)} decisions")
        return extracted_features