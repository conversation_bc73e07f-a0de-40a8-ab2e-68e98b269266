@echo off
REM Start MT5 in safe mode without Edge WebView

echo Starting MT5 in safe mode...

REM Disable Edge WebView temporarily
set WEBVIEW2_ADDITIONAL_BROWSER_ARGUMENTS=--no-sandbox --disable-gpu --disable-features=RendererCodeIntegrity
set WEBVIEW2_USER_DATA_FOLDER=%TEMP%\MT5WebView

REM Clear temp folder
rd /s /q "%TEMP%\MT5WebView" 2>nul
mkdir "%TEMP%\MT5WebView" 2>nul

REM Find and start MT5
if exist "C:\Program Files\MetaTrader 5\terminal64.exe" (
    start "" "C:\Program Files\MetaTrader 5\terminal64.exe"
) else if exist "C:\Program Files (x86)\MetaTrader 5\terminal64.exe" (
    start "" "C:\Program Files (x86)\MetaTrader 5\terminal64.exe"
) else (
    echo MT5 not found! Please update the path in this script.
    pause
)

echo.
echo MT5 started. If it works, login and save your credentials.
echo Then you can run the trading system.
echo.
pause