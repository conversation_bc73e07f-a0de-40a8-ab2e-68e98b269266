# Pinned Requirements File
# ========================
# This file contains exact version pins for all production dependencies.
# These versions have been tested and are known to work together.
#
# Generated: 2025-01-08
# Source: requirements.txt
#
# Usage:
# - For reproducible installs: pip install -r requirements-pinned.txt
# - To update pins: pip freeze > requirements-pinned.txt (after testing)
# - For latest versions: pip install --upgrade -r requirements.txt

# Core dependencies
pydantic==2.5.3
pydantic-settings==2.1.0
python-dotenv==1.0.0
MetaTrader5==5.0.45
openai==1.6.1
tiktoken==0.5.2

# Data processing
pandas==2.0.3
numpy==1.24.3

# Machine learning
scikit-learn==1.3.2
xgboost==2.0.3
joblib==1.3.2
imbalanced-learn==0.11.0

# Deep learning (for memory service)
torch==2.1.2
sentence-transformers==2.2.2
faiss-cpu==1.7.4

# Visualization and dashboards
matplotlib==3.7.4
seaborn==0.13.0
plotly==5.18.0
streamlit==1.29.0
mplfinance==0.12.10b0
tabulate==0.9.0

# Technical analysis
ta==0.10.2

# Async and networking
aiohttp==3.9.1
requests==2.31.0
backoff==2.2.1
nest-asyncio==1.5.8

# Database
aiosqlite==0.19.0

# Utilities
schedule==1.2.0
scipy==1.11.4
psutil==5.9.6
h5py==3.10.0