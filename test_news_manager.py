#!/usr/bin/env python3
"""
Test the news file manager functionality
"""

import logging
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.utils.news_file_manager import ensure_news_data, NewsFileManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """Test the news file manager"""
    print("🧪 Testing News File Manager")
    print("=" * 50)
    
    # Test the main function
    print("\n1️⃣ Testing ensure_news_data()...")
    success = ensure_news_data()
    
    if success:
        print("✅ News data ensured successfully!")
    else:
        print("❌ Failed to ensure news data!")
        return False
    
    # Test the manager directly
    print("\n2️⃣ Testing NewsFileManager directly...")
    manager = NewsFileManager()
    
    # Check if file exists
    if manager.news_file.exists():
        print(f"✅ News file exists: {manager.news_file}")
        
        # Validate file
        if manager.validate_file():
            print("✅ News file is valid")
            
            # Show file info
            try:
                import json
                with open(manager.news_file, 'r') as f:
                    data = json.load(f)
                print(f"📊 File contains {len(data)} events")
                
                # Show first few events
                if data:
                    print("\n📋 Sample events:")
                    for i, event in enumerate(data[:3]):
                        currency = event.get('currency', 'Unknown')
                        title = event.get('title', event.get('event', 'Unknown'))
                        impact = event.get('impact', 'unknown')
                        datetime_str = event.get('datetime', 'Unknown time')
                        print(f"   {i+1}. {datetime_str}: {currency} - {title} ({impact})")
                
            except Exception as e:
                print(f"⚠️  Could not read file details: {e}")
        else:
            print("❌ News file is invalid")
    else:
        print("❌ News file does not exist")
    
    # Test hash checking
    print("\n3️⃣ Testing hash comparison...")
    try:
        local_hash = manager._get_file_hash(manager.news_file)
        if local_hash:
            print(f"📋 Local file hash: {local_hash[:16]}...")
            
            remote_hash = manager._get_remote_hash()
            if remote_hash:
                print(f"📋 Remote file hash: {remote_hash[:16]}...")
                
                if local_hash == remote_hash:
                    print("✅ Hashes match - file is up to date")
                else:
                    print("🔄 Hashes differ - file would be updated")
            else:
                print("⚠️  Could not get remote hash")
        else:
            print("⚠️  Could not get local hash")
    except Exception as e:
        print(f"⚠️  Hash comparison failed: {e}")
    
    print("\n🎉 Test completed!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
