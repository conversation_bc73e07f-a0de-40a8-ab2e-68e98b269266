#!/usr/bin/env python3
"""
Launch script for the GPT Flow Dashboard
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    """Launch the GPT flow dashboard"""
    print("🚀 Launching GPT Flow Dashboard...")
    print("=" * 50)
    
    # Get the dashboard script path
    project_root = Path(__file__).parent.parent
    dashboard_script = project_root / "scripts" / "gpt_flow_dashboard.py"
    
    # Set environment
    env = os.environ.copy()
    env['PYTHONPATH'] = str(project_root)
    
    # Launch with streamlit
    try:
        print("Dashboard will be available at: http://localhost:8502")
        print("=" * 50)
        
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            str(dashboard_script),
            "--server.port", "8502",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ], env=env)
    except KeyboardInterrupt:
        print("\n\n✅ Dashboard closed successfully")
    except Exception as e:
        print(f"\n❌ Error launching dashboard: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()