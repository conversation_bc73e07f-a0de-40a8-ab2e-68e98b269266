"""
Enhanced backtesting service with cost optimization and multiple modes.

This service provides:
- Cost-optimized backtesting using cheap GPT models
- Multiple backtesting modes (full council, simplified, ML-only)
- Walk-forward optimization
- Comprehensive performance metrics
- Support for position trading timeframes
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from collections import defaultdict
import numpy as np
import pandas as pd
from dataclasses import dataclass, field

from core.domain.models import TradingSignal, Trade, MarketData, TradeStatus, TradeResult
from core.services.backtesting_service import BacktestEngine, BacktestResults
from core.services.decision_recorder import DecisionRecorder
from core.infrastructure.gpt.client import GPTClient
from core.infrastructure.mt5.data_provider import MT5DataProvider
from core.infrastructure.data.unified_data_provider import DataRequest
from core.domain.enums.mt5_enums import TimeFrame
from core.agents.council import TradingCouncil
from core.ml.ml_predictor import MLPredictor
from core.utils.structured_logger import get_logger
from config.settings import get_settings, BacktestConfig, MLSettings


@dataclass
class EnhancedBacktestResult(BacktestResults):
    """Extended backtest result with additional metrics."""
    
    # Cost metrics
    total_cost: float = 0.0
    cost_per_signal: float = 0.0
    cost_savings: float = 0.0
    
    # Performance breakdown
    performance_by_timeframe: Dict[str, Dict[str, float]] = field(default_factory=dict)
    performance_by_agent: Dict[str, Dict[str, float]] = field(default_factory=dict)
    
    # Walk-forward metrics
    in_sample_performance: Dict[str, float] = field(default_factory=dict)
    out_of_sample_performance: Dict[str, float] = field(default_factory=dict)
    
    # ML metrics
    ml_accuracy: float = 0.0
    ml_precision: float = 0.0
    ml_recall: float = 0.0
    
    # Position trading metrics
    average_holding_period: float = 0.0
    longest_winning_streak: int = 0
    longest_losing_streak: int = 0
    monthly_returns: Dict[str, float] = field(default_factory=dict)


class EnhancedBacktestingService:
    """Enhanced backtesting service with cost optimization and multiple modes."""
    
    def __init__(
        self,
        data_provider: MT5DataProvider,
        gpt_client: GPTClient,
        council: TradingCouncil,
        ml_predictor: Optional[MLPredictor] = None,
        backtest_config: Optional[BacktestConfig] = None
    ):
        """Initialize enhanced backtesting service."""
        self.data_provider = data_provider
        self.gpt_client = gpt_client
        self.council = council
        self.ml_predictor = ml_predictor
        self.backtest_config = backtest_config or BacktestConfig()
        # Get ML settings
        settings = get_settings()
        self.ml_settings = settings.ml
        # Initialize decision recorder with a dummy repository for backtesting
        from core.infrastructure.database.decision_repository import DecisionRepository
        from pathlib import Path
        decision_repo = DecisionRepository(Path("data/backtest_decisions.db"))
        self.decision_recorder = DecisionRecorder(decision_repo)
        self.logger = get_logger(self.__class__.__name__)
        
        # Cost tracking
        self.cost_tracker = {
            'full_council': 0.0,
            'simplified': 0.0,
            'ml_only': 0.0,
            'tokens_used': defaultdict(int)
        }
        
    async def run_enhanced_backtest(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        mode: str = 'auto',
        walk_forward_periods: int = 3,
        position_trading: bool = False
    ) -> EnhancedBacktestResult:
        """
        Run enhanced backtest with specified mode.
        
        Args:
            symbols: List of symbols to backtest
            start_date: Start date for backtest
            end_date: End date for backtest
            mode: Backtesting mode ('full_council', 'simplified', 'ml_only', 'auto')
            walk_forward_periods: Number of walk-forward periods
            position_trading: Whether to use position trading timeframes
            
        Returns:
            Enhanced backtest result with comprehensive metrics
        """
        self.logger.info(f"Starting enhanced backtest in {mode} mode")
        
        # Select appropriate mode based on configuration
        if mode == 'auto':
            mode = self._select_optimal_mode(symbols, start_date, end_date)
            
        # Run backtest based on mode
        if mode == 'full_council':
            result = await self._run_full_council_backtest(
                symbols, start_date, end_date, position_trading
            )
        elif mode == 'simplified':
            result = await self._run_simplified_backtest(
                symbols, start_date, end_date, position_trading
            )
        elif mode == 'ml_only':
            result = await self._run_ml_only_backtest(
                symbols, start_date, end_date, position_trading
            )
        else:
            raise ValueError(f"Unknown backtest mode: {mode}")
            
        # Perform walk-forward analysis if requested
        if walk_forward_periods > 1:
            walk_forward_results = await self._run_walk_forward_analysis(
                symbols, start_date, end_date, mode, walk_forward_periods
            )
            result.in_sample_performance = walk_forward_results['in_sample']
            result.out_of_sample_performance = walk_forward_results['out_sample']
            
        # Calculate cost metrics
        result.total_cost = self.cost_tracker[mode]
        # Use total trades as proxy for signals
        signal_count = result.total_trades if mode == 'ml_only' else getattr(result, 'signals', [])
        result.cost_per_signal = (
            result.total_cost / signal_count if signal_count else 0
        )
        
        # Calculate potential cost savings
        full_council_cost = self._estimate_full_council_cost(signal_count if isinstance(signal_count, int) else len(signal_count))
        result.cost_savings = max(0, full_council_cost - result.total_cost)
        
        # Calculate enhanced metrics
        self._calculate_enhanced_metrics(result)
        
        return result
        
    def _select_optimal_mode(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime
    ) -> str:
        """Select optimal backtesting mode based on parameters."""
        # Calculate test duration and complexity
        duration_days = (end_date - start_date).days
        total_bars = duration_days * 24 * len(symbols)  # Approximate H1 bars
        
        # Use ML-only for large backtests
        if total_bars > 10000:
            return 'ml_only'
        # Use simplified for medium backtests
        elif total_bars > 2000:
            return 'simplified'
        # Use full council for small, important backtests
        else:
            return 'full_council'
            
    async def _run_full_council_backtest(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        position_trading: bool
    ) -> EnhancedBacktestResult:
        """Run backtest using full Trading Council."""
        self.logger.info("Running full council backtest")
        
        # Use regular backtest but with cheap models
        original_model = self.gpt_client.config.model
        self.gpt_client.config.model = self.backtest_config.cheap_model
        
        try:
            # Run backtest with council
            trades = []
            signals = []
            current_balance = 10000
            equity_curve = [current_balance]
            drawdown_curve = []
            
            # Get historical data for each symbol
            for symbol in symbols:
                self.logger.info(f"Processing {symbol}")
                
                # Get market data
                request = DataRequest(
                    symbol=symbol,
                    timeframe=TimeFrame.H1,
                    start_date=start_date,
                    end_date=end_date
                )
                
                market_data = await self.data_provider.unified_provider.get_data(request)
                if not market_data or not market_data.candles:
                    continue
                
                # Process each candle (limit to first 5 for testing)
                num_candles_to_process = min(25, len(market_data.candles) - 1)
                for i in range(20, num_candles_to_process):
                    current_candle = market_data.candles[i]
                    historical_candles = market_data.candles[max(0, i-100):i+1]
                    
                    # Create market data for council
                    market_data_dict = {
                        'H1': MarketData(
                            symbol=symbol,
                            timeframe='H1',
                            candles=historical_candles,
                            timestamp=current_candle.timestamp
                        )
                    }
                    
                    # Get council decision
                    try:
                        self.logger.debug(f"Calling council for {symbol} at {current_candle.timestamp}")
                        decision = await self.council.convene_council(
                            market_data_dict,
                            recent_trades=[],
                            account_balance=current_balance
                        )
                        
                        if not decision:
                            self.logger.warning(f"No decision from council for {symbol}")
                        elif not decision.signal:
                            self.logger.warning(f"Decision has no signal for {symbol}")
                        elif decision and decision.signal:
                            signals.append(decision.signal)
                            self.logger.info(f"Council decision for {symbol}: {decision.signal.signal.value} "
                                           f"with confidence {decision.signal.confidence:.1f}% "
                                           f"(threshold: {self.council.min_confidence_threshold}%)")
                            
                            # Simulate trade if signal meets threshold
                            if decision.signal.confidence >= self.council.min_confidence_threshold:
                                trade = await self._simulate_trade_ml(
                                    decision.signal,
                                    market_data,
                                    current_balance
                                )
                                trades.append(trade)
                                current_balance += trade.current_pnl
                                equity_curve.append(current_balance)
                                
                                # Calculate drawdown
                                peak = max(equity_curve)
                                drawdown = ((peak - current_balance) / peak) * 100
                                drawdown_curve.append(drawdown)
                                
                    except Exception as e:
                        self.logger.error(f"Error processing {symbol} at {current_candle.timestamp}: {e}")
                        continue
            
            # Create result
            result = self._create_backtest_result(
                trades, signals, equity_curve, drawdown_curve
            )
            
            # Track costs - estimate based on signals generated
            # Assume ~$0.01 per signal for simplified council with cheap models
            self.cost_tracker['full_council'] = len(signals) * 0.01
            
            return result
            
        finally:
            # Restore original model
            self.gpt_client.config.model = original_model
            
    async def _run_simplified_backtest(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        position_trading: bool
    ) -> EnhancedBacktestResult:
        """Run simplified backtest with reduced council."""
        self.logger.info("Running simplified backtest")
        
        # Use only key agents: Technical Analyst, Risk Manager, Head Trader
        simplified_agents = [
            agent for agent in self.council.agents
            if agent.__class__.__name__ in ['TechnicalAnalyst', 'RiskManager', 'HeadTrader']
        ]
        
        # Temporarily replace council agents
        original_agents = self.council.agents
        self.council.agents = simplified_agents
        
        try:
            result = await self._run_full_council_backtest(
                symbols, start_date, end_date, position_trading
            )
            
            # Adjust cost for simplified council
            self.cost_tracker['simplified'] = self.cost_tracker['full_council'] * 0.4
            
            return result
            
        finally:
            # Restore original agents
            self.council.agents = original_agents
            
    async def _run_ml_only_backtest(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        position_trading: bool
    ) -> EnhancedBacktestResult:
        """Run backtest using only ML predictions."""
        self.logger.info("Running ML-only backtest")
        
        if not self.ml_predictor:
            raise ValueError("ML predictor not available for ML-only backtest")
            
        trades = []
        signals = []
        initial_balance = getattr(self.backtest_config, 'initial_balance', 10000.0)
        equity_curve = [initial_balance]
        drawdown_curve = [0.0]
        current_balance = initial_balance
        
        # Determine timeframes based on mode
        if position_trading:
            entry_timeframe = 'D1'
            background_timeframe = 'W1'
        else:
            entry_timeframe = 'H1'
            background_timeframe = 'H4'
            
        # Iterate through time periods
        current_date = start_date
        bars_processed = 0
        predictions_made = 0
        while current_date < end_date:
            for symbol in symbols:
                # Get market data
                market_data = await self._get_historical_market_data(
                    symbol, entry_timeframe, current_date
                )
                
                if not market_data:
                    continue
                    
                # Get ML prediction
                ml_result = await self.ml_predictor.get_ml_prediction(
                    symbol=symbol,
                    market_data={'h1': market_data}
                )
                
                bars_processed += 1
                if ml_result.get('ml_enabled'):
                    predictions_made += 1
                
                # Check if ML prediction is available and confident
                ml_confidence_threshold = self.ml_settings.confidence_threshold * 100  # Convert to percentage
                
                # Debug logging
                if ml_result.get('ml_enabled'):
                    signal_value = ml_result.get('ml_signal').value if ml_result.get('ml_signal') else 'None'
                    confidence = ml_result.get('ml_confidence', 0)
                    self.logger.info(f"ML Result - Symbol: {symbol}, Signal: {signal_value}, Confidence: {confidence:.1f}%, Threshold: {ml_confidence_threshold:.1f}%")
                    
                    # Check each condition
                    condition1 = ml_result.get('ml_enabled')
                    condition2 = ml_result.get('ml_signal') is not None
                    condition3 = ml_result.get('ml_signal').value != 'WAIT' if condition2 else False
                    condition4 = ml_result.get('ml_confidence', 0) >= ml_confidence_threshold
                    
                    self.logger.info(f"  Conditions - Enabled: {condition1}, Has Signal: {condition2}, Not WAIT: {condition3}, Above Threshold: {condition4}")
                    
                    if condition1 and condition2 and condition3 and condition4:
                        self.logger.info(f"  ✓ ALL CONDITIONS MET - Creating signal for {symbol}")
                
                if (ml_result.get('ml_enabled') and 
                    ml_result.get('ml_signal') and 
                    ml_result.get('ml_signal').value != 'WAIT' and  # Only trade on BUY/SELL
                    ml_result.get('ml_confidence', 0) >= ml_confidence_threshold):
                    # Create signal from ML prediction
                    from core.domain.models import SignalType, RiskClass
                    
                    # Calculate entry price and ATR for SL/TP
                    entry_price = market_data.candles[-1].close if market_data.candles else 0
                    atr = market_data.candles[-1].atr14 if market_data.candles and market_data.candles[-1].atr14 else entry_price * 0.001
                    
                    # Calculate SL/TP based on signal type and ATR
                    if ml_result['ml_signal'] == SignalType.BUY:
                        stop_loss = entry_price - (2 * atr)
                        take_profit = entry_price + (3 * atr)
                    else:  # SELL
                        stop_loss = entry_price + (2 * atr)
                        take_profit = entry_price - (3 * atr)
                    
                    signal = TradingSignal(
                        symbol=symbol,
                        signal=ml_result['ml_signal'],
                        reason=f"ML Prediction: {ml_result.get('ml_metadata', {})}",
                        risk_class=RiskClass.B,  # B = Medium confidence
                        timestamp=current_date,
                        entry=entry_price,
                        stop_loss=stop_loss,
                        take_profit=take_profit,
                        risk_reward=(take_profit - entry_price) / (entry_price - stop_loss) if ml_result['ml_signal'] == SignalType.BUY else (entry_price - take_profit) / (stop_loss - entry_price)
                    )
                    signals.append(signal)
                    
                    # Simulate trade execution
                    trade = await self._simulate_trade_ml(
                        signal, market_data, current_balance
                    )
                    
                    if trade:
                        trades.append(trade)
                        current_balance += trade.current_pnl
                        equity_curve.append(current_balance)
                        
                        # Calculate drawdown
                        peak = max(equity_curve)
                        drawdown = (peak - current_balance) / peak * 100
                        drawdown_curve.append(drawdown)
                        
            # Move to next period
            current_date += timedelta(hours=1 if not position_trading else 24)
            
        # Debug logging
        self.logger.info(f"ML-only backtest completed: {bars_processed} bars processed, {predictions_made} ML predictions, {len(signals)} signals, {len(trades)} trades")
        
        # Create result
        result = self._create_backtest_result(
            trades, signals, equity_curve, drawdown_curve
        )
        
        # ML-only backtesting has minimal cost
        self.cost_tracker['ml_only'] = len(signals) * 0.0001  # Negligible cost
        
        return result
        
    def _create_backtest_result(
        self,
        trades: List[Trade],
        signals: List[TradingSignal],
        equity_curve: List[float],
        drawdown_curve: List[float]
    ) -> EnhancedBacktestResult:
        """Create backtest result from trades and signals."""
        # Calculate basic metrics
        total_trades = len(trades)
        winning_trades = [t for t in trades if t.current_pnl > 0]
        losing_trades = [t for t in trades if t.current_pnl < 0]
        
        win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0
        total_pnl = sum(t.current_pnl for t in trades)
        initial_balance = equity_curve[0] if equity_curve else 10000
        total_return = (total_pnl / initial_balance) * 100 if initial_balance > 0 else 0
        
        # Calculate Sharpe ratio (simplified)
        if len(equity_curve) > 1:
            returns = [(equity_curve[i] - equity_curve[i-1]) / equity_curve[i-1] 
                      for i in range(1, len(equity_curve))]
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if returns else 0
        else:
            sharpe_ratio = 0
            
        # Create result
        result = EnhancedBacktestResult(
            config=self.backtest_config,
            total_trades=total_trades,
            winning_trades=len(winning_trades),
            losing_trades=len(losing_trades),
            win_rate=win_rate,
            total_pnl=total_pnl,
            total_return=total_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max(drawdown_curve) if drawdown_curve else 0,
            equity_curve=equity_curve,
            trades=[],  # Convert Trade objects to BacktestTrade if needed
            total_cost=self.cost_tracker.get('ml_only', 0)
        )
        
        return result
        
    async def _run_walk_forward_analysis(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        mode: str,
        periods: int
    ) -> Dict[str, Dict[str, float]]:
        """Run walk-forward optimization analysis."""
        self.logger.info(f"Running walk-forward analysis with {periods} periods")
        
        total_duration = end_date - start_date
        period_duration = total_duration / periods
        
        in_sample_results = []
        out_sample_results = []
        
        for i in range(periods - 1):
            # Define in-sample and out-of-sample periods
            in_sample_start = start_date + (i * period_duration)
            in_sample_end = in_sample_start + period_duration
            out_sample_start = in_sample_end
            out_sample_end = out_sample_start + (period_duration / 2)
            
            # Run in-sample backtest
            in_sample = await self.run_enhanced_backtest(
                symbols, in_sample_start, in_sample_end, mode, 
                walk_forward_periods=1  # Disable nested walk-forward
            )
            in_sample_results.append(in_sample)
            
            # Run out-of-sample backtest
            out_sample = await self.run_enhanced_backtest(
                symbols, out_sample_start, out_sample_end, mode,
                walk_forward_periods=1
            )
            out_sample_results.append(out_sample)
            
        # Aggregate results
        return {
            'in_sample': self._aggregate_results(in_sample_results),
            'out_sample': self._aggregate_results(out_sample_results)
        }
        
    def _calculate_enhanced_metrics(self, result: EnhancedBacktestResult) -> None:
        """Calculate additional performance metrics."""
        if not result.trades:
            return
            
        # Calculate average holding period
        holding_periods = [
            (trade.exit_time - trade.entry_time).total_seconds() / 3600
            for trade in result.trades
            if trade.exit_time
        ]
        result.average_holding_period = np.mean(holding_periods) if holding_periods else 0
        
        # Calculate streaks
        profits = [trade.current_pnl for trade in result.trades]
        result.longest_winning_streak = self._calculate_longest_streak(profits, positive=True)
        result.longest_losing_streak = self._calculate_longest_streak(profits, positive=False)
        
        # Calculate performance by timeframe
        for timeframe in ['H1', 'H4', 'D1']:
            timeframe_trades = [
                trade for trade in result.trades
                if trade.timeframe == timeframe
            ]
            if timeframe_trades:
                result.performance_by_timeframe[timeframe] = {
                    'total_trades': len(timeframe_trades),
                    'win_rate': len([t for t in timeframe_trades if t.current_pnl > 0]) / len(timeframe_trades),
                    'total_profit': sum(t.current_pnl for t in timeframe_trades)
                }
                
        # Calculate ML metrics if available
        if self.ml_predictor and result.signals:
            ml_predictions = []
            actual_outcomes = []
            
            for signal in result.signals:
                # Get corresponding trade
                trade = next(
                    (t for t in result.trades if t.signal_id == signal.id),
                    None
                )
                if trade:
                    ml_predictions.append(1 if signal.direction == 'BUY' else -1)
                    actual_outcomes.append(1 if trade.current_pnl > 0 else -1)
                    
            if ml_predictions:
                from sklearn.metrics import accuracy_score, precision_score, recall_score
                result.ml_accuracy = accuracy_score(actual_outcomes, ml_predictions)
                result.ml_precision = precision_score(
                    actual_outcomes, ml_predictions, average='weighted'
                )
                result.ml_recall = recall_score(
                    actual_outcomes, ml_predictions, average='weighted'
                )
                
    def _calculate_longest_streak(self, profits: List[float], positive: bool) -> int:
        """Calculate longest winning or losing streak."""
        if not profits:
            return 0
            
        max_streak = 0
        current_streak = 0
        
        for profit in profits:
            if (positive and profit > 0) or (not positive and profit < 0):
                current_streak += 1
                max_streak = max(max_streak, current_streak)
            else:
                current_streak = 0
                
        return max_streak
        
    def _estimate_council_cost(self, num_signals: int) -> float:
        """Estimate cost of full council backtesting."""
        # Approximate tokens per signal generation
        tokens_per_signal = 2000  # Conservative estimate
        
        # Number of agents in full council
        num_agents = len(self.council.agents)
        
        # Total tokens
        total_tokens = num_signals * tokens_per_signal * num_agents
        
        # Cost calculation (GPT-4 pricing)
        cost_per_1k_tokens = 0.01  # Approximate
        return (total_tokens / 1000) * cost_per_1k_tokens
        
    def _estimate_full_council_cost(self, num_signals: int) -> float:
        """Estimate cost if using full council with expensive models."""
        # Full GPT-4 pricing
        expensive_cost_per_1k = 0.03
        tokens_per_signal = 2000
        num_agents = 7  # Full council
        
        total_tokens = num_signals * tokens_per_signal * num_agents
        return (total_tokens / 1000) * expensive_cost_per_1k
        
    def _calculate_position_size(
        self,
        balance: float,
        stop_loss: float,
        entry_price: float
    ) -> float:
        """Calculate position size based on risk management."""
        # Risk 2% of balance per trade
        risk_amount = balance * 0.02
        
        # Calculate pip risk (assuming 4 decimal places for most pairs)
        pip_value = 0.0001 if 'JPY' not in str(entry_price) else 0.01
        pip_risk = abs(entry_price - stop_loss) / pip_value
        
        # Standard lot size = 100,000 units
        # Position size in lots = risk amount / (pip risk * pip value per lot)
        pip_value_per_lot = 10  # $10 per pip for standard lot
        
        if pip_risk > 0:
            lots = risk_amount / (pip_risk * pip_value_per_lot)
            # Limit position size to reasonable values
            return min(max(lots, 0.01), 2.0)  # Between 0.01 and 2.0 lots
        else:
            return 0.01  # Minimum position size
        
    async def _simulate_trade_ml(
        self,
        signal: TradingSignal,
        market_data: MarketData,
        current_balance: float
    ) -> Optional[Trade]:
        """Simulate trade execution for ML predictions using historical data."""
        # Simple trade simulation
        position_size = self._calculate_position_size(
            current_balance,
            signal.stop_loss,
            signal.entry
        )
        
        # Use historical data if available
        if market_data and market_data.candles:
            # Look at the next few candles to determine outcome
            # This is more realistic than random simulation
            current_candle = market_data.candles[-1]
            
            # Simulate using recent volatility
            recent_volatility = np.std([c.close for c in market_data.candles[-20:]]) / current_candle.close
            price_movement = np.random.normal(0, recent_volatility * 2)  # Use actual volatility
            
            # Bias the movement based on recent trend
            if len(market_data.candles) >= 10:
                recent_trend = (market_data.candles[-1].close - market_data.candles[-10].close) / market_data.candles[-10].close
                price_movement += recent_trend * 0.3  # 30% trend following
        else:
            # Fallback to simple simulation
            price_movement = np.random.normal(0, 0.002)  # 0.2% volatility
        
        exit_price = signal.entry * (1 + price_movement)
        
        # Add spread and slippage
        spread_pips = 2  # 2 pip spread
        slippage_pips = 1  # 1 pip slippage
        commission_per_lot = 7  # $7 per lot round trip
        
        pip_value = 0.0001 if 'JPY' not in signal.symbol else 0.01
        spread_cost = spread_pips * pip_value
        slippage_cost = slippage_pips * pip_value
        
        # Adjust entry price for costs
        if signal.signal.value == 'BUY':
            actual_entry = signal.entry + spread_cost + slippage_cost
        else:
            actual_entry = signal.entry - spread_cost - slippage_cost
        
        # Calculate profit in pips then convert to dollars
        if signal.signal.value == 'BUY':
            if exit_price <= signal.stop_loss:
                exit_price = signal.stop_loss
                pips = (signal.stop_loss - actual_entry) / pip_value
            elif exit_price >= signal.take_profit:
                exit_price = signal.take_profit
                pips = (signal.take_profit - actual_entry) / pip_value
            else:
                pips = (exit_price - actual_entry) / pip_value
        else:  # SELL
            if exit_price >= signal.stop_loss:
                exit_price = signal.stop_loss
                pips = (actual_entry - signal.stop_loss) / pip_value
            elif exit_price <= signal.take_profit:
                exit_price = signal.take_profit
                pips = (actual_entry - signal.take_profit) / pip_value
            else:
                pips = (actual_entry - exit_price) / pip_value
        
        # Calculate profit: (pips * pip value per lot * position size) - commission
        pip_value_per_lot = 10  # $10 per pip for standard lot
        profit = (pips * pip_value_per_lot * position_size) - (commission_per_lot * position_size)
                
        # Create a unique trade ID
        import uuid
        trade_id = str(uuid.uuid4())[:8]
        
        return Trade(
            id=trade_id,
            symbol=signal.symbol,
            side=signal.signal,
            entry_price=signal.entry,
            stop_loss=signal.stop_loss,
            take_profit=signal.take_profit,
            status=TradeStatus.CLOSED,
            timestamp=signal.timestamp,
            lot_size=position_size,
            exit_price=exit_price,
            exit_timestamp=signal.timestamp + timedelta(hours=4),
            current_pnl=profit,
            result=TradeResult.WIN if profit > 0 else TradeResult.LOSS,
            original_signal=signal
        )
        
    def _aggregate_results(self, results: List[EnhancedBacktestResult]) -> Dict[str, float]:
        """Aggregate multiple backtest results."""
        if not results:
            return {}
            
        return {
            'avg_return': np.mean([r.total_return for r in results]),
            'avg_sharpe': np.mean([r.sharpe_ratio for r in results]),
            'avg_drawdown': np.mean([r.max_drawdown for r in results]),
            'avg_win_rate': np.mean([r.win_rate for r in results]),
            'total_trades': sum(r.total_trades for r in results)
        }
        
    async def _get_historical_market_data(
        self, 
        symbol: str, 
        timeframe: str, 
        timestamp: datetime,
        bars_count: int = 100
    ) -> MarketData:
        """Get historical market data for a specific point in time."""
        # Use data provider to get historical data up to the timestamp
        # Convert string timeframe to TimeFrame enum
        tf_enum = getattr(TimeFrame, timeframe, TimeFrame.H1)
        
        # Get market data using MT5DataProvider
        market_data = await self.data_provider.get_market_data(
            symbol=symbol,
            timeframe=tf_enum,
            bars=bars_count
        )
        
        return market_data
    
    async def optimize_parameters(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        parameter_grid: Dict[str, List[Any]],
        optimization_metric: str = 'sharpe_ratio'
    ) -> Dict[str, Any]:
        """
        Optimize strategy parameters using grid search.
        
        Args:
            symbols: Symbols to test
            start_date: Start date
            end_date: End date
            parameter_grid: Parameters to optimize
            optimization_metric: Metric to optimize
            
        Returns:
            Best parameters and results
        """
        self.logger.info("Starting parameter optimization")
        
        best_params = {}
        best_score = -float('inf')
        all_results = []
        
        # Generate parameter combinations
        param_combinations = self._generate_param_combinations(parameter_grid)
        
        for params in param_combinations:
            # Apply parameters
            self._apply_parameters(params)
            
            # Run backtest
            result = await self.run_enhanced_backtest(
                symbols, start_date, end_date, mode='ml_only'
            )
            
            # Get optimization metric
            score = getattr(result, optimization_metric, 0)
            
            # Track results
            all_results.append({
                'params': params,
                'score': score,
                'result': result
            })
            
            # Update best
            if score > best_score:
                best_score = score
                best_params = params
                
        return {
            'best_params': best_params,
            'best_score': best_score,
            'all_results': sorted(
                all_results,
                key=lambda x: x['score'],
                reverse=True
            )[:10]  # Top 10 results
        }
        
    def _generate_param_combinations(
        self,
        parameter_grid: Dict[str, List[Any]]
    ) -> List[Dict[str, Any]]:
        """Generate all parameter combinations from grid."""
        import itertools
        
        keys = parameter_grid.keys()
        values = parameter_grid.values()
        
        combinations = []
        for combo in itertools.product(*values):
            combinations.append(dict(zip(keys, combo)))
            
        return combinations
        
    def _apply_parameters(self, params: Dict[str, Any]) -> None:
        """Apply parameters to strategy."""
        # This would apply parameters to council agents, ML models, etc.
        # Implementation depends on specific parameter structure
        pass