@echo off
echo ========================================
echo ForexFactory News Data Updater
echo ========================================
echo.

echo Updating ForexFactory news data...
python scripts\manage_news_data.py ensure

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ News data update completed successfully!
    echo.
    echo You can now run your trading system.
) else (
    echo.
    echo ❌ News data update failed!
    echo.
    echo Trying fallback creation...
    python scripts\create_fallback_news.py
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ Fallback data created successfully!
    ) else (
        echo ❌ Fallback creation also failed!
        echo Please check your internet connection and try again.
    )
)

echo.
pause
