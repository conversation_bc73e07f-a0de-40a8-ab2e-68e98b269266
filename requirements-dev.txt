# Development Requirements File
# =============================
# This file contains dependencies needed only for development, testing, and code quality.
# These are NOT needed for running the trading system in production.
#
# Usage:
# - For development setup: pip install -r requirements.txt -r requirements-dev.txt
# - For exact dev versions: pip install -r requirements-dev-pinned.txt
#
# Categories: Testing, Code Quality, Security, Documentation, Profiling, Debugging

# Testing
pytest>=7.2.0
pytest-asyncio>=0.20.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0

# Code quality
black>=23.0.0
isort>=5.11.0
flake8>=6.0.0
pylint>=2.15.0
mypy>=1.0.0

# Security
bandit>=1.7.4
safety>=2.3.0

# Documentation
sphinx>=5.3.0
sphinx-rtd-theme>=1.1.0

# Profiling
memory-profiler>=0.60.0
py-spy>=0.3.14

# Debugging
ipdb>=0.13.11
pdbpp>=0.10.3