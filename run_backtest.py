# run_backtest.py
"""
Script to run backtests on the GPT Trading System with enhanced cost-optimized backtesting
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta, timezone
import logging
from typing import Dict, List, Tuple, Optional
import argparse

# Add project root to Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from config.settings import get_settings, BacktestConfig
from core.infrastructure.mt5.client import MT5Client
from core.infrastructure.mt5.data_provider import MT5DataProvider
from core.infrastructure.gpt.client import GPTClient
from core.agents.council import TradingCouncil
from core.ml.ml_predictor import MLPredictor
from core.services.backtesting_service import (
    BacktestEngine, BacktestConfig as LegacyBacktestConfig, BacktestMode, BacktestReportGenerator
)
from core.services.enhanced_backtesting_service import EnhancedBacktestingService, EnhancedBacktestResult
from core.services.llm_simulator import LLMSimulator
from core.services.decision_recorder import DecisionRecorder
from core.utils.chart_utils import ChartGenerator
from core.utils.data_diagnostics import DataDiagnostics

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def check_data_availability(data_provider: MT5DataProvider, symbols: List[str]):
    """Check and report data availability for all symbols"""
    print("\n" + "="*80)
    print("DATA AVAILABILITY CHECK")
    print("="*80)
    
    diagnostics = DataDiagnostics(data_provider.unified_provider)
    report = await diagnostics.generate_availability_report(symbols)
    
    available_symbols = []
    date_ranges = {}
    
    for symbol, info in report['symbols'].items():
        if info['available']:
            available_symbols.append(symbol)
            start = datetime.fromisoformat(info['start_date'])
            end = datetime.fromisoformat(info['end_date'])
            date_ranges[symbol] = (start, end)
            
            print(f"\n{symbol}:")
            print(f"  Date Range: {start.date()} to {end.date()} ({info['total_days']} days)")
            print(f"  Timeframes:")
            for tf, tf_info in info['timeframes'].items():
                if tf_info['available']:
                    print(f"    {tf}: OK {tf_info['bars_available']} bars available")
                else:
                    print(f"    {tf}: X {tf_info.get('error', 'Not available')}")
        else:
            print(f"\n{symbol}: X {info.get('error', 'Not available')}")
    
    print("\n" + "="*80)
    
    return available_symbols, date_ranges


async def determine_backtest_period(date_ranges: Dict[str, Tuple[datetime, datetime]]):
    """Determine optimal backtest period based on available data"""
    if not date_ranges:
        return None, None
    
    # Find common date range
    latest_start = max(start for start, _ in date_ranges.values())
    earliest_end = min(end for _, end in date_ranges.values())
    
    if latest_start >= earliest_end:
        print("\nNo overlapping data period found across symbols!")
        return None, None
    
    # Calculate available period
    available_days = (earliest_end - latest_start).days
    
    print(f"\nCommon data period: {latest_start.date()} to {earliest_end.date()} ({available_days} days)")
    
    # Determine backtest period based on available data
    if available_days < 30:
        print("Warning: Less than 30 days of data available!")
        return latest_start, earliest_end
    
    # Use last 6 months or available data, whichever is smaller
    ideal_days = 180  # 6 months
    actual_days = min(ideal_days, available_days - 10)  # Leave some buffer
    
    backtest_end = earliest_end - timedelta(days=5)  # Small buffer from end
    backtest_start = backtest_end - timedelta(days=actual_days)
    
    # Ensure start is not before available data
    backtest_start = max(backtest_start, latest_start + timedelta(days=5))
    
    return backtest_start, backtest_end


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Run enhanced backtests with cost optimization'
    )
    
    parser.add_argument(
        '--mode',
        choices=['full_council', 'simplified', 'ml_only', 'auto', 'legacy'],
        default='auto',
        help='Backtesting mode (default: auto)'
    )
    
    parser.add_argument(
        '--symbols',
        nargs='+',
        help='Symbols to backtest (default: from config)'
    )
    
    parser.add_argument(
        '--days',
        type=int,
        default=180,
        help='Number of days to backtest (default: 180)'
    )
    
    parser.add_argument(
        '--position-trading',
        action='store_true',
        help='Use position trading timeframes (D1/W1)'
    )
    
    parser.add_argument(
        '--walk-forward',
        type=int,
        default=0,
        help='Number of walk-forward periods (0 to disable)'
    )
    
    parser.add_argument(
        '--train-simulator',
        action='store_true',
        help='Train LLM simulator before backtesting'
    )
    
    parser.add_argument(
        '--compare-modes',
        action='store_true',
        help='Compare all backtesting modes'
    )
    
    parser.add_argument(
        '--optimize',
        action='store_true',
        help='Run parameter optimization'
    )
    
    return parser.parse_args()


async def run_enhanced_backtest(
    enhanced_service: EnhancedBacktestingService,
    symbols: List[str],
    start_date: datetime,
    end_date: datetime,
    mode: str,
    walk_forward: int,
    position_trading: bool
) -> EnhancedBacktestResult:
    """Run enhanced backtest with specified parameters."""
    print(f"\n{'='*80}")
    print(f"RUNNING {mode.upper()} BACKTEST")
    print(f"{'='*80}")
    
    result = await enhanced_service.run_enhanced_backtest(
        symbols=symbols,
        start_date=start_date,
        end_date=end_date,
        mode=mode,
        walk_forward_periods=walk_forward,
        position_trading=position_trading
    )
    
    # Print enhanced results
    print(f"\nBasic Metrics:")
    print(f"  Total Trades: {result.total_trades}")
    print(f"  Win Rate: {result.win_rate:.1%}")
    print(f"  Total Return: {result.total_return:.2f}%")
    print(f"  Sharpe Ratio: {result.sharpe_ratio:.2f}")
    print(f"  Max Drawdown: {result.max_drawdown:.2f}%")
    
    print(f"\nCost Analysis:")
    print(f"  Total Cost: ${result.total_cost:.2f}")
    print(f"  Cost per Signal: ${result.cost_per_signal:.4f}")
    print(f"  Cost Savings: ${result.cost_savings:.2f} ({(result.cost_savings/max(result.total_cost+result.cost_savings, 1))*100:.1f}%)")
    
    if result.ml_accuracy > 0:
        print(f"\nML Performance:")
        print(f"  Accuracy: {result.ml_accuracy:.1%}")
        print(f"  Precision: {result.ml_precision:.1%}")
        print(f"  Recall: {result.ml_recall:.1%}")
    
    if result.performance_by_timeframe:
        print(f"\nPerformance by Timeframe:")
        for tf, metrics in result.performance_by_timeframe.items():
            print(f"  {tf}: {metrics['total_trades']} trades, {metrics['win_rate']:.1%} win rate")
    
    if walk_forward > 1:
        print(f"\nWalk-Forward Analysis:")
        print(f"  In-Sample Sharpe: {result.in_sample_performance.get('avg_sharpe', 0):.2f}")
        print(f"  Out-of-Sample Sharpe: {result.out_of_sample_performance.get('avg_sharpe', 0):.2f}")
    
    return result


async def compare_backtest_modes(
    enhanced_service: EnhancedBacktestingService,
    symbols: List[str],
    start_date: datetime,
    end_date: datetime
):
    """Compare performance across different backtesting modes."""
    print(f"\n{'='*80}")
    print("COMPARING BACKTEST MODES")
    print(f"{'='*80}")
    
    modes = ['full_council', 'simplified', 'ml_only']
    results = {}
    
    for mode in modes:
        try:
            result = await enhanced_service.run_enhanced_backtest(
                symbols=symbols,
                start_date=start_date,
                end_date=end_date,
                mode=mode,
                walk_forward_periods=1
            )
            results[mode] = result
        except Exception as e:
            logger.error(f"Failed to run {mode} backtest: {e}")
            results[mode] = None
    
    # Print comparison
    print(f"\n{'Mode':<15} {'Trades':<10} {'Win Rate':<12} {'Sharpe':<10} {'Cost':<12} {'Time/Signal':<12}")
    print("-" * 80)
    
    for mode, result in results.items():
        if result:
            time_per_signal = 30.0 if mode == 'full_council' else (10.0 if mode == 'simplified' else 0.1)
            print(f"{mode:<15} {result.total_trades:<10} {result.win_rate:<12.1%} "
                  f"{result.sharpe_ratio:<10.2f} ${result.total_cost:<11.2f} {time_per_signal:<12.1f}s")
    
    # Calculate relative performance
    if results['full_council'] and results['ml_only']:
        ml_accuracy = (
            1 - abs(results['ml_only'].sharpe_ratio - results['full_council'].sharpe_ratio) /
            max(abs(results['full_council'].sharpe_ratio), 1)
        )
        print(f"\nML Simulation Accuracy vs Full Council: {ml_accuracy:.1%}")


async def main():
    """Run enhanced backtest with cost optimization"""
    
    # Parse arguments
    args = parse_arguments()
    
    # Force reload of environment variables
    from dotenv import load_dotenv
    load_dotenv(override=True)
    
    # Check API key validity for non-ML modes
    if args.mode != 'ml_only':
        current_key = os.getenv('OPENAI_API_KEY', 'NOT_SET')
        if len(current_key) < 20 or current_key == 'sk-...':
            print(f"Warning: Invalid API key detected for {args.mode} mode")
    
    # For ML-only mode, set a dummy API key if not present
    if args.mode == 'ml_only' and not os.getenv('OPENAI_API_KEY', '').startswith('sk-'):
        os.environ['OPENAI_API_KEY'] = 'sk-test1234567890abcdefghijklmnopqrstuvwxyz1234567890'
    
    # For testing simplified mode, override to use ultra cheap settings
    if args.mode == 'simplified':
        os.environ['BACKTEST_USE_CHEAP_MODELS'] = 'true'
        os.environ['COUNCIL_QUICK_MODE'] = 'true'
    
    # Ensure MarketAux settings are properly set
    if not os.getenv('MARKETAUX_ENABLED'):
        os.environ['MARKETAUX_ENABLED'] = 'false'
    if not os.getenv('MARKETAUX_SENTIMENT_WEIGHT'):
        os.environ['MARKETAUX_SENTIMENT_WEIGHT'] = '0.3'
    
    # Clear problematic TRADING_SYMBOLS env var if it exists or is empty
    if 'TRADING_SYMBOLS' in os.environ and (os.environ['TRADING_SYMBOLS'] == '' or os.environ['TRADING_SYMBOLS'].strip() == ''):
        del os.environ['TRADING_SYMBOLS']
    
    # Load settings
    settings = get_settings()
    
    # Initialize MT5 client
    print("Initializing MT5 connection...")
    mt5_client = MT5Client(settings.mt5)
    if not mt5_client.initialize():
        print("Failed to initialize MT5")
        return
    
    try:
        # Create data provider
        data_provider = MT5DataProvider(mt5_client)
        
        # Determine symbols
        symbols_to_test = args.symbols or settings.trading.symbols[:3]
        
        # Check data availability
        available_symbols, date_ranges = await check_data_availability(
            data_provider, symbols_to_test
        )
        
        if not available_symbols:
            print("\nNo data available for any symbols!")
            return
        
        # Determine backtest period
        backtest_end = datetime.now(timezone.utc) - timedelta(days=5)
        backtest_start = backtest_end - timedelta(days=args.days)
        
        # Adjust for available data
        if date_ranges:
            earliest_start = min(start for start, _ in date_ranges.values())
            latest_end = max(end for _, end in date_ranges.values())
            
            backtest_start = max(backtest_start, earliest_start + timedelta(days=5))
            backtest_end = min(backtest_end, latest_end - timedelta(days=5))
        
        print(f"\nBacktest Configuration:")
        print(f"  Period: {backtest_start.date()} to {backtest_end.date()}")
        print(f"  Symbols: {', '.join(available_symbols)}")
        print(f"  Mode: {args.mode}")
        
        # Use legacy backtest for comparison if requested
        if args.mode == 'legacy':
            # Run legacy backtest
            config = LegacyBacktestConfig(
                start_date=backtest_start,
                end_date=backtest_end,
                symbols=available_symbols,
                mode=BacktestMode.OFFLINE_ONLY,
                initial_balance=10000,
                risk_per_trade=settings.trading.risk_per_trade_percent / 100,
                max_open_trades=settings.trading.max_open_trades,
                save_results=True
            )
            
            engine = BacktestEngine(
                data_provider=data_provider,
                chart_generator=ChartGenerator() if ChartGenerator else None,
                db_path=settings.database.db_path
            )
            
            print(f"\nStarting legacy backtest...")
            results = await engine.run_backtest(config)
            
            # Print legacy results
            print("\n" + "="*80)
            print("LEGACY BACKTEST RESULTS")
            print("="*80)
            print(f"Total Trades: {results.total_trades}")
            print(f"Win Rate: {results.win_rate:.1%}")
            print(f"Total Return: {results.total_return:.2f}%")
            print(f"Sharpe Ratio: {results.sharpe_ratio:.2f}")
            
        else:
            # Initialize enhanced backtesting components
            gpt_client = None
            council = None
            
            # Only create GPT client and council if not in ml_only mode
            if args.mode != 'ml_only':
                gpt_client = GPTClient(settings.gpt)
                
                # Create council
                from core.agents.technical_analyst import TechnicalAnalyst
                from core.agents.fundamental_analyst import FundamentalAnalyst
                from core.agents.sentiment_reader import SentimentReader
                from core.agents.risk_manager import RiskManager
                from core.agents.momentum_trader import MomentumTrader
                from core.agents.contrarian_trader import ContrarianTrader
                from core.agents.head_trader import HeadTrader
                
                council = TradingCouncil(
                    gpt_client=gpt_client,
                    account_balance=10000,
                    risk_per_trade=settings.trading.risk_per_trade_percent / 100,  # Convert percent to decimal
                    min_confidence_threshold=settings.trading.council_min_confidence,
                    llm_weight=settings.trading.council_llm_weight,
                    ml_weight=settings.trading.council_ml_weight,
                    trading_config=settings.trading
                )
            
            # Initialize ML predictor if available
            ml_predictor = None
            if settings.ml.enabled:
                try:
                    from pathlib import Path
                    models_dir = Path("models")
                    ml_predictor = MLPredictor(models_dir)
                except Exception as e:
                    logger.warning(f"Could not initialize ML predictor: {e}")
            
            # Create enhanced backtesting service
            enhanced_service = EnhancedBacktestingService(
                data_provider=data_provider,
                gpt_client=gpt_client,
                council=council,
                ml_predictor=ml_predictor,
                backtest_config=settings.backtest if hasattr(settings, 'backtest') else None
            )
            
            # Train LLM simulator if requested
            if args.train_simulator:
                print("\nTraining LLM simulator...")
                simulator = LLMSimulator()
                metrics = await simulator.train_from_decisions()
                print(f"Simulator trained with {metrics.get('direction_accuracy', 0):.1%} accuracy")
            
            # Run comparison if requested
            if args.compare_modes:
                await compare_backtest_modes(
                    enhanced_service,
                    available_symbols,
                    backtest_start,
                    backtest_end
                )
            
            # Run parameter optimization if requested
            elif args.optimize:
                print("\nRunning parameter optimization...")
                param_grid = {
                    'confidence_threshold': [0.6, 0.7, 0.8],
                    'risk_per_trade': [0.01, 0.02, 0.03],
                    'max_open_trades': [3, 5, 7]
                }
                
                best_params = await enhanced_service.optimize_parameters(
                    symbols=available_symbols,
                    start_date=backtest_start,
                    end_date=backtest_end,
                    parameter_grid=param_grid,
                    optimization_metric='sharpe_ratio'
                )
                
                print(f"\nBest Parameters:")
                print(f"  {best_params['best_params']}")
                print(f"  Best Sharpe: {best_params['best_score']:.2f}")
            
            # Run single backtest
            else:
                result = await run_enhanced_backtest(
                    enhanced_service,
                    available_symbols,
                    backtest_start,
                    backtest_end,
                    args.mode,
                    args.walk_forward,
                    args.position_trading
                )
                
                # Generate report if trades exist
                if result.total_trades > 0:
                    print("\nGenerating enhanced report...")
                    # TODO: Create enhanced report generator
        
    except Exception as e:
        logger.exception(f"Backtest failed: {e}")
    finally:
        mt5_client.shutdown()
        print("\nMT5 connection closed")


if __name__ == "__main__":
    # Clear problematic TRADING_SYMBOLS env var before anything else
    if 'TRADING_SYMBOLS' in os.environ and (os.environ['TRADING_SYMBOLS'] == '' or os.environ['TRADING_SYMBOLS'].strip() == ''):
        del os.environ['TRADING_SYMBOLS']
    
    asyncio.run(main())