#!/usr/bin/env python3
"""
Test script for the enhanced backtesting framework.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from config.settings import get_settings
from core.infrastructure.mt5.client import MT5Client
from core.infrastructure.mt5.data_provider import MT5DataProvider
from core.infrastructure.gpt.client import GPTClient
from core.agents.council import TradingCouncil
from core.agents.technical_analyst import TechnicalAnalyst
from core.agents.risk_manager import RiskManager
from core.agents.head_trader import HeadTrader
from core.services.enhanced_backtesting_service import EnhancedBacktestingService
from core.services.llm_simulator import LLMSimulator


async def test_enhanced_backtest():
    """Test the enhanced backtesting framework."""
    print("Testing Enhanced Backtesting Framework")
    print("=" * 80)
    
    # Load settings
    settings = get_settings()
    
    # Initialize MT5 client
    print("\n1. Initializing MT5 connection...")
    mt5_client = MT5Client(settings.mt5)
    if not mt5_client.initialize():
        print("Failed to initialize MT5")
        return
    
    try:
        # Create data provider
        data_provider = MT5DataProvider(mt5_client)
        
        # Initialize GPT client
        print("\n2. Initializing GPT client...")
        gpt_client = GPTClient(settings.openai)
        
        # Create simplified council for testing
        print("\n3. Creating Trading Council...")
        agents = [
            TechnicalAnalyst(gpt_client, settings.trading),
            RiskManager(gpt_client, settings.trading),
            HeadTrader(gpt_client, settings.trading)
        ]
        council = TradingCouncil(agents, settings.trading)
        
        # Create enhanced backtesting service
        print("\n4. Creating Enhanced Backtesting Service...")
        enhanced_service = EnhancedBacktestingService(
            data_provider=data_provider,
            gpt_client=gpt_client,
            council=council,
            ml_predictor=None,  # Skip ML for basic test
            backtest_config=settings.backtest
        )
        
        # Test parameters
        symbols = ['EURUSD']  # Single symbol for quick test
        end_date = datetime.now() - timedelta(days=5)
        start_date = end_date - timedelta(days=30)  # 30 days test
        
        print(f"\n5. Running enhanced backtest:")
        print(f"   Symbols: {symbols}")
        print(f"   Period: {start_date.date()} to {end_date.date()}")
        print(f"   Mode: simplified (for quick test)")
        
        # Run backtest
        result = await enhanced_service.run_enhanced_backtest(
            symbols=symbols,
            start_date=start_date,
            end_date=end_date,
            mode='simplified',
            walk_forward_periods=1,
            position_trading=False
        )
        
        # Print results
        print(f"\n6. Results:")
        print(f"   Total Trades: {result.total_trades}")
        print(f"   Win Rate: {result.win_rate:.1%}")
        print(f"   Sharpe Ratio: {result.sharpe_ratio:.2f}")
        print(f"   Total Cost: ${result.total_cost:.2f}")
        print(f"   Cost per Signal: ${result.cost_per_signal:.4f}")
        print(f"   Cost Savings: ${result.cost_savings:.2f}")
        
        # Test LLM simulator
        print(f"\n7. Testing LLM Simulator...")
        simulator = LLMSimulator()
        
        # Check if we have enough decisions to train
        decisions = await simulator.decision_recorder.get_all_decisions()
        print(f"   Available decisions: {len(decisions)}")
        
        if len(decisions) >= 100:  # Lower threshold for testing
            print("   Training simulator...")
            metrics = await simulator.train_from_decisions(min_decisions=100)
            print(f"   Simulator accuracy: {metrics.get('direction_accuracy', 0):.1%}")
        else:
            print("   Not enough decisions to train simulator")
        
        print("\n✅ Enhanced backtesting framework test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        mt5_client.shutdown()
        print("\nMT5 connection closed")


if __name__ == "__main__":
    asyncio.run(test_enhanced_backtest())