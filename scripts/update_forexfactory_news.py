#!/usr/bin/env python3
"""
ForexFactory News Updater
Downloads the latest ForexFactory calendar data and converts it to the format expected by the trading system.
"""

import json
import logging
import requests
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ForexFactoryUpdater:
    """Updates ForexFactory news data from the JSON API"""
    
    def __init__(self):
        self.data_dir = Path("data")
        self.data_dir.mkdir(exist_ok=True)
        
        # API endpoint
        self.ff_api_url = "https://nfs.faireconomy.media/ff_calendar_thisweek.json"
        
        # Output file
        self.output_file = self.data_dir / "forexfactory_week.json"
        
        # Request timeout
        self.timeout = 30
        
        # Impact level mapping (normalize to lowercase)
        self.impact_mapping = {
            'High': 'high',
            'Medium': 'medium',
            'Low': 'low',
            'high': 'high',
            'medium': 'medium', 
            'low': 'low',
            '': 'low'  # Default for empty impact
        }
        
        # Currency mapping
        self.currency_mapping = {
            'US': 'USD',
            'EU': 'EUR',
            'UK': 'GBP',
            'GB': 'GBP',
            'JP': 'JPY',
            'CA': 'CAD',
            'AU': 'AUD',
            'CH': 'CHF',
            'NZ': 'NZD',
            'CN': 'CNY'
        }
    
    def update_news_data(self) -> bool:
        """
        Download and update ForexFactory news data
        
        Returns:
            True if update successful
        """
        try:
            logger.info("🔄 Updating ForexFactory news data...")
            logger.info(f"📡 Fetching from: {self.ff_api_url}")
            
            # Download data
            raw_data = self._download_data()
            if not raw_data:
                logger.error("❌ Failed to download data")
                return False
            
            # Transform data
            transformed_data = self._transform_data(raw_data)
            
            # Save to file
            success = self._save_data(transformed_data)
            
            if success:
                logger.info("✅ ForexFactory news data updated successfully!")
                logger.info(f"📁 Saved to: {self.output_file}")
                logger.info(f"📊 Total events: {len(transformed_data)}")

                # Show summary of high impact events
                self._show_summary(transformed_data)
                
            return success
            
        except Exception as e:
            logger.error(f"❌ Update failed: {e}")
            return False
    
    def _download_data(self) -> Optional[List[Dict[str, Any]]]:
        """Download data from ForexFactory API"""
        try:
            logger.info("📥 Downloading ForexFactory data...")
            
            response = requests.get(
                self.ff_api_url,
                timeout=self.timeout,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ Downloaded {len(data) if isinstance(data, list) else 'unknown'} events")
                return data
            else:
                logger.error(f"❌ HTTP {response.status_code}: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            logger.error("❌ Request timed out")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Request failed: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"❌ Invalid JSON response: {e}")
            return None
    
    def _transform_data(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform raw API data to our format"""
        events = []

        for event in raw_data:
            try:
                transformed_event = self._transform_event(event)
                if transformed_event:
                    events.append(transformed_event)
            except Exception as e:
                logger.warning(f"⚠️  Failed to transform event: {e}")
                continue

        logger.info(f"🔄 Transformed {len(events)} events")
        return events
    
    def _transform_event(self, event: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Transform a single event"""
        try:
            # Get basic fields
            title = event.get('title', '').strip()
            if not title:
                return None
            
            # Parse date/time
            date_str = event.get('date', '')
            time_str = event.get('time', '')
            
            # Try to parse datetime
            event_datetime = self._parse_datetime(date_str, time_str)
            if not event_datetime:
                logger.warning(f"Could not parse datetime for event: {title}")
                return None
            
            # Get currency/country
            country = event.get('country', '').strip().upper()
            currency = self.currency_mapping.get(country, country)
            
            # Normalize impact
            impact = event.get('impact', '').strip()
            impact_level = self.impact_mapping.get(impact, 'low')
            
            # Create transformed event
            transformed = {
                'datetime': event_datetime.isoformat(),
                'date': event_datetime.strftime('%Y-%m-%d'),
                'time': event_datetime.strftime('%H:%M'),
                'currency': currency,
                'country': country,
                'event': title,
                'title': title,
                'impact': impact_level,
                'forecast': event.get('forecast', '').strip() or None,
                'previous': event.get('previous', '').strip() or None,
                'actual': event.get('actual', '').strip() or None,
                'url': event.get('url', '').strip() or None
            }
            
            return transformed
            
        except Exception as e:
            logger.warning(f"Failed to transform event {event.get('title', 'Unknown')}: {e}")
            return None
    
    def _parse_datetime(self, date_str: str, time_str: str) -> Optional[datetime]:
        """Parse date and time strings into datetime object"""
        try:
            if not date_str:
                return None
            
            # Try different date formats
            date_formats = [
                '%Y-%m-%d',
                '%m-%d-%Y', 
                '%d-%m-%Y',
                '%Y/%m/%d',
                '%m/%d/%Y',
                '%d/%m/%Y'
            ]
            
            parsed_date = None
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(date_str, fmt)
                    break
                except ValueError:
                    continue
            
            if not parsed_date:
                logger.warning(f"Could not parse date: {date_str}")
                return None
            
            # Parse time if provided
            hour, minute = 0, 0
            if time_str and time_str.lower() not in ['all day', 'tentative', '']:
                try:
                    # Handle formats like "9:20pm", "10:30am", "14:30"
                    time_str = time_str.lower().strip()
                    
                    if 'pm' in time_str:
                        time_part = time_str.replace('pm', '').strip()
                        if ':' in time_part:
                            hour, minute = map(int, time_part.split(':'))
                        else:
                            hour = int(time_part)
                        if hour != 12:
                            hour += 12
                    elif 'am' in time_str:
                        time_part = time_str.replace('am', '').strip()
                        if ':' in time_part:
                            hour, minute = map(int, time_part.split(':'))
                        else:
                            hour = int(time_part)
                        if hour == 12:
                            hour = 0
                    else:
                        # 24-hour format
                        if ':' in time_str:
                            hour, minute = map(int, time_str.split(':'))
                        else:
                            hour = int(time_str)
                            
                except (ValueError, IndexError):
                    logger.warning(f"Could not parse time: {time_str}")
            
            # Combine date and time
            event_datetime = parsed_date.replace(
                hour=hour,
                minute=minute,
                second=0,
                microsecond=0,
                tzinfo=timezone.utc
            )
            
            return event_datetime
            
        except Exception as e:
            logger.warning(f"DateTime parsing failed: {e}")
            return None
    
    def _save_data(self, data: List[Dict[str, Any]]) -> bool:
        """Save transformed data to JSON file"""
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            logger.info(f"💾 Data saved to {self.output_file}")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to save data: {e}")
            return False
    
    def _show_summary(self, events: List[Dict[str, Any]]):
        """Show summary of the updated data"""
        if not events:
            logger.info("📊 No events found")
            return

        # Count by impact
        impact_counts = {'high': 0, 'medium': 0, 'low': 0}
        for event in events:
            impact = event.get('impact', 'low')
            impact_counts[impact] = impact_counts.get(impact, 0) + 1

        logger.info(f"📊 Event Summary:")
        logger.info(f"   🔴 High Impact: {impact_counts['high']}")
        logger.info(f"   🟡 Medium Impact: {impact_counts['medium']}")
        logger.info(f"   🟢 Low Impact: {impact_counts['low']}")

        # Show next few high impact events
        high_impact_events = [e for e in events if e.get('impact') == 'high']
        if high_impact_events:
            logger.info("🔴 Upcoming High Impact Events:")
            for event in high_impact_events[:5]:
                logger.info(f"   • {event.get('datetime', 'Unknown time')}: {event.get('currency', 'XX')} - {event.get('title', 'Unknown event')}")

def main():
    """Main function"""
    logger.info("🚀 Starting ForexFactory news update...")
    
    updater = ForexFactoryUpdater()
    success = updater.update_news_data()
    
    if success:
        logger.info("🎉 Update completed successfully!")
    else:
        logger.error("💥 Update failed!")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
