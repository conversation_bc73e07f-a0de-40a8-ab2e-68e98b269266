#!/usr/bin/env python3
"""
Train GPT-Enhanced ML Models
Combines traditional technical analysis with GPT trading insights
"""

import sys
import logging
from pathlib import Path
from datetime import datetime
import argparse

# Add project root to path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from core.ml.gpt_enhanced_trainer import GPTEnhancedModelTrainer
from config.settings import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main training function"""
    parser = argparse.ArgumentParser(
        description="Train ML models enhanced with GPT insights"
    )
    
    parser.add_argument(
        "--symbols",
        nargs="+",
        help="Symbols to train (default: all configured symbols)"
    )
    
    parser.add_argument(
        "--lookback-days",
        type=int,
        default=365,
        help="Days of historical data to use (default: 365)"
    )
    
    parser.add_argument(
        "--min-trades",
        type=int,
        default=100,
        help="Minimum trades required for training (default: 100)"
    )
    
    parser.add_argument(
        "--output-dir",
        type=str,
        default="models/gpt_enhanced",
        help="Output directory for models (default: models/gpt_enhanced)"
    )
    
    args = parser.parse_args()
    
    # Print header
    print("="*80)
    print("GPT-ENHANCED ML MODEL TRAINING")
    print("="*80)
    print(f"\nTimestamp: {datetime.now()}")
    print(f"Lookback days: {args.lookback_days}")
    print(f"Minimum trades: {args.min_trades}")
    print(f"Output directory: {args.output_dir}")
    
    # Get symbols
    if args.symbols:
        symbols = args.symbols
    else:
        settings = get_settings()
        symbols = settings.trading.symbols
    
    print(f"Symbols to train: {', '.join(symbols)}")
    print("\n" + "="*80 + "\n")
    
    # Initialize trainer
    trainer = GPTEnhancedModelTrainer(
        models_dir=Path(args.output_dir),
        lookback_days=args.lookback_days,
        min_trades_required=args.min_trades
    )
    
    # Train models
    results = trainer.train_all_symbols(symbols)
    
    # Print summary
    print("\n" + "="*80)
    print("TRAINING SUMMARY")
    print("="*80 + "\n")
    
    for symbol, result in results.items():
        if result['success']:
            best_model = result.get('best_model', 'Unknown')
            best_score = result.get('best_score', 0)
            print(f"✓ {symbol}: SUCCESS - Best model: {best_model} (accuracy: {best_score:.2%})")
            
            # Print model performances
            if 'results' in result:
                for model_name, metrics in result['results'].items():
                    print(f"  - {model_name}: Test accuracy: {metrics['test_accuracy']:.2%}")
        else:
            reason = result.get('reason', 'Unknown error')
            print(f"✗ {symbol}: FAILED - {reason}")
    
    # Overall summary
    successful = sum(1 for r in results.values() if r['success'])
    total = len(results)
    
    print(f"\nOverall: {successful}/{total} symbols trained successfully")
    
    if successful > 0:
        print(f"\nModels saved to: {args.output_dir}")
        print("\nTo use these models:")
        print("1. Update ML_MODEL_DIR in .env to point to the new models")
        print("2. Set ML_ENABLED=true in .env")
        print("3. The system will automatically load GPT-enhanced models")
        print("\nKey improvements:")
        print("- Models now incorporate GPT trading insights")
        print("- Better understanding of market sentiment")
        print("- Improved pattern recognition from expert reasoning")
        print("- More nuanced risk assessment")
    
    return 0 if successful == total else 1


if __name__ == "__main__":
    sys.exit(main())