#!/usr/bin/env python3
"""
Offline Backtest Runner
Runs backtests using historical data without requiring MT5 connection
"""

import sys
import asyncio
import logging
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from typing import Dict, List, Optional
import argparse

# Add project root to path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from core.domain.models import MarketData, Candle, SignalType
from core.ml.ml_predictor import MLPredictor
from config.settings import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OfflineBacktester:
    """Runs backtests using simulated market data"""
    
    def __init__(self, use_enhanced_ml: bool = True):
        self.settings = get_settings()
        
        # Initialize ML predictor
        self.ml_predictor = MLPredictor()
            
        self.results = {
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'wait_signals': 0,
            'ml_confidence_avg': 0,
            'signals_by_symbol': {}
        }
    
    def generate_market_data(
        self, 
        symbol: str, 
        days: int = 7
    ) -> Dict[str, MarketData]:
        """Generate simulated market data for testing"""
        
        # Generate realistic OHLC data
        base_price = {
            'EURUSD': 1.0850,
            'GBPUSD': 1.2650,
            'USDJPY': 150.50,
            'GOLD': 2050.0,
            'XAUUSD': 2050.0,
            'OIL': 78.50,
            'US30': 38500
        }.get(symbol, 1.0)
        
        # Generate hourly candles
        candles = []
        current_time = datetime.now() - timedelta(days=days)
        
        for i in range(days * 24):  # Hourly candles
            # Add some realistic price movement
            volatility = 0.0005 if 'USD' in symbol else 0.001
            price_change = np.random.normal(0, volatility)
            
            open_price = base_price * (1 + price_change)
            high = open_price * (1 + abs(np.random.normal(0, volatility/2)))
            low = open_price * (1 - abs(np.random.normal(0, volatility/2)))
            close = open_price * (1 + np.random.normal(0, volatility))
            
            candle = Candle(
                time=current_time,
                open=open_price,
                high=high,
                low=low,
                close=close,
                volume=np.random.randint(100, 1000),
                timeframe='H1',
                symbol=symbol
            )
            
            candles.append(candle)
            current_time += timedelta(hours=1)
            base_price = close  # Next candle starts from previous close
        
        # Create MarketData object
        market_data = MarketData(
            symbol=symbol,
            timeframe='H1',
            candles=candles,
            current_price=candles[-1].close,
            spread=0.0001 if 'USD' in symbol else 0.5,
            timestamp=candles[-1].time
        )
        
        return {'h1': market_data}
    
    async def run_backtest(
        self, 
        symbols: List[str], 
        days: int = 7
    ) -> Dict:
        """Run offline backtest for specified symbols"""
        
        logger.info(f"Starting offline backtest for {len(symbols)} symbols over {days} days")
        
        for symbol in symbols:
            logger.info(f"\nProcessing {symbol}...")
            
            # Generate market data
            market_data = self.generate_market_data(symbol, days)
            
            # Get ML prediction if model exists
            if self.ml_predictor.has_model(symbol):
                try:
                    ml_result = await self.ml_predictor.get_ml_prediction(
                        symbol, 
                        market_data
                    )
                    
                    if ml_result['ml_enabled']:
                        self.results['total_signals'] += 1
                        
                        signal = ml_result['ml_signal']
                        confidence = ml_result['ml_confidence']
                        
                        # Count signal types
                        if signal == SignalType.BUY:
                            self.results['buy_signals'] += 1
                        elif signal == SignalType.SELL:
                            self.results['sell_signals'] += 1
                        else:
                            self.results['wait_signals'] += 1
                        
                        # Track by symbol
                        if symbol not in self.results['signals_by_symbol']:
                            self.results['signals_by_symbol'][symbol] = {
                                'total': 0,
                                'buy': 0,
                                'sell': 0,
                                'wait': 0,
                                'avg_confidence': 0
                            }
                        
                        sym_results = self.results['signals_by_symbol'][symbol]
                        sym_results['total'] += 1
                        
                        if signal == SignalType.BUY:
                            sym_results['buy'] += 1
                        elif signal == SignalType.SELL:
                            sym_results['sell'] += 1
                        else:
                            sym_results['wait'] += 1
                        
                        # Update average confidence
                        sym_results['avg_confidence'] = (
                            (sym_results['avg_confidence'] * (sym_results['total'] - 1) + confidence) 
                            / sym_results['total']
                        )
                        
                        logger.info(
                            f"  ML Signal: {signal.value} with {confidence:.1f}% confidence"
                        )
                        
                        # Log metadata
                        metadata = ml_result.get('ml_metadata', {})
                        if metadata.get('is_gpt_enhanced'):
                            logger.info("  Using GPT-enhanced model")
                            if 'top_gpt_features' in metadata:
                                logger.info("  Top GPT features:")
                                for feat, importance in metadata['top_gpt_features'].items():
                                    logger.info(f"    - {feat}: {importance:.4f}")
                    else:
                        logger.info(f"  No ML prediction: {ml_result.get('ml_metadata', {}).get('reason', 'Unknown')}")
                        
                except Exception as e:
                    logger.error(f"  ML prediction failed: {e}")
            else:
                logger.info(f"  No ML model available for {symbol}")
        
        # Calculate overall statistics
        if self.results['total_signals'] > 0:
            total_confidence = sum(
                s['avg_confidence'] * s['total'] 
                for s in self.results['signals_by_symbol'].values()
            )
            total_count = sum(
                s['total'] 
                for s in self.results['signals_by_symbol'].values()
            )
            self.results['ml_confidence_avg'] = total_confidence / total_count if total_count > 0 else 0
        
        return self.results
    
    def print_results(self):
        """Print backtest results"""
        print("\n" + "="*60)
        print("OFFLINE BACKTEST RESULTS")
        print("="*60)
        
        print(f"\nTotal ML signals generated: {self.results['total_signals']}")
        
        if self.results['total_signals'] > 0:
            print(f"  - BUY signals: {self.results['buy_signals']} ({self.results['buy_signals']/self.results['total_signals']*100:.1f}%)")
            print(f"  - SELL signals: {self.results['sell_signals']} ({self.results['sell_signals']/self.results['total_signals']*100:.1f}%)")
            print(f"  - WAIT signals: {self.results['wait_signals']} ({self.results['wait_signals']/self.results['total_signals']*100:.1f}%)")
            print(f"  - Average ML confidence: {self.results['ml_confidence_avg']:.1f}%")
            
            print("\nResults by Symbol:")
            for symbol, stats in self.results['signals_by_symbol'].items():
                if stats['total'] > 0:
                    print(f"\n  {symbol}:")
                    print(f"    - Total signals: {stats['total']}")
                    print(f"    - BUY: {stats['buy']} ({stats['buy']/stats['total']*100:.1f}%)")
                    print(f"    - SELL: {stats['sell']} ({stats['sell']/stats['total']*100:.1f}%)")
                    print(f"    - WAIT: {stats['wait']} ({stats['wait']/stats['total']*100:.1f}%)")
                    print(f"    - Avg confidence: {stats['avg_confidence']:.1f}%")
        else:
            print("\nNo ML signals were generated.")
            print("Possible reasons:")
            print("  - No ML models found in the models/ directory")
            print("  - ML models exist but couldn't generate predictions")
            print("  - ML confidence threshold too high")
            
        print("\n" + "="*60)


async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Run offline backtest without MT5 connection"
    )
    
    parser.add_argument(
        "--symbols",
        nargs="+",
        help="Symbols to backtest (default: from config)"
    )
    
    parser.add_argument(
        "--days",
        type=int,
        default=7,
        help="Number of days to simulate (default: 7)"
    )
    
    parser.add_argument(
        "--use-enhanced",
        action="store_true",
        default=True,
        help="Use GPT-enhanced ML models if available"
    )
    
    args = parser.parse_args()
    
    # Get symbols
    if args.symbols:
        symbols = args.symbols
    else:
        settings = get_settings()
        symbols = settings.trading.symbols
    
    print(f"Running offline backtest for: {', '.join(symbols)}")
    print(f"Simulation period: {args.days} days")
    print(f"Using enhanced ML: {args.use_enhanced}")
    
    # Run backtest
    backtester = OfflineBacktester(use_enhanced_ml=args.use_enhanced)
    await backtester.run_backtest(symbols, args.days)
    
    # Print results
    backtester.print_results()


if __name__ == "__main__":
    asyncio.run(main())