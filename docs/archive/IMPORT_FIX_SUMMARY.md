# Import Issues Fixed

## Problem
When running `python run_backtest.py --mode ml_only --days 7`, you encountered:
```
ImportError: cannot import name 'CouncilDecision' from partially initialized module 'core.agents.council' 
(most likely due to a circular import)
```

## Root Cause
- `council.py` defined `CouncilDecision` class
- `council_optimizer.py` imported `CouncilDecision` from `council.py`
- `council.py` imported `CouncilOptimizer` from `council_optimizer.py`
- This created a circular dependency

## Solution

### 1. Created `core/agents/council_types.py`
- Moved `CouncilDecision` dataclass to this new file
- This breaks the circular dependency

### 2. Updated Imports
- `council.py` → imports from `council_types.py`
- `council_optimizer.py` → imports from `council_types.py` and `base_agent.py`
- `council_signal_service.py` → updated to use new location
- `enhanced_council_signal_service.py` → updated to use new location

### 3. Fixed Additional Import Issues
- `enhanced_backtesting_service.py`:
  - Changed `Signal` → `TradingSignal`
  - Fixed `BacktestConfig` import
- `llm_simulator.py`:
  - Changed `Signal` → `TradingSignal`
  - Fixed signal creation with correct attributes
  - Added proper imports for `SignalType` and `RiskClass`

## Testing
Run the test script to verify all imports work:
```bash
python test_imports.py
```

## You Can Now Run
```bash
# This should work without import errors
python run_backtest.py --mode ml_only --days 7

# Other commands that should work
python trading_loop.py
python scripts/run_dashboard.py
python test_mt5_simple.py
```

The circular import issue is completely resolved!