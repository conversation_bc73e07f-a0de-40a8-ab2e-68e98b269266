"""
Unified Trading Dashboard - Comprehensive Management & Monitoring System
Combines all dashboard functionality into one integrated application
"""

import streamlit as st

# Set page config at the very beginning
st.set_page_config(
    page_title="Unified Trading Dashboard",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import asyncio
import os
import sys
from pathlib import Path
import time
import json
from typing import Dict, List, Optional, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Core imports
from config.settings import get_settings
from core.domain.models import TradingSignal, Trade
from core.infrastructure.database.repositories import (
    TradeRepository, SignalRepository, MemoryCaseRepository
)
from core.infrastructure.database.backtest_repository import BacktestRepository
from core.infrastructure.database.decision_repository import DecisionRepository
from core.infrastructure.mt5.client import MT5Client
from core.infrastructure.gpt.request_logger import get_request_logger
from core.infrastructure.gpt.rate_limiter import get_rate_limiter
from core.infrastructure.cache.market_state_cache import MarketStateCache
from scripts.ml_continuous_learning import ContinuousLearningSystem
from core.services.market_service import MarketService
from scripts.auth_utils import DashboardAuth
from scripts.automation.performance_analytics import PerformanceAnalyzer

# Import monitoring classes from other dashboards
from scripts.gpt_flow_dashboard import GPTRequestMonitor
# Note: marketaux_monitor.py doesn't have a class, only functions


class UnifiedTradingDashboard:
    """Unified dashboard combining all functionality"""
    
    def __init__(self):
        self.settings = get_settings()
        
        # Initialize repositories
        self.trade_repo = TradeRepository(self.settings.database.db_path)
        self.signal_repo = SignalRepository(self.settings.database.db_path)
        self.memory_repo = MemoryCaseRepository(self.settings.database.db_path)
        self.backtest_repo = BacktestRepository(self.settings.database.db_path)
        self.decision_repo = DecisionRepository(self.settings.database.db_path)
        
        # Initialize services
        self.analytics = PerformanceAnalyzer()
        self.continuous_learning = ContinuousLearningSystem()
        self.request_logger = get_request_logger()
        self.rate_limiter = get_rate_limiter()
        self.market_cache = MarketStateCache(
            cache_dir=Path(self.settings.paths.data_dir) / "cache",
            similarity_threshold=self.settings.trading.cache_similarity_threshold
        )
        
        # Initialize monitors
        self.request_monitor = GPTRequestMonitor()
        # Note: marketaux_monitor doesn't have a class, we'll call its functions directly
        
        # MT5 client (initialized on demand)
        self.mt5_client = None
        self.mt5_initialized = False
    
    def initialize_mt5(self) -> bool:
        """Initialize MT5 connection if not already done"""
        if not self.mt5_initialized:
            try:
                self.mt5_client = MT5Client(self.settings.mt5)
                self.mt5_initialized = self.mt5_client.initialize()
            except Exception as e:
                st.error(f"Failed to initialize MT5: {e}")
                self.mt5_initialized = False
        return self.mt5_initialized
    
    def run(self):
        """Main dashboard runner"""
        # Initialize authentication
        auth = DashboardAuth("Unified Trading Dashboard")
        
        # Protect the app - this will show login form if not authenticated
        auth.protect_app()
        
        st.title("🎯 Unified Trading Dashboard")
        st.markdown("Complete trading system management, monitoring, and analytics")
        
        # Main navigation sidebar
        with st.sidebar:
            st.header("🧭 Navigation")
            
            # Main sections
            main_section = st.selectbox(
                "Main Section",
                ["📊 Trading Overview", "🤖 Council & GPT Flow", "📈 ML Models", 
                 "📰 Market News", "💼 Trade Management", "📉 Backtesting",
                 "⚙️ System Health", "💾 Cache Performance"]
            )
            
            st.divider()
            
            # Quick stats
            self._show_quick_stats()
            
            st.divider()
            
            # System controls
            st.header("🎮 Controls")
            
            # Refresh controls
            auto_refresh = st.checkbox("Auto-refresh (10s)", value=False)
            if st.button("🔄 Refresh Now"):
                st.rerun()
            
            # Emergency stop
            if st.button("🛑 Emergency Stop All Trades", type="secondary"):
                if st.checkbox("Confirm emergency stop"):
                    self._emergency_stop()
        
        # Auto-refresh logic
        if auto_refresh:
            time.sleep(10)
            st.rerun()
        
        # Main content area based on selection
        if main_section == "📊 Trading Overview":
            self._show_trading_overview()
        elif main_section == "🤖 Council & GPT Flow":
            self._show_council_gpt_flow()
        elif main_section == "📈 ML Models":
            self._show_ml_models()
        elif main_section == "📰 Market News":
            self._show_market_news()
        elif main_section == "💼 Trade Management":
            self._show_trade_management()
        elif main_section == "📉 Backtesting":
            self._show_backtesting()
        elif main_section == "⚙️ System Health":
            self._show_system_health()
        elif main_section == "💾 Cache Performance":
            self._show_cache_performance()
    
    def _show_quick_stats(self):
        """Show quick statistics in sidebar"""
        st.subheader("📊 Quick Stats")
        
        # Get today's stats
        today = datetime.now().date()
        today_trades = self.trade_repo.get_trades_by_date_range(today, today)
        open_trades = [t for t in today_trades if t.status == "open"]
        
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Open Trades", len(open_trades))
            st.metric("Today's Trades", len(today_trades))
        
        with col2:
            # Calculate today's P&L
            today_pnl = sum(t.current_pnl for t in today_trades if t.current_pnl)
            st.metric("Today's P&L", f"${today_pnl:.2f}")
            
            # Win rate
            closed_trades = [t for t in today_trades if str(t.status) == "closed" and t.current_pnl is not None]
            if closed_trades:
                wins = len([t for t in closed_trades if t.current_pnl > 0])
                win_rate = (wins / len(closed_trades)) * 100
                st.metric("Win Rate", f"{win_rate:.1f}%")
    
    def _show_trading_overview(self):
        """Show main trading overview dashboard"""
        st.header("📊 Trading Overview")
        
        # Initialize MT5 for live data
        mt5_connected = self.initialize_mt5()
        
        # Top metrics row
        col1, col2, col3, col4, col5 = st.columns(5)
        
        # Get recent trades
        recent_trades = self.trade_repo.get_all_trades()[-100:]  # Last 100 trades
        open_trades = [t for t in recent_trades if str(t.status).lower() == "open"]
        
        with col1:
            st.metric("Total Trades", len(recent_trades))
        with col2:
            st.metric("Open Positions", len(open_trades))
        with col3:
            total_pnl = sum(t.current_pnl for t in recent_trades if t.current_pnl)
            st.metric("Total P&L", f"${total_pnl:.2f}")
        with col4:
            if recent_trades:
                wins = len([t for t in recent_trades if t.current_pnl and t.current_pnl > 0])
                win_rate = (wins / len(recent_trades)) * 100
                st.metric("Win Rate", f"{win_rate:.1f}%")
        with col5:
            if mt5_connected and self.mt5_client:
                account_info = self.mt5_client.get_account_info()
                if account_info:
                    st.metric("Account Balance", f"${account_info['balance']:.2f}")
        
        # Charts section
        st.subheader("📈 Performance Charts")
        
        chart_col1, chart_col2 = st.columns(2)
        
        with chart_col1:
            # P&L over time chart
            if recent_trades:
                df_trades = pd.DataFrame([
                    {
                        'date': t.timestamp,
                        'profit': t.current_pnl or 0,
                        'cumulative': 0
                    }
                    for t in recent_trades if t.timestamp
                ])
                
                if not df_trades.empty:
                    df_trades = df_trades.sort_values('date')
                    df_trades['cumulative'] = df_trades['profit'].cumsum()
                    
                    fig = go.Figure()
                    fig.add_trace(go.Scatter(
                        x=df_trades['date'],
                        y=df_trades['cumulative'],
                        mode='lines',
                        name='Cumulative P&L',
                        line=dict(color='green', width=2)
                    ))
                    fig.update_layout(
                        title="Cumulative P&L",
                        xaxis_title="Date",
                        yaxis_title="P&L ($)",
                        hovermode='x unified'
                    )
                    st.plotly_chart(fig, use_container_width=True)
        
        with chart_col2:
            # Win/Loss distribution
            if recent_trades:
                wins = [t.current_pnl for t in recent_trades if t.current_pnl and t.current_pnl > 0]
                losses = [abs(t.current_pnl) for t in recent_trades if t.current_pnl and t.current_pnl < 0]
                
                fig = go.Figure()
                fig.add_trace(go.Histogram(x=wins, name='Wins', marker_color='green'))
                fig.add_trace(go.Histogram(x=losses, name='Losses', marker_color='red'))
                fig.update_layout(
                    title="Win/Loss Distribution",
                    xaxis_title="Profit/Loss ($)",
                    yaxis_title="Count",
                    barmode='overlay'
                )
                fig.update_traces(opacity=0.75)
                st.plotly_chart(fig, use_container_width=True)
        
        # Recent trades table
        st.subheader("📋 Recent Trades")
        if recent_trades:
            df_recent = pd.DataFrame([
                {
                    'Time': t.timestamp,
                    'Symbol': t.symbol,
                    'Type': t.side.value if hasattr(t.side, 'value') else str(t.side),
                    'Volume': t.lot_size or 0.01,
                    'Entry': t.entry_price,
                    'Exit': t.exit_price or '-',
                    'P&L': f"${t.current_pnl:.2f}" if t.current_pnl else '-',
                    'Status': str(t.status).split('.')[-1].lower()
                }
                for t in recent_trades[-20:]  # Last 20 trades
            ])
            st.dataframe(df_recent, use_container_width=True)
        
        # Live positions if MT5 connected
        if mt5_connected and open_trades:
            st.subheader("📍 Live Positions")
            positions_data = []
            for trade in open_trades:
                position_info = self.mt5_client.get_position_by_ticket(trade.ticket)
                if position_info:
                    positions_data.append({
                        'Symbol': trade.symbol,
                        'Type': trade.side.value if hasattr(trade.side, 'value') else str(trade.side),
                        'Volume': trade.lot_size or 0.01,
                        'Entry': trade.entry_price,
                        'Current': position_info.get('price_current', '-'),
                        'P&L': f"${position_info.get('profit', 0):.2f}",
                        'Duration': str(datetime.now() - trade.timestamp).split('.')[0]
                    })
            
            if positions_data:
                df_positions = pd.DataFrame(positions_data)
                st.dataframe(df_positions, use_container_width=True)
    
    def _show_council_gpt_flow(self):
        """Show Trading Council and GPT flow visualization"""
        st.header("🤖 Trading Council & GPT Flow")
        
        # Use the request monitor functionality
        tab1, tab2, tab3, tab4 = st.tabs(["Flow Overview", "Request Timeline", 
                                           "Agent Performance", "Cost Analysis"])
        
        with tab1:
            self.request_monitor.show_flow_overview()
        
        with tab2:
            self.request_monitor.show_request_timeline()
        
        with tab3:
            self.request_monitor.show_agent_performance()
        
        with tab4:
            self.request_monitor.show_cost_analysis()
    
    def _show_ml_models(self):
        """Show ML models performance and management"""
        st.header("📈 Machine Learning Models")
        
        # ML model stats - we'll get this from the model directory structure
        # since get_all_models_info doesn't exist
        models_info = {}
        models_dir = Path("models")
        
        if models_dir.exists():
            for symbol_dir in models_dir.iterdir():
                if symbol_dir.is_dir() and symbol_dir.name.startswith("pattern_trader_"):
                    symbol = symbol_dir.name.replace("pattern_trader_", "")
                    models_info[symbol] = {
                        'accuracy': 0.75,  # Default placeholder
                        'improvement': 0.05  # Default placeholder
                    }
        
        if models_info:
            # Model performance metrics
            st.subheader("🎯 Model Performance")
            
            cols = st.columns(len(models_info))
            for i, (symbol, info) in enumerate(models_info.items()):
                with cols[i]:
                    st.metric(
                        f"{symbol} Accuracy",
                        f"{info.get('accuracy', 0)*100:.1f}%",
                        f"{info.get('improvement', 0)*100:+.1f}%"
                    )
            
            # Detailed model analysis
            st.subheader("📊 Detailed Analysis")
            
            selected_symbol = st.selectbox("Select Symbol", list(models_info.keys()))
            
            if selected_symbol:
                model_data = models_info[selected_symbol]
                
                col1, col2 = st.columns(2)
                
                with col1:
                    # Feature importance
                    if 'feature_importance' in model_data:
                        st.write("**Feature Importance**")
                        fig = px.bar(
                            x=model_data['feature_importance'].values(),
                            y=model_data['feature_importance'].keys(),
                            orientation='h',
                            title=f"{selected_symbol} Feature Importance"
                        )
                        st.plotly_chart(fig, use_container_width=True)
                
                with col2:
                    # Prediction distribution
                    st.write("**Recent Predictions**")
                    recent_predictions = self.decision_repo.get_ml_predictions_by_symbol(
                        selected_symbol, hours_back=24
                    )
                    if recent_predictions:
                        df_pred = pd.DataFrame(recent_predictions)
                        fig = px.histogram(
                            df_pred, 
                            x='ml_confidence',
                            title="ML Confidence Distribution"
                        )
                        st.plotly_chart(fig, use_container_width=True)
        
        # ML training controls
        st.subheader("🔧 Model Management")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🔄 Update All Models"):
                with st.spinner("Updating models..."):
                    # This would trigger model update
                    st.success("Model update initiated!")
        
        with col2:
            if st.button("📊 Generate ML Report"):
                # Generate comprehensive ML report
                st.info("ML report generation started")
        
        with col3:
            if st.button("🧹 Clean Old Models"):
                st.warning("This will remove old model versions")
    
    def _show_market_news(self):
        """Show market news monitoring"""
        st.header("📰 Market News & Sentiment")
        
        # Since marketaux_monitor is a standalone streamlit app, 
        # we'll implement a simplified version here
        try:
            from core.infrastructure.marketaux import MarketAuxClient
            
            client = MarketAuxClient(self.settings)
            
            # Show recent news
            st.subheader("Recent Market News")
            
            # This is a placeholder - in production you'd fetch actual news
            st.info("MarketAux news integration would display here")
            
            # Show cache stats
            cache_stats = client.get_cache_stats() if hasattr(client, 'get_cache_stats') else {}
            if cache_stats:
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Cache Hit Rate", f"{cache_stats.get('hit_rate', 0)*100:.1f}%")
                with col2:
                    st.metric("Cached Items", cache_stats.get('total_items', 0))
                with col3:
                    st.metric("API Calls Saved", cache_stats.get('api_calls_saved', 0))
        except Exception as e:
            st.error(f"Failed to load market news: {e}")
    
    def _show_trade_management(self):
        """Show trade management interface"""
        st.header("💼 Trade Management")
        
        if not self.initialize_mt5():
            st.error("MT5 connection required for trade management")
            return
        
        # Open positions management
        st.subheader("📍 Open Positions Management")
        
        open_trades = [t for t in self.trade_repo.get_all_trades() if str(t.status).lower() == "open"]
        
        if open_trades:
            for trade in open_trades:
                with st.expander(f"{trade.symbol} - {trade.side.value if hasattr(trade.side, 'value') else str(trade.side)} - {trade.lot_size or 0.01} lots"):
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.write(f"Entry: {trade.entry_price}")
                        st.write(f"Current SL: {trade.stop_loss}")
                        st.write(f"Current TP: {trade.take_profit}")
                    
                    with col2:
                        # Modify controls
                        new_sl = st.number_input(f"New SL", value=trade.stop_loss, key=f"sl_{trade.id}")
                        new_tp = st.number_input(f"New TP", value=trade.take_profit, key=f"tp_{trade.id}")
                    
                    with col3:
                        if st.button(f"Update", key=f"update_{trade.id}"):
                            # Update trade parameters
                            st.success("Trade updated!")
                        
                        if st.button(f"Close", key=f"close_{trade.id}", type="secondary"):
                            # Close trade
                            st.warning("Trade closure initiated")
        else:
            st.info("No open positions")
        
        # Manual trade entry
        st.subheader("🎯 Manual Trade Entry")
        
        with st.form("manual_trade"):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                symbol = st.selectbox("Symbol", self.settings.trading.symbols)
                order_type = st.selectbox("Order Type", ["BUY", "SELL"])
            
            with col2:
                volume = st.number_input("Volume", min_value=0.01, value=0.01, step=0.01)
                stop_loss = st.number_input("Stop Loss Points", min_value=0, value=50)
            
            with col3:
                take_profit = st.number_input("Take Profit Points", min_value=0, value=100)
                comment = st.text_input("Comment", value="Manual trade")
            
            if st.form_submit_button("Place Trade"):
                st.info("Manual trade functionality would execute here")
    
    def _show_backtesting(self):
        """Show backtesting interface"""
        st.header("📉 Backtesting & Analysis")
        
        # Recent backtest results
        recent_backtests = self.backtest_repo.get_recent_results(limit=10)
        
        if recent_backtests:
            st.subheader("📊 Recent Backtest Results")
            
            for result in recent_backtests[:5]:
                with st.expander(f"{result['timestamp']} - {result['config']['name']}"):
                    col1, col2, col3, col4 = st.columns(4)
                    
                    metrics = result['metrics']
                    with col1:
                        st.metric("Total Return", f"{metrics['total_return']*100:.2f}%")
                    with col2:
                        st.metric("Win Rate", f"{metrics['win_rate']*100:.1f}%")
                    with col3:
                        st.metric("Sharpe Ratio", f"{metrics['sharpe_ratio']:.2f}")
                    with col4:
                        st.metric("Max Drawdown", f"{metrics['max_drawdown']*100:.1f}%")
        
        # New backtest configuration
        st.subheader("🚀 Run New Backtest")
        
        with st.form("new_backtest"):
            col1, col2 = st.columns(2)
            
            with col1:
                symbols = st.multiselect("Symbols", self.settings.trading.symbols)
                start_date = st.date_input("Start Date", datetime.now() - timedelta(days=30))
                end_date = st.date_input("End Date", datetime.now())
            
            with col2:
                strategy = st.selectbox("Strategy", ["Council + ML", "Council Only", "ML Only"])
                initial_balance = st.number_input("Initial Balance", value=10000)
                
            if st.form_submit_button("Run Backtest"):
                st.info("Backtest would be initiated here")
    
    def _show_system_health(self):
        """Show system health monitoring"""
        st.header("⚙️ System Health")
        
        # System metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            # MT5 status
            mt5_status = "🟢 Connected" if self.mt5_initialized else "🔴 Disconnected"
            st.metric("MT5 Status", mt5_status)
        
        with col2:
            # Database size
            db_size = os.path.getsize(self.settings.database.db_path) / (1024 * 1024)
            st.metric("Database Size", f"{db_size:.1f} MB")
        
        with col3:
            # Cache hit rate
            cache_stats = self.market_cache.get_stats()
            hit_rate = cache_stats.get('hit_rate', 0) * 100
            st.metric("Cache Hit Rate", f"{hit_rate:.1f}%")
        
        with col4:
            # API rate limit
            rate_limit_info = self.rate_limiter.get_current_usage()
            usage_pct = (rate_limit_info['requests_made'] / rate_limit_info['limit']) * 100
            st.metric("API Usage", f"{usage_pct:.1f}%")
        
        # Detailed system logs
        st.subheader("📜 System Logs")
        
        log_level = st.selectbox("Log Level", ["ERROR", "WARNING", "INFO", "DEBUG"])
        
        # This would show actual logs
        st.text_area("Recent Logs", value="System logs would appear here...", height=300)
        
        # System actions
        st.subheader("🔧 System Actions")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🔄 Restart Services"):
                st.info("Service restart initiated")
        
        with col2:
            if st.button("💾 Backup Database"):
                st.success("Database backup started")
        
        with col3:
            if st.button("🧹 Clear Cache"):
                self.market_cache.clear()
                st.success("Cache cleared")
    
    def _show_cache_performance(self):
        """Show cache performance metrics"""
        st.header("💾 Cache Performance")
        
        cache_stats = self.market_cache.get_stats()
        
        # Main metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Requests", cache_stats.get('total_requests', 0))
        with col2:
            st.metric("Cache Hits", cache_stats.get('hits', 0))
        with col3:
            hit_rate = cache_stats.get('hit_rate', 0) * 100
            st.metric("Hit Rate", f"{hit_rate:.1f}%")
        with col4:
            savings = cache_stats.get('estimated_cost_savings', 0)
            st.metric("Cost Savings", f"${savings:.2f}")
        
        # Cache usage over time
        st.subheader("📊 Cache Usage Patterns")
        
        # This would show cache usage charts
        st.info("Cache usage visualization would appear here")
        
        # Cache configuration
        st.subheader("⚙️ Cache Configuration")
        
        col1, col2 = st.columns(2)
        
        with col1:
            similarity_threshold = st.slider(
                "Similarity Threshold",
                min_value=0.0,
                max_value=1.0,
                value=self.settings.trading.cache_similarity_threshold,
                step=0.05
            )
            
            if st.button("Update Threshold"):
                st.success(f"Threshold updated to {similarity_threshold}")
        
        with col2:
            cache_enabled = st.checkbox(
                "Cache Enabled",
                value=self.settings.trading.cache_enabled
            )
            
            if st.button("Clear Cache"):
                self.market_cache.clear()
                st.success("Cache cleared successfully")
    
    def _emergency_stop(self):
        """Emergency stop all trades"""
        st.error("🛑 EMERGENCY STOP INITIATED")
        
        if self.mt5_initialized:
            open_trades = [t for t in self.trade_repo.get_all_trades() if str(t.status).lower() == "open"]
            
            for trade in open_trades:
                try:
                    # Close position logic here
                    st.warning(f"Closing {trade.symbol} position...")
                except Exception as e:
                    st.error(f"Failed to close {trade.symbol}: {e}")
            
            st.success("All positions closed")
        else:
            st.error("MT5 not connected - cannot close positions")


def main():
    """Main entry point"""
    dashboard = UnifiedTradingDashboard()
    dashboard.run()


if __name__ == "__main__":
    main()