"""
Trading system interfaces for dependency injection and decoupling.
These interfaces define contracts that implementations must follow.
"""

from .signal_service_interface import ISignalService
from .data_provider_interface import IDataProvider
from .trade_service_interface import ITradeService
from .market_service_interface import IMarketService
from .news_service_interface import INewsService

__all__ = [
    'ISignalService',
    'IDataProvider',
    'ITradeService',
    'IMarketService',
    'INewsService'
]