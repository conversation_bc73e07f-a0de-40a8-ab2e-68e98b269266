# GPT Flow Dashboard Guide

## Overview
The GPT Flow Dashboard provides detailed insights into the Trading Council's decision-making process, showing exactly what data each agent receives and how they respond.

## How to Start the Dashboard

### Option 1: Using Python (Cross-platform)
```bash
cd D:\gpt_trader_v1
python scripts/run_gpt_flow_dashboard.py
```

### Option 2: Using Batch File (Windows)
Double-click or run:
```
scripts\start_gpt_flow_dashboard.bat
```

### Option 3: Direct Streamlit Command
```bash
cd D:\gpt_trader_v1
streamlit run scripts/gpt_flow_dashboard.py --server.port 8502
```

## Accessing the Dashboard
Once started, open your web browser and go to:
```
http://localhost:8502
```

## Dashboard Features

### 1. Request Payloads Tab
Shows detailed GPT request/response data:
- **System Prompts**: Each agent's role and instructions
- **User Input**: Full market data, indicators, news sent to agents
- **Agent Responses**: Complete analysis with signals and reasoning
- **Token Usage**: Detailed breakdown of API usage and costs

### 2. Agent Conversations Tab (NEW!)
Visual representation of council sessions:
- Council sessions grouped by time
- Side-by-side agent decisions
- Color-coded signals (🟢 BUY, 🔴 SELL, ⏸️ WAIT)
- Key market metrics that influenced decisions
- Response times and costs per agent

### 3. Other Tabs
- **Flow Overview**: System architecture and live flow
- **Agent Performance**: Success rates and metrics per agent
- **Token Usage**: API usage analytics
- **Council Decisions**: Historical decisions with full debates
- **Cost Analysis**: Financial breakdown of API costs

## What You'll See

### Example Agent Input:
```
Current Price: 1.2850
Spread: 0.00015
Volume: 125000
Trend: Bullish (EMA50 > EMA200)
RSI: 65.5
ATR: 0.0025
News: Fed minutes show hawkish tone...
```

### Example Agent Response:
```json
{
  "signal": "BUY",
  "confidence": 75,
  "analysis": "Strong bullish momentum confirmed by...",
  "key_points": [
    "Price above key moving averages",
    "RSI showing strength without overbought",
    "Positive news sentiment"
  ]
}
```

## Authentication
On first access, you'll need to create login credentials:
- Username: Choose any username
- Password: Choose a secure password

## Troubleshooting

### Dashboard Not Loading
1. Ensure trading system has been running (generates data)
2. Check if port 8502 is available
3. Verify virtual environment is activated

### No Data Showing
- The trading system must be running with `GPT_REQUEST_LOGGING=true`
- Check that `data/gpt_requests.db` exists
- Look for data in the last 24 hours (use time slider)

### Responses Showing as "..."
- This has been fixed! Restart the dashboard to see full responses
- The dashboard now shows:
  - Full agent responses up to 1000 characters
  - Button to show full response for longer content (avoids nested expander error)
  - Extracted key information (RECOMMENDATION, CONFIDENCE, etc.)
  - Analysis summaries shown inline instead of in nested expanders
  - If responses still show as "...", check the response_text field in the database

### Error Messages
- "No requests found": Trading system hasn't made GPT calls yet
- "Authentication required": Create credentials on first use
- "No response found in messages": Check if GPT logging is capturing responses
- Port conflicts: Change port in launch script

## Tips
1. Use "Agent Conversations" for quick overview of decisions
2. Use "Request Payloads" for detailed technical analysis
3. Filter by agent type to focus on specific roles
4. Adjust time range to see historical patterns