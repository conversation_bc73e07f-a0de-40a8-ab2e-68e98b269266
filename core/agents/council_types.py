"""
Shared data types for the Trading Council system.
This module prevents circular imports by centralizing shared type definitions.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import List, Dict, Any

from core.domain.models import TradingSignal
from core.agents.base_agent import AgentAnalysis, DebateResponse


@dataclass
class CouncilDecision:
    """Complete council decision package"""
    signal: TradingSignal
    agent_analyses: List[AgentAnalysis]
    debate_log: List[DebateResponse]
    llm_confidence: float
    ml_confidence: float
    final_confidence: float
    consensus_level: float
    dissenting_views: List[Dict[str, Any]]
    decision_rationale: str
    timestamp: datetime