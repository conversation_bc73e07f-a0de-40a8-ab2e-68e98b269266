"""
Enhanced Feature Engineering with GPT Insights
Combines traditional technical features with GPT-extracted insights
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
from sklearn.preprocessing import StandardScaler
from sklearn.base import BaseEstimator, TransformerMixin
import logging

from core.ml.feature_engineering import ProductionFeatureEngineer
from core.ml.gpt_insight_extractor import GPTInsightExtractor
from core.infrastructure.database.repositories import DecisionRepository
from core.infrastructure.database.connection_pool import get_connection

logger = logging.getLogger(__name__)


class GPTEnhancedFeatureEngineer(BaseEstimator, TransformerMixin):
    """
    Enhanced feature engineering that combines technical indicators with GPT insights
    """
    
    def __init__(
        self, 
        lookback_periods: List[int] = [5, 10, 20, 50, 100],
        include_gpt_features: bool = True,
        gpt_lookback_hours: int = 24
    ):
        self.base_engineer = ProductionFeatureEngineer(lookback_periods)
        self.gpt_extractor = GPTInsightExtractor()
        self.include_gpt_features = include_gpt_features
        self.gpt_lookback_hours = gpt_lookback_hours
        self.feature_names = []
        self.gpt_feature_cache = {}
        
    def fit(self, X, y=None):
        """Fit the feature engineer"""
        self.base_engineer.fit(X, y)
        return self
    
    def transform(self, df: pd.DataFrame, symbol: str = None) -> np.ndarray:
        """
        Transform raw OHLCV data into trading features including GPT insights
        
        Args:
            df: DataFrame with OHLCV data
            symbol: Trading symbol (needed for GPT feature lookup)
            
        Returns:
            Feature array
        """
        # Get base technical features
        base_features = self.base_engineer.transform(df)
        base_features_df = pd.DataFrame(
            base_features, 
            index=df.index,
            columns=self.base_engineer.get_feature_names()
        )
        
        if not self.include_gpt_features or symbol is None:
            self.feature_names = base_features_df.columns.tolist()
            return base_features_df.values
        
        # Add GPT features
        gpt_features_df = self._get_gpt_features(df.index, symbol)
        
        # Merge features
        if gpt_features_df is not None and not gpt_features_df.empty:
            # Align GPT features with price data
            aligned_gpt = gpt_features_df.reindex(df.index, method='ffill')
            
            # Combine features
            combined_features = pd.concat([base_features_df, aligned_gpt], axis=1)
            
            # Fill any remaining NaN values
            combined_features = combined_features.fillna(0)
        else:
            # No GPT features available, use zeros
            gpt_columns = self._get_gpt_column_names()
            zero_features = pd.DataFrame(
                0, 
                index=df.index, 
                columns=gpt_columns
            )
            combined_features = pd.concat([base_features_df, zero_features], axis=1)
        
        self.feature_names = combined_features.columns.tolist()
        return combined_features.values
    
    def _get_gpt_features(self, timestamps: pd.DatetimeIndex, symbol: str) -> Optional[pd.DataFrame]:
        """
        Retrieve GPT features for given timestamps
        """
        try:
            # Check cache first
            cache_key = f"{symbol}_{timestamps[0]}_{timestamps[-1]}"
            if cache_key in self.gpt_feature_cache:
                return self.gpt_feature_cache[cache_key]
            
            # Get decisions from database
            decisions = self._fetch_gpt_decisions(
                symbol, 
                timestamps[0], 
                timestamps[-1]
            )
            
            if not decisions:
                return None
            
            # Extract features
            feature_records = []
            for timestamp, decision_data in decisions:
                try:
                    # Reconstruct CouncilDecision from stored data
                    if 'council_decision' in decision_data:
                        features = self.gpt_extractor.extract_features_from_decision(
                            decision_data['council_decision']
                        )
                        features['timestamp'] = timestamp
                        feature_records.append(features)
                except Exception as e:
                    logger.warning(f"Failed to extract GPT features: {e}")
                    continue
            
            if not feature_records:
                return None
            
            # Convert to DataFrame
            gpt_df = pd.DataFrame(feature_records)
            gpt_df.set_index('timestamp', inplace=True)
            
            # Remove non-feature columns
            feature_cols = [col for col in gpt_df.columns if col.startswith('gpt_')]
            gpt_df = gpt_df[feature_cols]
            
            # Cache the result
            self.gpt_feature_cache[cache_key] = gpt_df
            
            return gpt_df
            
        except Exception as e:
            logger.error(f"Error retrieving GPT features: {e}")
            return None
    
    def _fetch_gpt_decisions(
        self, 
        symbol: str, 
        start_time: datetime, 
        end_time: datetime
    ) -> List[Tuple[datetime, Dict[str, Any]]]:
        """
        Fetch GPT decisions from database
        """
        try:
            with get_connection() as conn:
                repo = DecisionRepository(conn)
                
                # Get decisions within timeframe
                decisions = repo.get_decisions_by_timeframe(
                    symbol=symbol,
                    start_time=start_time,
                    end_time=end_time
                )
                
                return [(d.timestamp, d.decision_data) for d in decisions]
                
        except Exception as e:
            logger.error(f"Failed to fetch GPT decisions: {e}")
            return []
    
    def _get_gpt_column_names(self) -> List[str]:
        """Get standard GPT feature column names"""
        return [
            'gpt_final_confidence',
            'gpt_llm_confidence',
            'gpt_consensus_level',
            'gpt_signal_buy',
            'gpt_signal_sell',
            'gpt_signal_wait',
            'gpt_risk_low',
            'gpt_risk_medium',
            'gpt_risk_high',
            'gpt_agent_buy_ratio',
            'gpt_agent_sell_ratio',
            'gpt_agent_wait_ratio',
            'gpt_technical_analyst_confidence',
            'gpt_fundamental_analyst_confidence',
            'gpt_sentiment_reader_confidence',
            'gpt_risk_manager_confidence',
            'gpt_momentum_trader_confidence',
            'gpt_contrarian_trader_confidence',
            'gpt_agents_sentiment',
            'gpt_agents_reversal_score',
            'gpt_agents_continuation_score',
            'gpt_agents_breakout_score',
            'gpt_agents_support_resistance_score',
            'gpt_agents_risk_score',
            'gpt_debate_sentiment_mean',
            'gpt_debate_sentiment_std',
            'gpt_debate_sentiment_trend',
            'gpt_rationale_sentiment',
            'gpt_rationale_risk_score'
        ]
    
    def get_feature_names(self) -> List[str]:
        """Get all feature names"""
        if not self.feature_names:
            # Generate feature names
            base_names = self.base_engineer.get_feature_names()
            if self.include_gpt_features:
                gpt_names = self._get_gpt_column_names()
                self.feature_names = base_names + gpt_names
            else:
                self.feature_names = base_names
        
        return self.feature_names