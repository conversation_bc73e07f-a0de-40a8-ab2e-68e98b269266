"""
News service interface for market news and sentiment analysis.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
from datetime import datetime


class INewsService(ABC):
    """Interface for news and sentiment services"""
    
    @abstractmethod
    async def get_latest_news(
        self,
        symbols: Optional[List[str]] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get latest news articles.
        
        Args:
            symbols: Optional list of symbols to filter by
            limit: Maximum number of articles
            
        Returns:
            List of news articles with metadata
        """
        pass
    
    @abstractmethod
    async def get_market_sentiment(
        self,
        symbol: str
    ) -> Dict[str, Any]:
        """
        Get market sentiment for a symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Dictionary with sentiment metrics
        """
        pass
    
    @abstractmethod
    async def get_high_impact_events(
        self,
        hours_ahead: int = 24
    ) -> List[Dict[str, Any]]:
        """
        Get upcoming high-impact events.
        
        Args:
            hours_ahead: Hours to look ahead
            
        Returns:
            List of high-impact events
        """
        pass
    
    @abstractmethod
    async def analyze_news_impact(
        self,
        symbol: str,
        time_window: int = 4
    ) -> Dict[str, Any]:
        """
        Analyze news impact on a symbol.
        
        Args:
            symbol: Trading symbol
            time_window: Hours to analyze
            
        Returns:
            Analysis of news impact
        """
        pass
    
    @abstractmethod
    async def get_sentiment_history(
        self,
        symbol: str,
        days: int = 7
    ) -> List[Dict[str, Any]]:
        """
        Get historical sentiment data.
        
        Args:
            symbol: Trading symbol
            days: Number of days of history
            
        Returns:
            List of historical sentiment data points
        """
        pass
    
    @abstractmethod
    async def is_high_risk_period(
        self,
        symbol: str
    ) -> tuple[bool, str]:
        """
        Check if current time is high risk due to news.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Tuple of (is_high_risk, reason)
        """
        pass