# Launcher Consolidation Guide

## New Unified Launcher

The new `launcher.bat` file consolidates all launcher functionality into a single, menu-driven interface. It provides:

- **Trading System**: Start the main trading loop
- **Dashboard Options**: All dashboard types (unified, simple, comprehensive, with GPT flow)
- **ML Monitor**: Performance monitoring and reporting
- **Production Services**: Start all essential services at once
- **Utilities**: Scheduled tasks, ML training, backtesting, MT5 tools, and backups

## Usage

Simply run:
```batch
launcher.bat
```

Then select options from the interactive menu.

## Old .bat Files That Can Be Removed

After verifying that `launcher.bat` works correctly, you can safely remove these redundant files:

### Root Directory
- `run_trading.bat` - Replaced by option 1 in main menu
- `start_trading.bat` - Replaced by option 1 in main menu
- `start_unified_dashboard.bat` - Replaced by option 2.1 in dashboard menu
- `start_unified_simple.bat` - Replaced by option 2.2 in dashboard menu

### Scripts Directory
- `scripts/start_dashboard.bat` - Replaced by option 2.3 in dashboard menu
- `scripts/start_simple_dashboard.bat` - Replaced by option 2.4 in dashboard menu
- `scripts/start_dashboard_with_gpt_flow.bat` - Replaced by option 2.5 in dashboard menu
- `scripts/start_unified_dashboard.bat` - Replaced by option 2.1 in dashboard menu
- `scripts/start_ml_monitor.bat` - Replaced by option 3 in main menu
- `scripts/start_all_services.bat` - Replaced by option 4 in main menu
- `scripts/start_production.bat` - Replaced by option 4 in main menu
- `scripts/create_scheduled_tasks.bat` - Replaced by option 5.1 in utilities menu

## Files to Keep

These files should NOT be removed:
- `venv/Scripts/activate.bat` - Required by Python virtual environment
- `venv/Scripts/deactivate.bat` - Required by Python virtual environment

## Benefits of Consolidation

1. **Single Entry Point**: No need to remember multiple launcher names
2. **Consistent Environment Setup**: All launchers use the same environment detection and setup
3. **Error Handling**: Unified error checking and reporting
4. **Easier Maintenance**: One file to update instead of 12+
5. **Better Organization**: Menu-based navigation is more intuitive
6. **Flexibility**: Easy to add new options without creating new files

## Migration Steps

1. Test `launcher.bat` thoroughly with all menu options
2. Verify each function works as expected
3. Create a backup of old .bat files (optional)
4. Delete the redundant .bat files listed above
5. Update any documentation or shortcuts to use `launcher.bat`

## Quick Reference

| Old Command | New Launcher Option |
|-------------|-------------------|
| `run_trading.bat` | Main Menu → 1 |
| `start_trading.bat` | Main Menu → 1 |
| `start_unified_dashboard.bat` | Main Menu → 2 → 1 |
| `start_unified_simple.bat` | Main Menu → 2 → 2 |
| `scripts/start_dashboard.bat` | Main Menu → 2 → 3 |
| `scripts/start_simple_dashboard.bat` | Main Menu → 2 → 4 |
| `scripts/start_dashboard_with_gpt_flow.bat` | Main Menu → 2 → 5 |
| `scripts/start_ml_monitor.bat` | Main Menu → 3 |
| `scripts/start_all_services.bat` | Main Menu → 4 |
| `scripts/start_production.bat` | Main Menu → 4 |
| `scripts/create_scheduled_tasks.bat` | Main Menu → 5 → 1 |