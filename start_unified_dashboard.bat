@echo off
echo Starting Unified Trading Dashboard...
echo =====================================

REM Show current directory
echo Working directory: %CD%

REM Activate virtual environment
if exist venv\Scripts\activate (
    call venv\Scripts\activate
) else (
    echo WARNING: Virtual environment not found, using system Python
)

REM Set Python path
set PYTHONPATH=%CD%

REM Start the unified dashboard
python scripts\run_unified_dashboard.py

pause