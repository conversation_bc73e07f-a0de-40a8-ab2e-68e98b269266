"""
GPT-Enhanced ML Model Trainer
Trains ML models using both technical indicators and GPT insights
"""

import logging
import pickle
import json
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler
import joblib

from core.ml.enhanced_feature_engineering import GPTEnhancedFeatureEngineer
from core.ml.model_trainer import evaluate_model
from core.infrastructure.database.repositories import TradeRepository, DecisionRepository
from core.infrastructure.database.connection_pool import get_connection
from core.domain.models import SignalType
from config.settings import get_settings

logger = logging.getLogger(__name__)


class GPTEnhancedModelTrainer:
    """
    Trains ML models enhanced with GPT insights
    """
    
    def __init__(
        self,
        models_dir: Path = Path("models/gpt_enhanced"),
        lookback_days: int = 365,
        min_trades_required: int = 100
    ):
        self.models_dir = models_dir
        self.lookback_days = lookback_days
        self.min_trades_required = min_trades_required
        
        # Create models directory
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # Model configurations
        self.model_configs = {
            'random_forest': {
                'model': RandomForestClassifier(
                    n_estimators=200,
                    max_depth=15,
                    min_samples_split=20,
                    min_samples_leaf=10,
                    class_weight='balanced',
                    random_state=42,
                    n_jobs=-1
                ),
                'description': 'Random Forest with GPT insights'
            },
            'gradient_boosting': {
                'model': GradientBoostingClassifier(
                    n_estimators=200,
                    learning_rate=0.05,
                    max_depth=5,
                    min_samples_split=20,
                    min_samples_leaf=10,
                    random_state=42
                ),
                'description': 'Gradient Boosting with GPT insights'
            }
        }
    
    def train_models_for_symbol(self, symbol: str) -> Dict[str, Any]:
        """
        Train GPT-enhanced models for a specific symbol
        """
        logger.info(f"Training GPT-enhanced models for {symbol}")
        
        try:
            # Load historical data with GPT decisions
            data = self._load_training_data(symbol)
            
            if data is None or len(data) < self.min_trades_required:
                logger.warning(
                    f"Insufficient data for {symbol}: "
                    f"{len(data) if data is not None else 0} samples"
                )
                return {'success': False, 'reason': 'Insufficient data'}
            
            # Prepare features and labels
            X, y, feature_names = self._prepare_features(data, symbol)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            logger.info(
                f"Training data shape: {X_train.shape}, "
                f"Test data shape: {X_test.shape}"
            )
            
            # Train models
            results = {}
            best_model = None
            best_score = 0
            
            for model_name, config in self.model_configs.items():
                logger.info(f"Training {model_name}...")
                
                # Create pipeline
                pipeline = Pipeline([
                    ('scaler', StandardScaler()),
                    ('model', config['model'])
                ])
                
                # Train
                pipeline.fit(X_train, y_train)
                
                # Evaluate
                train_score = pipeline.score(X_train, y_train)
                test_score = pipeline.score(X_test, y_test)
                
                # Cross-validation
                cv_scores = cross_val_score(
                    pipeline, X_train, y_train, cv=5, scoring='f1_weighted'
                )
                
                # Predictions for detailed metrics
                y_pred = pipeline.predict(X_test)
                
                # Store results
                results[model_name] = {
                    'train_accuracy': train_score,
                    'test_accuracy': test_score,
                    'cv_score_mean': cv_scores.mean(),
                    'cv_score_std': cv_scores.std(),
                    'classification_report': classification_report(
                        y_test, y_pred, output_dict=True
                    ),
                    'confusion_matrix': confusion_matrix(y_test, y_pred).tolist()
                }
                
                # Track best model
                if test_score > best_score:
                    best_score = test_score
                    best_model = (model_name, pipeline)
                
                logger.info(
                    f"{model_name} - Train: {train_score:.3f}, "
                    f"Test: {test_score:.3f}, CV: {cv_scores.mean():.3f}"
                )
            
            # Analyze feature importance
            if best_model:
                model_name, pipeline = best_model
                feature_importance = self._get_feature_importance(
                    pipeline.named_steps['model'], 
                    feature_names
                )
                
                # Save the best model
                model_package = {
                    'pipeline': pipeline,
                    'model_type': model_name,
                    'feature_names': feature_names,
                    'feature_importance': feature_importance,
                    'training_date': datetime.now().isoformat(),
                    'training_samples': len(X_train),
                    'test_samples': len(X_test),
                    'performance': results[model_name],
                    'includes_gpt_insights': True,
                    'gpt_features': [f for f in feature_names if f.startswith('gpt_')]
                }
                
                # Save model
                model_path = self.models_dir / f"{symbol}_gpt_enhanced_{model_name}.pkl"
                joblib.dump(model_package, model_path)
                
                logger.info(f"Saved GPT-enhanced model to {model_path}")
                
                # Log GPT feature importance
                gpt_importance = {
                    k: v for k, v in feature_importance.items() 
                    if k.startswith('gpt_')
                }
                
                if gpt_importance:
                    logger.info("Top GPT features:")
                    for feat, imp in sorted(
                        gpt_importance.items(), 
                        key=lambda x: x[1], 
                        reverse=True
                    )[:10]:
                        logger.info(f"  {feat}: {imp:.4f}")
            
            return {
                'success': True,
                'results': results,
                'best_model': model_name if best_model else None,
                'best_score': best_score
            }
            
        except Exception as e:
            logger.error(f"Failed to train models for {symbol}: {e}")
            return {'success': False, 'reason': str(e)}
    
    def _load_training_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        Load historical trades with GPT decisions
        """
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.lookback_days)
            
            with get_connection() as conn:
                trade_repo = TradeRepository(conn)
                decision_repo = DecisionRepository(conn)
                
                # Get completed trades
                trades = trade_repo.get_trades_by_symbol(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    status='closed'
                )
                
                if not trades:
                    return None
                
                # Build dataset
                data_records = []
                
                for trade in trades:
                    # Get the decision that led to this trade
                    decision = decision_repo.get_decision_by_trade_id(trade.id)
                    
                    if decision and decision.decision_data.get('council_decision'):
                        # Extract trade outcome
                        profit_pct = (trade.exit_price - trade.entry_price) / trade.entry_price
                        if trade.direction == 'SELL':
                            profit_pct = -profit_pct
                        
                        # Determine label (profitable or not)
                        label = 1 if profit_pct > 0.001 else 0  # 0.1% threshold
                        
                        record = {
                            'timestamp': trade.entry_time,
                            'symbol': symbol,
                            'decision_data': decision.decision_data,
                            'profit_pct': profit_pct * 100,
                            'label': label,
                            'trade_duration_hours': (
                                trade.exit_time - trade.entry_time
                            ).total_seconds() / 3600
                        }
                        
                        data_records.append(record)
                
                if not data_records:
                    return None
                
                df = pd.DataFrame(data_records)
                logger.info(
                    f"Loaded {len(df)} trades with GPT decisions for {symbol}. "
                    f"Win rate: {df['label'].mean():.2%}"
                )
                
                return df
                
        except Exception as e:
            logger.error(f"Failed to load training data: {e}")
            return None
    
    def _prepare_features(
        self, 
        data: pd.DataFrame, 
        symbol: str
    ) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        Prepare features and labels for training
        """
        # Initialize feature engineer
        engineer = GPTEnhancedFeatureEngineer(include_gpt_features=True)
        
        # Extract features from each decision
        all_features = []
        labels = []
        
        for _, row in data.iterrows():
            try:
                # Extract GPT features
                from core.ml.gpt_insight_extractor import GPTInsightExtractor
                extractor = GPTInsightExtractor()
                
                council_decision = row['decision_data']['council_decision']
                gpt_features = extractor.extract_features_from_decision(
                    council_decision
                )
                
                # Convert to feature vector
                feature_vector = [gpt_features.get(f, 0) for f in engineer._get_gpt_column_names()]
                all_features.append(feature_vector)
                labels.append(row['label'])
                
            except Exception as e:
                logger.warning(f"Failed to extract features from row: {e}")
                continue
        
        X = np.array(all_features)
        y = np.array(labels)
        feature_names = engineer._get_gpt_column_names()
        
        return X, y, feature_names
    
    def _get_feature_importance(
        self, 
        model: Any, 
        feature_names: List[str]
    ) -> Dict[str, float]:
        """
        Extract feature importance from trained model
        """
        if hasattr(model, 'feature_importances_'):
            importances = model.feature_importances_
            return {
                name: float(imp) 
                for name, imp in zip(feature_names, importances)
            }
        return {}
    
    def train_all_symbols(self, symbols: Optional[List[str]] = None):
        """
        Train GPT-enhanced models for all configured symbols
        """
        if symbols is None:
            settings = get_settings()
            symbols = settings.trading.symbols
        
        results = {}
        
        for symbol in symbols:
            logger.info(f"\n{'='*60}")
            logger.info(f"Training GPT-enhanced models for {symbol}")
            logger.info(f"{'='*60}")
            
            result = self.train_models_for_symbol(symbol)
            results[symbol] = result
            
            if result['success']:
                logger.info(f"✓ Successfully trained models for {symbol}")
            else:
                logger.warning(f"✗ Failed to train models for {symbol}: {result.get('reason')}")
        
        # Summary
        successful = sum(1 for r in results.values() if r['success'])
        logger.info(f"\n{'='*60}")
        logger.info(f"Training complete: {successful}/{len(results)} symbols successful")
        logger.info(f"{'='*60}")
        
        return results