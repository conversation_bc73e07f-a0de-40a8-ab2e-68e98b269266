# GPT Trading System - Quick Reference Card

## 🚀 Essential Commands

### Starting Trading
```bash
# Scalping mode (default)
python trading_loop.py

# Position trading mode
python trading_loop.py --position-trading

# Specific symbols
python trading_loop.py --symbols EURUSD,GBPUSD,XAUUSD
```

### Backtesting
```bash
# Quick test (ML-only, fast)
python run_backtest.py --mode ml_only --days 30

# Full comparison
python run_backtest.py --compare-modes

# Optimize parameters
python run_backtest.py --optimize
```

### Monitoring
```bash
# Trading dashboard
python scripts/run_dashboard.py

# Check costs
python check_gpt_requests.py

# Position monitor
python scripts/position_monitor_dashboard.py
```

---

## ⚙️ Key Configuration Files

| File | Purpose | Key Parameters |
|------|---------|----------------|
| `.env` | Credentials & runtime | API keys, risk settings, symbols |
| `config/settings.py` | Trading parameters | Timeframes, thresholds, risk |
| `config/position_trading_config.py` | Position mode | Stop/target multipliers, timing |
| `config/symbols.py` | Trading instruments | Symbol groups and lists |
| `config/backtest_config.py` | Backtest settings | Model tiers, costs |

---

## 📊 Trading Modes

### Scalping (Default)
- **Timeframes**: H1/H4
- **Hold time**: 4-8 hours
- **Stop loss**: 1-2x ATR
- **R:R**: 1.5-2:1

### Position Trading
- **Timeframes**: D1/W1
- **Hold time**: 5-20 days
- **Stop loss**: 3-5x ATR
- **R:R**: 3-5:1

---

## 🎯 Common .env Settings

```env
# Minimum required
OPENAI_API_KEY=sk-...
MT5_FILES_DIR=C:/path/to/MQL5/Files

# Risk management
RISK_PER_TRADE=1.0              # 1% risk per trade
MAX_CONCURRENT_TRADES=3         # Max open positions
PORTFOLIO_HEAT_LIMIT=6.0        # Max total risk

# Performance
COUNCIL_QUICK_MODE=true         # Faster decisions
CACHE_ENABLED=true              # Reduce API calls
ML_ENABLED=true                 # Use ML predictions

# Position trading
POSITION_TRADING_MODE=true      # Enable longer timeframes
TRADING_TIMEFRAMES=["D1","W1"]  # Daily/Weekly
```

---

## 💰 Cost Optimization

| Mode | Cost/Signal | Speed | Use Case |
|------|------------|-------|----------|
| Production | ~$0.50 | 60s | Live trading |
| Full Council | ~$0.02 | 30s | Detailed backtest |
| Simplified | ~$0.008 | 10s | Quick backtest |
| ML Only | ~$0.0002 | 0.1s | Rapid iteration |

---

## 🔧 Troubleshooting

```bash
# Test MT5 connection
python test_mt5_simple.py

# Find symbols
python test_symbol_finder.py

# Check API costs
python check_gpt_requests.py

# Database backup
python scripts/automation/database_backup.py

# System health
python scripts/automation/health_check.py
```

---

## 📈 Symbol Groups

```python
# In .env
SYMBOL_GROUPS=["conservative_forex"]     # Low risk
SYMBOL_GROUPS=["moderate_commodities"]   # Medium risk
SYMBOL_GROUPS=["aggressive_indices"]     # High risk
SYMBOL_GROUPS=["ftmo_recommended"]       # FTMO optimized
```

---

## 🎮 Workflow Examples

### 1. Test New Strategy
```bash
# Step 1: Quick ML test
python run_backtest.py --mode ml_only --days 7

# Step 2: If good, simplified test
python run_backtest.py --mode simplified --days 30

# Step 3: Full validation
python run_backtest.py --mode full_council --days 90
```

### 2. Daily Operations
```bash
# Morning: Check health
python scripts/automation/health_check.py

# Start trading
python trading_loop.py

# Monitor throughout day
python scripts/run_dashboard.py
```

### 3. Weekly Maintenance
```bash
# Backup database
python scripts/automation/database_backup.py

# Update ML models
python scripts/train_ml_production.py

# Performance review
python scripts/performance_analytics.py --days 7
```

---

## 🚨 Important Notes

1. **Always test first**: Use backtesting before live trading
2. **Start small**: Begin with conservative settings
3. **Monitor costs**: Check API usage regularly
4. **Position mode**: Recommended for cost efficiency
5. **Diversify**: Trade multiple uncorrelated pairs

---

## 📚 More Information

- Full guide: `COMPREHENSIVE_USAGE_GUIDE.md`
- ML details: `ML_USAGE_GUIDE.md`
- Position trading: `docs/position_trading_guide.md`
- Recent updates: `CLAUDE.md`