# Source Requirements File for pip-tools
# =====================================
# This is the source file for generating locked requirements using pip-tools.
# It specifies version constraints more precisely than requirements.txt
#
# Usage:
# - To generate requirements.txt: pip-compile requirements.in
# - To install exact versions: pip-sync requirements.txt
# - To update all packages: pip-compile --upgrade requirements.in
#
# Note: This file uses stricter version constraints to ensure compatibility

# Core dependencies
pydantic>=2.0.0,<3.0.0
pydantic-settings>=2.0.0
python-dotenv~=1.0.0
MetaTrader5~=5.0.45
openai>=1.0.0,<2.0.0
tiktoken>=0.5.0,<1.0.0

# Data processing
pandas>=2.0.0,<3.0.0
numpy>=1.24.0,<2.0.0

# Machine learning
scikit-learn~=1.3.2
xgboost>=2.0.0,<3.0.0
joblib~=1.3.2
imbalanced-learn~=0.11.0

# Deep learning (for memory service)
torch>=2.1.0,<3.0.0
sentence-transformers>=2.2.0,<3.0.0
faiss-cpu~=1.7.4

# Visualization and dashboards
matplotlib>=3.7.0,<4.0.0
seaborn~=0.13.0
plotly>=5.18.0,<6.0.0
streamlit>=1.29.0,<2.0.0
mplfinance>=0.12.9b7
tabulate~=0.9.0

# Technical analysis
ta~=0.10.2

# Async and networking
aiohttp>=3.9.0  # Security update
requests>=2.31.0  # Security update
backoff~=2.2.1
nest-asyncio~=1.5.8

# Database
aiosqlite>=0.19.0,<1.0.0

# Utilities
schedule~=1.2.0
scipy~=1.11.4
psutil>=5.9.0,<6.0.0
h5py~=3.10.0