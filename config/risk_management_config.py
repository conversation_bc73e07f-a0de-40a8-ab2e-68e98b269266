"""
Strict Risk Management Configuration
Enforces maximum drawdown limits for safe trading
"""

from dataclasses import dataclass
from typing import Dict, Optional
import logging

logger = logging.getLogger(__name__)


@dataclass
class RiskLimits:
    """Strict risk limits for trading"""
    
    # Per-trade limits
    max_risk_per_trade_percent: float = 1.0  # Maximum 1% risk per trade
    max_loss_per_trade_percent: float = 5.0  # Circuit breaker at 5% loss per trade
    
    # Portfolio limits
    max_daily_drawdown_percent: float = 5.0  # Maximum 5% daily drawdown
    max_total_drawdown_percent: float = 10.0  # Maximum 10% total drawdown
    
    # Position sizing limits
    max_position_size_percent: float = 20.0  # Maximum 20% of capital in one position
    max_correlated_exposure_percent: float = 30.0  # Maximum 30% in correlated pairs
    
    # Trade frequency limits
    max_trades_per_day: int = 5  # Maximum trades per day
    max_trades_per_symbol_day: int = 2  # Maximum trades per symbol per day
    max_consecutive_losses: int = 3  # Stop trading after 3 consecutive losses
    
    # Confidence requirements (increased for safety)
    min_confidence_to_trade: float = 85.0  # Minimum 85% confidence
    min_risk_reward_ratio: float = 2.0  # Minimum 2:1 R:R ratio
    
    # Emergency stops
    emergency_stop_loss_percent: float = 3.0  # Emergency stop at 3% loss
    trailing_stop_activation_percent: float = 2.0  # Activate trailing stop at 2% profit
    trailing_stop_distance_percent: float = 1.0  # Trail by 1%
    
    def validate_trade_risk(self, risk_percent: float, current_drawdown: float) -> tuple[bool, str]:
        """
        Validate if a trade meets risk requirements
        
        Returns:
            (allowed, reason)
        """
        # Check per-trade risk
        if risk_percent > self.max_risk_per_trade_percent:
            return False, f"Risk {risk_percent:.1f}% exceeds max {self.max_risk_per_trade_percent}%"
        
        # Check if adding this risk would exceed daily drawdown
        if current_drawdown + risk_percent > self.max_daily_drawdown_percent:
            return False, f"Would exceed daily drawdown limit of {self.max_daily_drawdown_percent}%"
        
        # Check total drawdown
        if current_drawdown >= self.max_total_drawdown_percent:
            return False, f"Total drawdown {current_drawdown:.1f}% at maximum limit"
        
        return True, "Risk approved"
    
    def calculate_position_size(
        self,
        account_balance: float,
        stop_loss_pips: float,
        pip_value: float,
        current_drawdown: float = 0.0
    ) -> float:
        """
        Calculate safe position size based on risk limits
        
        Args:
            account_balance: Current account balance
            stop_loss_pips: Stop loss distance in pips
            pip_value: Value per pip for the symbol
            current_drawdown: Current drawdown percentage
            
        Returns:
            Safe position size (lots)
        """
        # Adjust risk based on current drawdown
        risk_adjustment = 1.0
        if current_drawdown > 5.0:
            risk_adjustment = 0.5  # Halve risk if drawdown > 5%
        elif current_drawdown > 3.0:
            risk_adjustment = 0.75  # Reduce risk if drawdown > 3%
        
        # Calculate risk amount
        adjusted_risk = self.max_risk_per_trade_percent * risk_adjustment
        risk_amount = account_balance * (adjusted_risk / 100)
        
        # Calculate position size
        if stop_loss_pips > 0 and pip_value > 0:
            position_size = risk_amount / (stop_loss_pips * pip_value)
            
            # Apply maximum position size limit
            max_position_value = account_balance * (self.max_position_size_percent / 100)
            max_lots = max_position_value / (pip_value * 10000)  # Assuming standard lot
            
            return min(position_size, max_lots)
        
        return 0.0


@dataclass
class DrawdownTracker:
    """Track drawdowns and enforce limits"""
    
    starting_balance: float
    current_balance: float
    daily_starting_balance: float
    max_balance: float  # Track peak for drawdown calculation
    
    # Tracking
    consecutive_losses: int = 0
    daily_trades: int = 0
    symbol_trades: Dict[str, int] = None
    
    def __post_init__(self):
        if self.symbol_trades is None:
            self.symbol_trades = {}
    
    @property
    def current_drawdown_percent(self) -> float:
        """Calculate current drawdown from peak"""
        if self.max_balance <= 0:
            return 0.0
        return ((self.max_balance - self.current_balance) / self.max_balance) * 100
    
    @property
    def daily_drawdown_percent(self) -> float:
        """Calculate today's drawdown"""
        if self.daily_starting_balance <= 0:
            return 0.0
        return ((self.daily_starting_balance - self.current_balance) / self.daily_starting_balance) * 100
    
    def update_balance(self, new_balance: float):
        """Update balance and track peak"""
        self.current_balance = new_balance
        if new_balance > self.max_balance:
            self.max_balance = new_balance
    
    def can_trade(self, symbol: str, risk_limits: RiskLimits) -> tuple[bool, str]:
        """Check if trading is allowed based on limits"""
        
        # Check total drawdown
        if self.current_drawdown_percent >= risk_limits.max_total_drawdown_percent:
            return False, f"Max drawdown reached: {self.current_drawdown_percent:.1f}%"
        
        # Check daily drawdown
        if self.daily_drawdown_percent >= risk_limits.max_daily_drawdown_percent:
            return False, f"Daily drawdown limit reached: {self.daily_drawdown_percent:.1f}%"
        
        # Check consecutive losses
        if self.consecutive_losses >= risk_limits.max_consecutive_losses:
            return False, f"Consecutive loss limit reached: {self.consecutive_losses}"
        
        # Check daily trade limit
        if self.daily_trades >= risk_limits.max_trades_per_day:
            return False, f"Daily trade limit reached: {self.daily_trades}"
        
        # Check symbol trade limit
        symbol_count = self.symbol_trades.get(symbol, 0)
        if symbol_count >= risk_limits.max_trades_per_symbol_day:
            return False, f"Symbol daily limit reached for {symbol}: {symbol_count}"
        
        return True, "Trading allowed"
    
    def record_trade_result(self, symbol: str, profit: float):
        """Record trade result and update trackers"""
        self.daily_trades += 1
        self.symbol_trades[symbol] = self.symbol_trades.get(symbol, 0) + 1
        
        if profit < 0:
            self.consecutive_losses += 1
        else:
            self.consecutive_losses = 0
        
        self.update_balance(self.current_balance + profit)
    
    def reset_daily_stats(self):
        """Reset daily statistics"""
        self.daily_starting_balance = self.current_balance
        self.daily_trades = 0
        self.symbol_trades = {}


# Preset configurations
RISK_PROFILES = {
    "conservative": RiskLimits(
        max_risk_per_trade_percent=0.5,
        max_loss_per_trade_percent=3.0,
        max_daily_drawdown_percent=3.0,
        max_total_drawdown_percent=10.0,
        min_confidence_to_trade=90.0,
        min_risk_reward_ratio=3.0
    ),
    
    "moderate": RiskLimits(
        max_risk_per_trade_percent=1.0,
        max_loss_per_trade_percent=5.0,
        max_daily_drawdown_percent=5.0,
        max_total_drawdown_percent=10.0,
        min_confidence_to_trade=85.0,
        min_risk_reward_ratio=2.0
    ),
    
    "aggressive": RiskLimits(
        max_risk_per_trade_percent=1.5,
        max_loss_per_trade_percent=5.0,
        max_daily_drawdown_percent=7.0,
        max_total_drawdown_percent=15.0,
        min_confidence_to_trade=80.0,
        min_risk_reward_ratio=1.5
    ),
    
    "prop_firm": RiskLimits(
        # Strict limits for prop firm challenges
        max_risk_per_trade_percent=1.0,
        max_loss_per_trade_percent=5.0,
        max_daily_drawdown_percent=5.0,
        max_total_drawdown_percent=10.0,
        min_confidence_to_trade=85.0,
        min_risk_reward_ratio=2.0,
        max_trades_per_day=3,
        max_consecutive_losses=2
    )
}


def get_risk_limits(profile: str = "moderate") -> RiskLimits:
    """Get risk limits for a profile"""
    if profile not in RISK_PROFILES:
        logger.warning(f"Unknown risk profile {profile}, using moderate")
        profile = "moderate"
    
    limits = RISK_PROFILES[profile]
    logger.info(f"Using risk profile: {profile}")
    logger.info(f"  Max risk per trade: {limits.max_risk_per_trade_percent}%")
    logger.info(f"  Max daily drawdown: {limits.max_daily_drawdown_percent}%")
    logger.info(f"  Max total drawdown: {limits.max_total_drawdown_percent}%")
    
    return limits