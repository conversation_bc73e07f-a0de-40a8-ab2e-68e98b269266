@echo off
REM Fix MT5 Edge WebView permission issues
echo Fixing MT5 permissions...

REM Create temp directory with proper permissions
mkdir "C:\Program Files\MetaTrader 5\temp" 2>nul
mkdir "C:\Program Files\MetaTrader 5\temp\EBWebView" 2>nul

REM Grant full permissions to the temp directory
icacls "C:\Program Files\MetaTrader 5\temp" /grant %USERNAME%:F /T
icacls "C:\Program Files\MetaTrader 5\temp" /grant Users:F /T

echo.
echo Permissions fixed. You may need to run this as Administrator.
echo.

REM Alternative: Create temp directory in user profile
set MT5_TEMP=%USERPROFILE%\AppData\Local\MetaTrader5\temp
mkdir "%MT5_TEMP%" 2>nul
mkdir "%MT5_TEMP%\EBWebView" 2>nul

echo Alternative temp directory created at: %MT5_TEMP%
echo.
pause